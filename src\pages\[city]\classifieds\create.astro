---
import Layout from '../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../components/MinimalAcctButton.astro';
import { getCityBySlug } from '../../../lib/database';
import { getCurrencyForCountry } from '../../../lib/currencies';

const { city: citySlug } = Astro.params;
const category = Astro.url.searchParams.get('category');

if (!citySlug) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get currency information for this city's country
const localCurrency = getCurrencyForCountry(city.country);

const pageTitle = `Post Classified Ad - ${city.name}`;
const pageDescription = `Post your classified ad in ${city.name}. Connect with the expat community.`;
---

<Layout title={pageTitle} description={pageDescription}>
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50">
    <!-- Header with Breadcrumb -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <nav class="flex items-center space-x-2 text-sm text-slate-600 mb-4">
          <a href="/" class="hover:text-blue-600 transition-colors">🌎 Main</a>
          <span class="text-slate-400">›</span>
          <a href={`/${citySlug}`} class="hover:text-blue-600 transition-colors">{city.name}</a>
          <span class="text-slate-400">›</span>
          <a href={`/${citySlug}/classifieds`} class="hover:text-blue-600 transition-colors">Classifieds</a>
          <span class="text-slate-400">›</span>
          <span class="text-slate-900 font-medium">Post Ad</span>
        </nav>

        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
            <span class="text-2xl">📝</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-slate-900">Post Classified Ad</h1>
            <p class="text-slate-600">Share with the expat community</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="bg-white border border-slate-200 rounded-xl overflow-hidden">
          <div class="p-4 sm:p-6 lg:p-8">
            <!-- Auth Check Notice (hidden by default to prevent flicker) -->
            <div id="auth-notice" class="mb-8" style="display: none;">
              <div class="bg-white border border-slate-200 rounded-xl p-6 text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl">🔐</span>
                </div>
                <h3 class="text-lg font-bold text-slate-900 mb-2">Account Required</h3>
                <p class="text-slate-600 mb-6">Please sign in or create an account to post classified ads</p>
                <button
                  onclick="window.authFunctions?.showAuthModal()"
                  class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center"
                >
                  <span class="mr-2">👤</span>
                  Sign In / Create Account
                </button>
              </div>
            </div>

            <!-- Category-Specific Forms (visible by default, hidden only if not authenticated) -->
            <div id="classified-forms" style="display: block;">

              <!-- Housing Form -->
              <form id="housing-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">🏠</span>
                  <h2 class="text-lg font-semibold text-slate-900">Housing Details</h2>
                </div>

                <!-- Listing Type -->
                <div>
                  <label for="listing_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Listing Type *
                  </label>
                  <select
                    id="listing_type"
                    name="listing_type"
                    required
                    onchange="updateHousingFormFields(this.value)"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select listing type</option>
                    <option value="rent">🏠 For Rent</option>
                    <option value="sale">🏡 For Sale</option>
                    <option value="roommate">👥 Roommate Wanted</option>
                  </select>
                </div>

                <!-- Housing Type -->
                <div>
                  <label for="housing_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Property Type *
                  </label>
                  <select
                    id="housing_type"
                    name="housing_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select property type</option>
                    <option value="apartment">Apartment</option>
                    <option value="house">House</option>
                    <option value="room">Room</option>
                    <option value="studio">Studio</option>
                    <option value="condo">Condo</option>
                    <option value="villa">Villa</option>
                  </select>
                </div>

                <!-- Title -->
                <div>
                  <label for="housing_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    id="housing_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., Beautiful 2BR apartment near beach"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="housing_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    id="housing_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the property, amenities, location, etc..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Housing Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label for="bedrooms" class="block text-sm font-medium text-slate-700 mb-2">
                      Bedrooms
                    </label>
                    <select
                      id="bedrooms"
                      name="bedrooms"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="0">Studio</option>
                      <option value="1">1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4+</option>
                    </select>
                  </div>
                  <div>
                    <label for="bathrooms" class="block text-sm font-medium text-slate-700 mb-2">
                      Bathrooms
                    </label>
                    <select
                      id="bathrooms"
                      name="bathrooms"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="1">1</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2</option>
                      <option value="2.5">2.5</option>
                      <option value="3">3+</option>
                    </select>
                  </div>
                  <div>
                    <label for="furnished" class="block text-sm font-medium text-slate-700 mb-2">
                      Furnished
                    </label>
                    <select
                      id="furnished"
                      name="furnished"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="yes">Yes</option>
                      <option value="no">No</option>
                      <option value="partial">Partially</option>
                    </select>
                  </div>
                </div>

                <!-- Dual Currency Price -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    <span id="housing_price_label">Price *</span>
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="housing_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="housing_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p id="housing_price_help" class="text-xs text-slate-500 mt-2">Enter price in either currency (at least one required)</p>
                </div>

                <!-- Enhanced Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span class="text-xl">📸</span>
                      <h3 class="text-lg font-semibold text-slate-900">Add Photos</h3>
                      <span class="text-sm text-slate-500">(Up to 3 images)</span>
                    </div>
                    <div class="text-xs text-slate-400">
                      <span id="image_count_housing">0</span>/3 uploaded
                    </div>
                  </div>

                  <!-- Upload Guidelines -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start space-x-3">
                      <span class="text-blue-500 text-lg">💡</span>
                      <div class="text-sm text-blue-800">
                        <p class="font-medium mb-2">Photo Guidelines for Best Results:</p>
                        <ul class="space-y-1 text-xs">
                          <li>• <strong>Max 3 photos</strong> - Show different angles or key features</li>
                          <li>• <strong>Size limit:</strong> 2MB per image</li>
                          <li>• <strong>Best dimensions:</strong> 1200×900px or 800×600px (4:3 ratio)</li>
                          <li>• <strong>Formats:</strong> JPEG, PNG, or WebP</li>
                          <li>• <strong>Tip:</strong> Good lighting and clear focus get more responses!</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- Upload Area -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input
                      type="file"
                      id="image_upload_housing"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      multiple
                      class="hidden"
                    />
                    <label for="image_upload_housing" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Click to upload photos</div>
                        <div class="text-xs text-slate-500">or drag and drop images here</div>
                      </div>
                    </label>
                  </div>

                  <!-- Image Previews -->
                  <div id="image_previews_housing" class="grid grid-cols-3 gap-4 mt-4 hidden">
                    <!-- Preview images will be inserted here -->
                  </div>

                  <!-- Upload Status -->
                  <div id="upload_status_housing" class="hidden mt-3"></div>

                  <!-- Error Messages -->
                  <div id="upload_errors_housing" class="hidden mt-3"></div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <!-- Contact Information Section - Compact Design -->
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method <span class="text-sm font-normal text-slate-600">(provide at least one)</span></h3>

                    <!-- Contact Methods Grid -->
                    <div id="standard-contact-methods" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="housing_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('housing', 'email')"
                            required
                          />
                          <label for="housing_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="housing_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="housing_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="housing_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('housing', 'phone')"
                          />
                          <label for="housing_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="housing_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="housing_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          oninput="handlePhoneInput(this, 'housing')"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="housing_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('housing', 'whatsapp')"
                          />
                          <label for="housing_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="housing_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <div class="space-y-2">
                          <input
                            type="tel"
                            id="housing_contact_whatsapp"
                            name="contact_whatsapp"
                            placeholder="+52 ************"
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                          <label class="flex items-center space-x-2 text-xs text-slate-600">
                            <input
                              type="checkbox"
                              id="housing_whatsapp_same_as_phone"
                              class="w-3 h-3 text-emerald-600 rounded focus:ring-emerald-500"
                              onchange="handleWhatsAppCheckbox(this, 'housing')"
                            />
                            <span>Same as phone number</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a
                      href={`/${citySlug}/classifieds`}
                      class="text-slate-600 hover:text-slate-800 font-medium"
                    >
                      ← Back to Classifieds
                    </a>

                    <button
                      type="submit"
                      class="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center"
                    >
                      <span class="mr-2">📝</span>
                      Post Ad
                    </button>
                  </div>
                </div>
              </form>

              <!-- Jobs Form -->
              <form id="jobs-form" class="space-y-6" style="display: none;">
                <!-- Progress Indicator -->
                <div class="mb-8">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span class="text-xl">💼</span>
                      <h2 class="text-lg font-semibold text-slate-900">Jobs & Services</h2>
                    </div>
                    <div class="text-sm text-slate-500">
                      Step <span id="jobs-current-step">1</span> of 3
                    </div>
                  </div>

                  <!-- Progress Bar -->
                  <div class="w-full bg-slate-200 rounded-full h-2">
                    <div id="jobs-progress-bar" class="bg-emerald-600 h-2 rounded-full transition-all duration-300" style="width: 33%"></div>
                  </div>
                </div>

                <!-- Step 1: Listing Type Selection -->
                <div id="jobs-step-1" class="jobs-step">
                  <div class="text-center mb-8">
                    <h3 class="text-xl font-bold text-slate-900 mb-2">Which best describes your ad?</h3>
                    <p class="text-slate-600">Select one option below to get started</p>
                  </div>

                  <div class="space-y-4">
                    <!-- Offering Services -->
                    <label class="block cursor-pointer">
                      <input type="radio" name="job_listing_type" value="offering_services" class="sr-only" onchange="selectJobType('offering_services')">
                      <div class="border-2 border-slate-200 rounded-xl p-6 hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200 job-type-card">
                        <div class="flex items-start space-x-4">
                          <div class="text-3xl">🛠️</div>
                          <div class="flex-1">
                            <h4 class="text-lg font-semibold text-slate-900 mb-2">Offering My Services</h4>
                            <p class="text-slate-600 text-sm">You're advertising your own skill or service (e.g., massage, teaching, freelance work, consulting).</p>
                          </div>
                        </div>
                      </div>
                    </label>

                    <!-- Looking to Hire -->
                    <label class="block cursor-pointer">
                      <input type="radio" name="job_listing_type" value="looking_to_hire" class="sr-only" onchange="selectJobType('looking_to_hire')">
                      <div class="border-2 border-slate-200 rounded-xl p-6 hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200 job-type-card">
                        <div class="flex items-start space-x-4">
                          <div class="text-3xl">🔍</div>
                          <div class="flex-1">
                            <h4 class="text-lg font-semibold text-slate-900 mb-2">Looking to Hire / Need Help</h4>
                            <p class="text-slate-600 text-sm">You're searching for someone to do a task or job (e.g., cleaner, tutor, mover, assistant).</p>
                          </div>
                        </div>
                      </div>
                    </label>

                    <!-- Looking for Work -->
                    <label class="block cursor-pointer">
                      <input type="radio" name="job_listing_type" value="looking_for_work" class="sr-only" onchange="selectJobType('looking_for_work')">
                      <div class="border-2 border-slate-200 rounded-xl p-6 hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200 job-type-card">
                        <div class="flex items-start space-x-4">
                          <div class="text-3xl">💼</div>
                          <div class="flex-1">
                            <h4 class="text-lg font-semibold text-slate-900 mb-2">Looking for Work / Gigs</h4>
                            <p class="text-slate-600 text-sm">You're looking for short-term jobs or gigs you can do (e.g., pet sitting, cooking, handyman work).</p>
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>

                  <div class="flex justify-end mt-8">
                    <button type="button" id="jobs-step-1-next" class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 sm:px-8 sm:py-4 rounded-lg font-semibold transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto" disabled onclick="nextJobStep(2)">
                      Continue →
                    </button>
                  </div>
                </div>

                <!-- Step 2: Job Details -->
                <div id="jobs-step-2" class="jobs-step" style="display: none;">
                  <div class="text-center mb-6">
                    <h3 id="jobs-step-2-title" class="text-xl font-bold text-slate-900 mb-2">Tell us about your service</h3>
                    <p id="jobs-step-2-subtitle" class="text-slate-600">Provide details to attract the right clients</p>
                  </div>

                  <!-- Dynamic Title Field -->
                  <div class="mb-6">
                    <label for="job_title" class="block text-sm font-medium text-slate-700 mb-2">
                      <span id="jobs-title-label">What service are you offering?</span> *
                    </label>
                    <input
                      type="text"
                      id="job_title"
                      name="title"
                      required
                      maxlength="100"
                      placeholder="e.g., English Tutoring, Massage Therapy, Web Development"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>

                  <!-- Service Category -->
                  <div class="mb-6">
                    <label for="job_category" class="block text-sm font-medium text-slate-700 mb-2">
                      Service Category *
                    </label>
                    <select
                      id="job_category"
                      name="job_category"
                      required
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select category</option>
                      <option value="tutoring-education">📚 Tutoring & Education</option>
                      <option value="health-wellness">💆 Health & Wellness</option>
                      <option value="cleaning-maintenance">🧹 Cleaning & Maintenance</option>
                      <option value="tech-digital">💻 Tech & Digital Services</option>
                      <option value="creative-design">🎨 Creative & Design</option>
                      <option value="business-consulting">💼 Business & Consulting</option>
                      <option value="home-services">🏠 Home Services</option>
                      <option value="transportation">🚗 Transportation</option>
                      <option value="events-entertainment">🎉 Events & Entertainment</option>
                      <option value="other">🔧 Other Services</option>
                    </select>
                  </div>

                  <!-- Description -->
                  <div class="mb-6">
                    <label for="job_description" class="block text-sm font-medium text-slate-700 mb-2">
                      <span id="jobs-description-label">Service Description</span> *
                    </label>
                    <textarea
                      id="job_description"
                      name="description"
                      required
                      rows="4"
                      maxlength="2000"
                      placeholder="Describe your service, experience, qualifications, and what makes you unique..."
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    ></textarea>
                    <div class="text-xs text-slate-500 mt-1">
                      <span id="description-count">0</span>/2000 characters
                    </div>
                  </div>

                  <!-- Navigation -->
                  <div class="flex flex-col sm:flex-row justify-between gap-3 mt-8">
                    <button type="button" class="text-slate-600 hover:text-slate-800 font-medium px-4 py-2 order-2 sm:order-1" onclick="previousJobStep(1)">
                      ← Back
                    </button>
                    <button type="button" id="jobs-step-2-next" class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 sm:px-8 sm:py-4 rounded-lg font-semibold transition-colors duration-200 w-full sm:w-auto order-1 sm:order-2" onclick="nextJobStep(3)">
                      Continue →
                    </button>
                  </div>
                </div>

                <!-- Step 3: Additional Details -->
                <div id="jobs-step-3" class="jobs-step" style="display: none;">
                  <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-slate-900 mb-2">Final Details</h3>
                    <p class="text-slate-600">Add pricing, availability, and contact information</p>
                  </div>

                  <!-- Language Preference -->
                  <div class="mb-6">
                    <label for="preferred_languages" class="block text-sm font-medium text-slate-700 mb-2">
                      Preferred Language(s) <span class="text-slate-500">(Optional)</span>
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="languages" value="english" class="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500">
                        <span class="text-sm">🇺🇸 English</span>
                      </label>
                      <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="languages" value="spanish" class="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500">
                        <span class="text-sm">🇪🇸 Spanish</span>
                      </label>
                      <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="languages" value="french" class="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500">
                        <span class="text-sm">🇫🇷 French</span>
                      </label>
                      <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="languages" value="german" class="w-4 h-4 text-emerald-600 rounded focus:ring-emerald-500">
                        <span class="text-sm">🇩🇪 German</span>
                      </label>
                    </div>
                  </div>

                  <!-- Duration/Frequency -->
                  <div class="mb-6">
                    <label for="duration_type" class="block text-sm font-medium text-slate-700 mb-2">
                      Duration Type <span class="text-slate-500">(Optional)</span>
                    </label>
                    <select
                      id="duration_type"
                      name="duration_type"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select duration</option>
                      <option value="one-time">One-time job</option>
                      <option value="temporary">Temporary (few weeks/months)</option>
                      <option value="ongoing">Ongoing/Regular</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>

                  <!-- Availability -->
                  <div id="availability-section" class="mb-6">
                    <label for="availability" class="block text-sm font-medium text-slate-700 mb-2">
                      <span id="availability-label">Available Times / Days</span> <span class="text-slate-500">(Optional)</span>
                    </label>
                    <textarea
                      id="availability"
                      name="availability"
                      rows="2"
                      maxlength="500"
                      placeholder="e.g., Weekdays 9am-5pm, Weekends available, Flexible schedule"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    ></textarea>
                  </div>

                  <!-- Enhanced Pricing with Rate Types -->
                  <div class="mb-6">
                    <label class="block text-sm font-medium text-slate-700 mb-3">
                      <span id="job_salary_label">Rate (Optional)</span>
                    </label>

                    <!-- Rate Type Selection -->
                    <div class="mb-4">
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        Rate Type
                      </label>
                      <select
                        id="rate_type"
                        name="rate_type"
                        class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        onchange="updateRateLabels()"
                      >
                        <option value="">Select rate type</option>
                        <option value="hourly">💰 Per Hour</option>
                        <option value="daily">📅 Per Day</option>
                        <option value="weekly">📆 Per Week</option>
                        <option value="monthly">🗓️ Per Month</option>
                        <option value="project">📋 Per Project</option>
                        <option value="negotiable">🤝 Negotiable</option>
                      </select>
                    </div>

                    <!-- Rate Amount -->
                    <div id="rate-amount-section" class="grid grid-cols-1 md:grid-cols-2 gap-4" style="display: none;">
                      <!-- Local Currency -->
                      <div>
                        <label class="block text-xs font-medium text-slate-600 mb-2">
                          <span id="rate-local-label">{localCurrency.name} ({localCurrency.code})</span>
                        </label>
                        <div class="relative">
                          <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                          <input
                            type="number"
                            id="job_salary_local"
                            name="price_local"
                            min="0"
                            step="1"
                            placeholder="0"
                            class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                        </div>
                      </div>

                      <!-- USD -->
                      <div>
                        <label class="block text-xs font-medium text-slate-600 mb-2">
                          <span id="rate-usd-label">US Dollars (USD)</span>
                        </label>
                        <div class="relative">
                          <span class="absolute left-3 top-3 text-slate-500">$</span>
                          <input
                            type="number"
                            id="job_salary_usd"
                            name="price_usd"
                            min="0"
                            step="0.01"
                            placeholder="0.00"
                            class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                        </div>
                      </div>
                    </div>

                    <p id="job_salary_help" class="text-xs text-slate-500 mt-2">Select rate type first, then enter amount (optional)</p>
                  </div>

                  <!-- CV/Portfolio Upload (for job seekers) -->
                  <div id="cv-upload-section" class="mb-6" style="display: none;">
                    <label class="block text-sm font-medium text-slate-700 mb-2">
                      Upload CV or Portfolio <span class="text-slate-500">(Optional)</span>
                    </label>
                    <div class="border-2 border-dashed border-slate-300 rounded-lg p-4 text-center hover:border-emerald-400 transition-colors">
                      <input
                        type="file"
                        id="cv_upload"
                        accept=".pdf,.doc,.docx"
                        class="hidden"
                      />
                      <label for="cv_upload" class="cursor-pointer">
                        <div class="space-y-2">
                          <div class="text-2xl text-slate-400">📄</div>
                          <div class="text-sm font-medium text-slate-700">Click to upload CV/Portfolio</div>
                          <div class="text-xs text-slate-500">PDF, DOC, or DOCX (max 5MB)</div>
                        </div>
                      </label>
                    </div>
                    <div id="cv-preview" class="mt-3 hidden">
                      <!-- CV preview will be shown here -->
                    </div>
                  </div>

                  <!-- Enhanced Image Upload -->
                  <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                      <div class="flex items-center space-x-3">
                        <span class="text-xl">📸</span>
                        <h4 class="text-lg font-semibold text-slate-900">Add Photos <span class="text-sm font-normal text-slate-500">(Optional)</span></h4>
                        <span class="text-sm text-slate-500">(Up to 3 images)</span>
                      </div>
                      <div class="text-xs text-slate-400">
                        <span id="image_count_jobs">0</span>/3 uploaded
                      </div>
                    </div>

                    <!-- Upload Area -->
                    <div class="border-2 border-dashed border-slate-300 rounded-lg p-4 text-center hover:border-emerald-400 transition-colors">
                      <input
                        type="file"
                        id="image_upload_jobs"
                        accept="image/jpeg,image/jpg,image/png,image/webp"
                        multiple
                        class="hidden"
                      />
                      <label for="image_upload_jobs" class="cursor-pointer">
                        <div class="space-y-2">
                          <div class="text-3xl text-slate-400">📷</div>
                          <div class="text-sm font-medium text-slate-700">Click to upload photos</div>
                          <div class="text-xs text-slate-500">Show your work, portfolio, or team (optional)</div>
                        </div>
                      </label>
                    </div>

                    <!-- Image Previews with Thumbnail Selection -->
                    <div id="image_previews_jobs" class="grid grid-cols-3 gap-4 mt-4 hidden">
                      <!-- Preview images will be inserted here -->
                    </div>

                    <!-- Thumbnail Selection -->
                    <div id="thumbnail_selection_jobs" class="mt-4 hidden">
                      <label class="block text-sm font-medium text-slate-700 mb-2">
                        Select Thumbnail for Listings <span class="text-slate-500">(Optional)</span>
                      </label>
                      <p class="text-xs text-slate-500 mb-3">Choose which image will be shown on the main classifieds page</p>
                      <div id="thumbnail_options_jobs" class="flex gap-2">
                        <!-- Thumbnail options will be inserted here -->
                      </div>
                    </div>

                    <!-- Upload Status -->
                    <div id="upload_status_jobs" class="hidden mt-3"></div>

                    <!-- Error Messages -->
                    <div id="upload_errors_jobs" class="hidden mt-3"></div>
                  </div>

                  <!-- Contact Information -->
                  <div class="border-t border-slate-200 pt-6">
                    <h4 class="text-lg font-semibold text-slate-900 mb-4">⭐ Preferred Contact Method <span class="text-sm font-normal text-slate-600">(provide at least one)</span></h4>

                    <!-- Contact Methods Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="jobs_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('jobs', 'email')"
                            required
                          />
                          <label for="jobs_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="jobs_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="jobs_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="jobs_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('jobs', 'phone')"
                          />
                          <label for="jobs_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="jobs_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="jobs_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          oninput="handlePhoneInput(this, 'jobs')"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="jobs_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('jobs', 'whatsapp')"
                          />
                          <label for="jobs_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="jobs_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <div class="space-y-2">
                          <input
                            type="tel"
                            id="jobs_contact_whatsapp"
                            name="contact_whatsapp"
                            placeholder="+52 ************"
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                          <label class="flex items-center space-x-2 text-xs text-slate-600">
                            <input
                              type="checkbox"
                              id="jobs_whatsapp_same_as_phone"
                              class="w-3 h-3 text-emerald-600 rounded focus:ring-emerald-500"
                              onchange="handleWhatsAppCheckbox(this, 'jobs')"
                            />
                            <span>Same as phone number</span>
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex flex-col sm:flex-row justify-between gap-3">
                      <button type="button" class="text-slate-600 hover:text-slate-800 font-medium px-4 py-2 order-2 sm:order-1" onclick="previousJobStep(2)">
                        ← Back
                      </button>
                      <button
                        type="submit"
                        class="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 sm:px-10 sm:py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center w-full sm:w-auto order-1 sm:order-2"
                      >
                        <span class="mr-2">📝</span>
                        Post Job/Service
                      </button>
                    </div>
                  </div>
                </div>
              </form>

              <!-- Buy & Sell Form -->
              <form id="buy-sell-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">🛍️</span>
                  <h2 class="text-lg font-semibold text-slate-900">Buy & Sell Details</h2>
                </div>

                <!-- Listing Type -->
                <div>
                  <label for="buy_sell_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Listing Type *
                  </label>
                  <select
                    id="buy_sell_type"
                    name="buy_sell_type"
                    required
                    onchange="updateBuySellFormFields(this.value)"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select listing type</option>
                    <option value="sell">💰 For Sale</option>
                    <option value="buy">🔍 Want to Buy</option>
                  </select>
                </div>

                <!-- Item Category -->
                <div>
                  <label for="item_category" class="block text-sm font-medium text-slate-700 mb-2">
                    Item Category *
                  </label>
                  <select
                    id="item_category"
                    name="item_category"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select category</option>
                    <option value="furniture">Furniture</option>
                    <option value="electronics">Electronics</option>
                    <option value="vehicles">Vehicles</option>
                    <option value="appliances">Appliances</option>
                    <option value="clothing">Clothing</option>
                    <option value="books">Books</option>
                    <option value="sports">Sports & Recreation</option>
                    <option value="tools">Tools</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <!-- Title -->
                <div>
                  <label for="item_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Item Title *
                  </label>
                  <input
                    type="text"
                    id="item_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., iPhone 14, Dining Table, Honda Civic"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="item_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    id="item_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the item condition, features, reason for selling..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Item Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="condition" class="block text-sm font-medium text-slate-700 mb-2">
                      Condition *
                    </label>
                    <select
                      id="condition"
                      name="condition"
                      required
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select condition</option>
                      <option value="new">New</option>
                      <option value="like-new">Like New</option>
                      <option value="good">Good</option>
                      <option value="fair">Fair</option>
                      <option value="poor">Poor</option>
                    </select>
                  </div>
                  <div>
                    <label for="brand" class="block text-sm font-medium text-slate-700 mb-2">
                      Brand (Optional)
                    </label>
                    <input
                      type="text"
                      id="brand"
                      name="brand"
                      placeholder="e.g., Apple, IKEA, Honda"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                </div>

                <!-- Dual Currency Price -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    <span id="buy_sell_price_label">Price *</span>
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="buy_sell_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="buy_sell_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p id="buy_sell_price_help" class="text-xs text-slate-500 mt-2">Enter price in either currency (at least one required)</p>
                </div>

                <!-- Enhanced Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span class="text-xl">📸</span>
                      <h3 class="text-lg font-semibold text-slate-900">Add Photos</h3>
                      <span class="text-sm text-slate-500">(Up to 3 images)</span>
                    </div>
                    <div class="text-xs text-slate-400">
                      <span id="image_count_buy_sell">0</span>/3 uploaded
                    </div>
                  </div>

                  <!-- Upload Guidelines -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start space-x-3">
                      <span class="text-blue-500 text-lg">💡</span>
                      <div class="text-sm text-blue-800">
                        <p class="font-medium mb-2">Photo Guidelines for Best Results:</p>
                        <ul class="space-y-1 text-xs">
                          <li>• <strong>Max 3 photos</strong> - Show different angles and details</li>
                          <li>• <strong>Size limit:</strong> 2MB per image</li>
                          <li>• <strong>Best dimensions:</strong> 1200×900px or 800×600px (4:3 ratio)</li>
                          <li>• <strong>Formats:</strong> JPEG, PNG, or WebP</li>
                          <li>• <strong>Tip:</strong> Good lighting and clear focus get more responses!</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- Upload Area -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input
                      type="file"
                      id="image_upload_buy_sell"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      multiple
                      class="hidden"
                    />
                    <label for="image_upload_buy_sell" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Click to upload photos</div>
                        <div class="text-xs text-slate-500">or drag and drop images here</div>
                      </div>
                    </label>
                  </div>

                  <!-- Image Previews -->
                  <div id="image_previews_buy_sell" class="grid grid-cols-3 gap-4 mt-4 hidden">
                    <!-- Preview images will be inserted here -->
                  </div>

                  <!-- Upload Status -->
                  <div id="upload_status_buy_sell" class="hidden mt-3"></div>

                  <!-- Error Messages -->
                  <div id="upload_errors_buy_sell" class="hidden mt-3"></div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <!-- Contact Information Section - Compact Design -->
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method <span class="text-sm font-normal text-slate-600">(provide at least one)</span></h3>

                    <!-- Contact Methods Grid -->
                    <div id="standard-contact-methods" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="buy_sell_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('buy_sell', 'email')"
                            required
                          />
                          <label for="buy_sell_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="buy_sell_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="buy_sell_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="buy_sell_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('buy_sell', 'phone')"
                          />
                          <label for="buy_sell_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="buy_sell_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="buy_sell_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          oninput="handlePhoneInput(this, 'buy-sell')"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="buy_sell_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('buy_sell', 'whatsapp')"
                          />
                          <label for="buy_sell_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="buy_sell_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <div class="space-y-2">
                          <input
                            type="tel"
                            id="buy_sell_contact_whatsapp"
                            name="contact_whatsapp"
                            placeholder="+52 ************"
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                          <label class="flex items-center space-x-2 text-xs text-slate-600">
                            <input
                              type="checkbox"
                              id="buy_sell_whatsapp_same_as_phone"
                              class="w-3 h-3 text-emerald-600 rounded focus:ring-emerald-500"
                              onchange="handleWhatsAppCheckbox(this, 'buy-sell')"
                            />
                            <span>Same as phone number</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a
                      href={`/${citySlug}/classifieds`}
                      class="text-slate-600 hover:text-slate-800 font-medium"
                    >
                      ← Back to Classifieds
                    </a>

                    <button
                      type="submit"
                      class="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center"
                    >
                      <span class="mr-2">📝</span>
                      Post Ad
                    </button>
                  </div>
                </div>
              </form>

              <!-- Community & Events Form -->
              <form id="community-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">💬</span>
                  <h2 class="text-lg font-semibold text-slate-900">Event Details</h2>
                </div>

                <!-- Event Type -->
                <div>
                  <label for="event_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Type *
                  </label>
                  <select
                    id="event_type"
                    name="event_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    onchange="handleEventTypeChange(this.value)"
                  >
                    <option value="">Select type</option>
                    <option value="social">🎉 Social Event - Parties, gatherings, celebrations</option>
                    <option value="meetup">👥 Meetup - Casual get-togethers, networking</option>
                    <option value="class">📚 Class/Workshop - Learning, skills, education</option>
                    <option value="announcement">📢 Announcement - Important community news</option>
                    <option value="useful-links">🔗 Useful Links - Facebook groups, Telegram, WhatsApp groups</option>
                    <option value="other">📝 Other - Everything else</option>
                  </select>
                  <p class="text-xs text-slate-500 mt-1">Choose the category that best describes your post</p>
                </div>

                <!-- Title -->
                <div>
                  <label for="community_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    id="community_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., Beach Cleanup, Spanish Class, Lost Cat"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="community_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    id="community_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Provide details about the event, meetup, or announcement..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Contact Information - Moved Up for Better UX -->
                <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                  <h3 class="text-lg font-semibold text-emerald-900 mb-4 flex items-center">
                    ⭐ Contact Information
                    <span class="ml-2 text-sm font-normal text-emerald-700">(Required - Choose your preferred method)</span>
                  </h3>

                  <!-- Standard Contact Methods -->
                  <div id="standard-contact-methods">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="community_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('community', 'email')"
                            required
                          />
                          <label for="community_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="community_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="community_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="community_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('community', 'phone')"
                          />
                          <label for="community_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="community_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="community_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          oninput="handlePhoneInput(this, 'community')"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="community_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            onchange="updatePreferredStar('community', 'whatsapp')"
                          />
                          <label for="community_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="community_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <div class="space-y-2">
                          <input
                            type="tel"
                            id="community_contact_whatsapp"
                            name="contact_whatsapp"
                            placeholder="+52 ************"
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                          <label class="flex items-center space-x-2 text-xs text-slate-600">
                            <input
                              type="checkbox"
                              id="community_whatsapp_same_as_phone"
                              class="w-3 h-3 text-emerald-600 rounded focus:ring-emerald-500"
                              onchange="handleWhatsAppCheckbox(this, 'community')"
                            />
                            <span>Same as phone number</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Contact Methods for Useful Links -->
                  <div id="useful-links-contact-methods" class="space-y-4" style="display: none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 class="font-medium text-blue-900 mb-3 flex items-center">
                        🔗 Community Links & Groups
                        <span class="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Share useful community resources</span>
                      </h4>
                      
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Facebook Group/Page -->
                        <div class="space-y-2">
                          <label for="community_facebook_link" class="text-sm font-medium text-slate-700 flex items-center">
                            📘 Facebook Group/Page
                          </label>
                          <input
                            type="url"
                            id="community_facebook_link"
                            name="facebook_link"
                            placeholder="https://facebook.com/groups/..."
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p class="text-xs text-slate-500">Link to Facebook group or page</p>
                        </div>

                        <!-- Telegram Group/Channel -->
                        <div class="space-y-2">
                          <label for="community_telegram_link" class="text-sm font-medium text-slate-700 flex items-center">
                            ✈️ Telegram Group/Channel
                          </label>
                          <input
                            type="url"
                            id="community_telegram_link"
                            name="telegram_link"
                            placeholder="https://t.me/..."
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p class="text-xs text-slate-500">Link to Telegram group or channel</p>
                        </div>

                        <!-- WhatsApp Group -->
                        <div class="space-y-2">
                          <label for="community_whatsapp_group_link" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp Group
                          </label>
                          <input
                            type="url"
                            id="community_whatsapp_group_link"
                            name="whatsapp_group_link"
                            placeholder="https://chat.whatsapp.com/..."
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p class="text-xs text-slate-500">WhatsApp group invite link</p>
                        </div>

                        <!-- Website/Other Link -->
                        <div class="space-y-2">
                          <label for="community_website_link" class="text-sm font-medium text-slate-700 flex items-center">
                            🌐 Website/Other Link
                          </label>
                          <input
                            type="url"
                            id="community_website_link"
                            name="website_link"
                            placeholder="https://..."
                            class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p class="text-xs text-slate-500">Any other useful website or resource</p>
                        </div>
                      </div>


                    </div>
                  </div>
                </div>

                <!-- Event Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="event_date" class="block text-sm font-medium text-slate-700 mb-2">
                      Date (Optional)
                    </label>
                    <input
                      type="date"
                      id="event_date"
                      name="event_date"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                  <div>
                    <label for="event_time" class="block text-sm font-medium text-slate-700 mb-2">
                      Time (Optional)
                    </label>
                    <input
                      type="time"
                      id="event_time"
                      name="event_time"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                </div>

                <!-- Location -->
                <div>
                  <label for="event_location" class="block text-sm font-medium text-slate-700 mb-2">
                    Location (Optional)
                  </label>
                  <input
                    type="text"
                    id="event_location"
                    name="event_location"
                    placeholder="e.g., Central Park, Community Center, Online"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Dual Currency Cost -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    Cost (Optional)
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="community_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="community_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-500 mt-2">Enter cost in either currency (leave blank for free events)</p>
                </div>

                <!-- Enhanced Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span class="text-xl">📸</span>
                      <h3 class="text-lg font-semibold text-slate-900">Add Photos</h3>
                      <span class="text-sm text-slate-500">(Up to 3 images)</span>
                    </div>
                    <div class="text-xs text-slate-400">
                      <span id="image_count_community">0</span>/3 uploaded
                    </div>
                  </div>

                  <!-- Upload Guidelines -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start space-x-3">
                      <span class="text-blue-500 text-lg">💡</span>
                      <div class="text-sm text-blue-800">
                        <p class="font-medium mb-2">Photo Guidelines for Best Results:</p>
                        <ul class="space-y-1 text-xs">
                          <li>• <strong>Max 3 photos</strong> - Show event details or location</li>
                          <li>• <strong>Size limit:</strong> 2MB per image</li>
                          <li>• <strong>Best dimensions:</strong> 1200×900px or 800×600px (4:3 ratio)</li>
                          <li>• <strong>Formats:</strong> JPEG, PNG, or WebP</li>
                          <li>• <strong>Tip:</strong> Good lighting and clear focus get more responses!</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- Upload Area -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input
                      type="file"
                      id="image_upload_community"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      multiple
                      class="hidden"
                    />
                    <label for="image_upload_community" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Click to upload photos</div>
                        <div class="text-xs text-slate-500">or drag and drop images here</div>
                      </div>
                    </label>
                  </div>

                  <!-- Image Previews -->
                  <div id="image_previews_community" class="grid grid-cols-3 gap-4 mt-4 hidden">
                    <!-- Preview images will be inserted here -->
                  </div>

                  <!-- Upload Status -->
                  <div id="upload_status_community" class="hidden mt-3"></div>

                  <!-- Error Messages -->
                  <div id="upload_errors_community" class="hidden mt-3"></div>
                </div>



                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a
                      href={`/${citySlug}/classifieds`}
                      class="text-slate-600 hover:text-slate-800 font-medium"
                    >
                      ← Back to Classifieds
                    </a>

                    <button
                      type="submit"
                      class="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center"
                    >
                      <span class="mr-2">📝</span>
                      Post Ad
                    </button>
                  </div>
                </div>
              </form>



            </div>


          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug, category, localCurrency }}>
  // Get category from URL or default to housing
  const urlParams = new URLSearchParams(window.location.search);
  const selectedCategory = urlParams.get('category') || category || 'housing';

  // Check authentication status and show appropriate form
  function checkAuthAndShowForm() {
    if (!window.authFunctions) {
      setTimeout(checkAuthAndShowForm, 100);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();
    const userProfile = window.authFunctions.getUserProfile();
    const authNotice = document.getElementById('auth-notice');
    const formsContainer = document.getElementById('classified-forms');


    console.log('Auth check - currentUser:', currentUser, 'userProfile:', userProfile);
    console.log('Form elements found:', {
      authNotice: !!authNotice,
      formsContainer: !!formsContainer
    });

    if (currentUser) {
      console.log('User is logged in, showing form');
      if (authNotice) {
        authNotice.style.display = 'none';
        console.log('Auth notice hidden');
      }
      if (formsContainer) {
        formsContainer.style.display = 'block';
        console.log('Forms container shown:', formsContainer.style.display);
      }


      // Show the appropriate form based on category
      showCategoryForm(selectedCategory);

      // Setup form submission after showing forms
      setupFormSubmission();
    } else {
      console.log('User is not logged in, showing auth notice');
      if (authNotice) authNotice.style.display = 'block';
      if (formsContainer) formsContainer.style.display = 'none';
      if (contactSection) contactSection.style.display = 'none';
      if (submitSection) submitSection.style.display = 'none';
    }
  }

  // Show the appropriate form based on category
  function showCategoryForm(category) {
    console.log('showCategoryForm called with category:', category);

    // Hide all forms
    const forms = ['housing-form', 'jobs-form', 'buy-sell-form', 'community-form'];
    forms.forEach(formId => {
      const form = document.getElementById(formId);
      if (form) {
        form.style.display = 'none';
        console.log('Hidden form:', formId);
      } else {
        console.log('Form not found:', formId);
      }
    });

    // Show the selected form
    let formToShow = 'housing-form'; // default
    switch(category) {
      case 'jobs':
        formToShow = 'jobs-form';
        break;
      case 'buy-sell':
        formToShow = 'buy-sell-form';
        break;
      case 'community':
        formToShow = 'community-form';
        break;
      default:
        formToShow = 'housing-form';
    }

    console.log('Trying to show form:', formToShow);
    const selectedForm = document.getElementById(formToShow);
    console.log('Selected form element found:', !!selectedForm);

    if (selectedForm) {
      selectedForm.style.display = 'block';
      console.log('Form displayed:', formToShow);
    } else {
      console.log('ERROR: Could not find form element:', formToShow);
    }
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', checkAuthAndShowForm);

  // Listen for auth state changes
  document.addEventListener('authStateChanged', checkAuthAndShowForm);

  // Handle form submission for all category forms
  function setupFormSubmission() {
    // Prevent multiple setups
    if (window.formSubmissionSetup) {
      console.log('Form submission already set up, skipping...');
      return;
    }

    console.log('Setting up form submission handlers...');
    const forms = ['housing-form', 'jobs-form', 'buy-sell-form', 'community-form'];

    forms.forEach(formId => {
      const form = document.getElementById(formId);
      console.log('Form setup - ID:', formId, 'Found:', !!form);
      if (form && !form.dataset.listenerAdded) {
        console.log('Adding submit listener to:', formId);
        form.dataset.listenerAdded = 'true';
        form.addEventListener('submit', async (e) => {
          console.log('Form submitted:', formId, e);
          e.preventDefault();

          // Prevent double submission
          if (e.target.dataset.submitting === 'true') {
            return;
          }
          e.target.dataset.submitting = 'true';

          const currentUser = window.authFunctions?.getCurrentUser();
          if (!currentUser) {
            showFormError('Please log in to post an ad');
            e.target.dataset.submitting = 'false';
            return;
          }

          const formData = new FormData(e.target);
          const data = {};

          // Convert form data to object
          for (const [key, value] of formData.entries()) {
            if (value.toString().trim()) {
              data[key] = value;
            }
          }

          // Handle WhatsApp auto-population from phone number
          const categoryForId = selectedCategory.replace('-', '_'); // Convert buy-sell to buy_sell for ID matching
          const whatsappCheckbox = document.getElementById(`${categoryForId}_whatsapp_same_as_phone`);
          const phoneInput = document.getElementById(`${categoryForId}_contact_phone`);

          if (whatsappCheckbox && whatsappCheckbox.checked && phoneInput && phoneInput.value.trim()) {
            data.contact_whatsapp = phoneInput.value.trim();
          }

          // Add required fields
          data.city_slug = citySlug;
          data.user_id = currentUser.id;
          data.category = selectedCategory;

          // Build category-specific data object
          let categorySpecificData = {};

          if (selectedCategory === 'housing') {
            categorySpecificData = {
              listing_type: data.listing_type,
              housing_type: data.housing_type,
              bedrooms: data.bedrooms,
              bathrooms: data.bathrooms,
              furnished: data.furnished
            };
          } else if (selectedCategory === 'jobs') {
            // Collect languages
            const languages = [];
            const languageCheckboxes = document.querySelectorAll('input[name="languages"]:checked');
            languageCheckboxes.forEach(checkbox => languages.push(checkbox.value));

            categorySpecificData = {
              job_listing_type: data.job_listing_type,
              job_category: data.job_category,
              duration_type: data.duration_type,
              availability: data.availability,
              preferred_languages: languages.length > 0 ? languages : null
            };

            // Add rate information to main data object
            if (data.rate_type) {
              data.rate_type = data.rate_type;
              if (data.rate_type !== 'negotiable') {
                if (data.price_local) data.rate_amount = parseFloat(data.price_local);
                if (data.price_usd && !data.price_local) data.rate_amount = parseFloat(data.price_usd);
                data.rate_currency = data.price_local ? localCurrency.code : 'USD';
              }
            }
          } else if (selectedCategory === 'buy-sell') {
            categorySpecificData = {
              listing_type: data.buy_sell_type, // Maps buy_sell_type to listing_type
              item_category: data.item_category,
              condition: data.condition,
              brand: data.brand
            };
          } else if (selectedCategory === 'community') {
            categorySpecificData = {
              event_type: data.event_type,
              event_date: data.event_date,
              event_time: data.event_time,
              event_location: data.event_location
            };
            
            // Add useful links fields to main data object (not category specific)
            if (data.facebook_link) data.facebook_link = data.facebook_link;
            if (data.telegram_link) data.telegram_link = data.telegram_link;
            if (data.whatsapp_group_link) data.whatsapp_group_link = data.whatsapp_group_link;
            if (data.website_link) data.website_link = data.website_link;
          }

          // Remove empty values from category specific data
          Object.keys(categorySpecificData).forEach(key => {
            if (!categorySpecificData[key] || categorySpecificData[key].toString().trim() === '') {
              delete categorySpecificData[key];
            }
          });

          // Add category specific data to the main data object
          if (Object.keys(categorySpecificData).length > 0) {
            data.category_specific_data = categorySpecificData;
          }

          // Add uploaded images if available
          const getImagesFunction = window[`getUploadedImages_${selectedCategory}`];
          if (getImagesFunction) {
            const uploadedImages = getImagesFunction();
            if (uploadedImages && uploadedImages.length > 0) {
              data.image_url = uploadedImages[0]; // Use first image as primary
              data.additional_images = uploadedImages.slice(1); // Store additional images

              // Check for selected thumbnail
              const selectedThumbnail = document.querySelector('input[name="selected_thumbnail"]:checked');
              if (selectedThumbnail) {
                data.selected_thumbnail_url = selectedThumbnail.value;
              }

              console.log('Including images in submission:', {
                primary: data.image_url,
                additional: data.additional_images,
                thumbnail: data.selected_thumbnail_url
              });
            } else {
              console.log('No images uploaded for category:', selectedCategory);
            }
          } else {
            // Fallback to legacy single image function
            if (window.getUploadedImageUrl && window.getUploadedImageUrl()) {
              data.image_url = window.getUploadedImageUrl();
              console.log('Including legacy image URL in submission:', data.image_url);
            } else {
              console.log('No image URL available for submission');
            }
          }

          // Add CV file if uploaded (for jobs category)
          if (selectedCategory === 'jobs' && window.uploadedCVInfo) {
            data.category_specific_data = data.category_specific_data || {};
            data.category_specific_data.cv_url = window.uploadedCVInfo.url;
            data.category_specific_data.cv_filename = window.uploadedCVInfo.fileName;
            data.category_specific_data.cv_size = window.uploadedCVInfo.fileSize;
            console.log('Including CV in submission:', window.uploadedCVInfo);
          }

          // Validate at least one contact method with better UX
          if (!data.contact_email && !data.contact_phone && !data.contact_whatsapp) {
            showFormError('Please provide at least one contact method (email, phone, or WhatsApp)');
            e.target.dataset.submitting = 'false';
            return;
          }

          // Find the submit button in the current form
          const submitBtn = e.target.querySelector('button[type="submit"]');
          const originalText = submitBtn.innerHTML;

          try {
            // Show beautiful processing modal
            showFormProcessing();

            // Update button state
            submitBtn.innerHTML = '<span class="mr-2">⏳</span>Posting...';
            submitBtn.disabled = true;

            const response = await fetch('/api/classifieds/create', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
              // Get the current category from the form to redirect to the right section
              const currentCategory = getCurrentCategory();
              const redirectUrl = currentCategory && currentCategory !== 'housing'
                ? `/${citySlug}/classifieds/${currentCategory}`
                : `/${citySlug}/classifieds`;

              // Show success modal with category-specific redirect
              showFormSuccess(
                'Your classified ad has been posted successfully! You can now view it in the classifieds section.',
                redirectUrl
              );
            } else {
              showFormError('Error posting ad: ' + (result.error || 'Please check your information and try again.'));
            }
          } catch (error) {
            console.error('Error:', error);
            showFormError('Unable to post your ad right now. Please check your internet connection and try again.');
          } finally {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            e.target.dataset.submitting = 'false';
          }
        });
      }
    });

    // Mark as set up
    window.formSubmissionSetup = true;
  }

  // World-class UX feedback functions
  function showFormError(message) {
    clearFormFeedback();
    const errorDiv = document.createElement('div');
    errorDiv.id = 'form-feedback';
    errorDiv.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl border border-red-200 p-8 z-50 max-w-md w-full mx-4 animate-bounce-in';
    errorDiv.innerHTML = `
      <div class="text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span class="text-2xl">❌</span>
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">Oops! Something went wrong</h3>
        <p class="text-slate-600 mb-6">${message}</p>
        <button id="error-close-btn" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
          Try Again
        </button>
      </div>
    `;
    document.body.appendChild(errorDiv);

    // Add event listener to close button
    const closeBtn = errorDiv.querySelector('#error-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', clearFormFeedback);
    }

    // Auto-hide after 8 seconds
    setTimeout(clearFormFeedback, 8000);
  }

  function showFormSuccess(message, redirectUrl) {
    clearFormFeedback();
    const successDiv = document.createElement('div');
    successDiv.id = 'form-feedback';
    successDiv.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl border border-green-200 p-8 z-50 max-w-md w-full mx-4 animate-bounce-in';
    successDiv.innerHTML = `
      <div class="text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span class="text-2xl">🎉</span>
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">Success!</h3>
        <p class="text-slate-600 mb-6">${message}</p>
        <div class="flex items-center justify-center space-x-3">
          <div class="w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm text-slate-500">Redirecting you now...</span>
        </div>
      </div>
    `;
    document.body.appendChild(successDiv);

    // Redirect after 2 seconds for better UX
    setTimeout(() => {
      window.location.href = redirectUrl;
    }, 2000);
  }

  function showFormProcessing() {
    clearFormFeedback();
    const processingDiv = document.createElement('div');
    processingDiv.id = 'form-feedback';
    processingDiv.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl border border-blue-200 p-8 z-50 max-w-md w-full mx-4 animate-bounce-in';
    processingDiv.innerHTML = `
      <div class="text-center">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <div class="w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">Posting your ad...</h3>
        <p class="text-slate-600">Please wait while we publish your classified ad</p>
      </div>
    `;
    document.body.appendChild(processingDiv);
  }

  function clearFormFeedback() {
    const feedbackDiv = document.getElementById('form-feedback');
    if (feedbackDiv) feedbackDiv.remove();
  }

  // Make clearFormFeedback globally accessible
  window.clearFormFeedback = clearFormFeedback;

  // Get current category for redirect purposes
  function getCurrentCategory() {
    return selectedCategory;
  }

  // WhatsApp and Contact Method Functions
  window.handleWhatsAppCheckbox = function(checkbox, category) {
    // Handle category naming for IDs (buy-sell uses underscore in IDs)
    const categoryId = category === 'buy-sell' ? 'buy_sell' : category;
    const phoneInput = document.getElementById(`${categoryId}_contact_phone`);
    const whatsappInput = document.getElementById(`${categoryId}_contact_whatsapp`);

    if (checkbox.checked) {
      // Auto-populate WhatsApp with phone number
      if (phoneInput && phoneInput.value) {
        whatsappInput.value = phoneInput.value;
      }
      // Disable WhatsApp input when using same as phone
      whatsappInput.disabled = true;
      whatsappInput.classList.add('bg-slate-100', 'text-slate-500');
    } else {
      // Enable manual WhatsApp input
      whatsappInput.disabled = false;
      whatsappInput.classList.remove('bg-slate-100', 'text-slate-500');
      // Clear WhatsApp input
      whatsappInput.value = '';
    }
  };

  window.handlePhoneInput = function(phoneInput, category) {
    // Handle category naming for IDs (buy-sell uses underscore in IDs)
    const categoryId = category === 'buy-sell' ? 'buy_sell' : category;
    const checkbox = document.getElementById(`${categoryId}_whatsapp_same_as_phone`);
    const whatsappInput = document.getElementById(`${categoryId}_contact_whatsapp`);

    // If checkbox is checked, auto-update WhatsApp number
    if (checkbox && checkbox.checked && whatsappInput) {
      whatsappInput.value = phoneInput.value;
    }
  };

  // Function to update preferred star display
  window.updatePreferredStar = function(category, selectedMethod) {
    // Hide all stars for this category
    const stars = ['email', 'phone', 'whatsapp'];
    stars.forEach(method => {
      const star = document.getElementById(`${category}_${method}_star`);
      if (star) {
        star.classList.add('hidden');
      }
    });

    // Show star for selected method
    const selectedStar = document.getElementById(`${category}_${selectedMethod}_star`);
    if (selectedStar) {
      selectedStar.classList.remove('hidden');
    }
  };

  // Setup form submission after DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupFormSubmission();
    setupImageUpload();
    setupContactMethodValidation();
  });

  // Contact method validation
  function setupContactMethodValidation() {
    // Add validation for preferred contact method
    document.addEventListener('change', function(e) {
      if (e.target.name === 'preferred_contact_method') {
        validatePreferredContactMethod(e.target);
      }
    });
  }

  function validatePreferredContactMethod(radio) {
    const form = radio.closest('form');
    if (!form) return;

    const preferredMethod = radio.value;
    const emailInput = form.querySelector('input[name="contact_email"]');
    const phoneInput = form.querySelector('input[name="contact_phone"]');
    const whatsappInput = form.querySelector('input[name="contact_whatsapp"]');

    // Show warning if preferred method doesn't have a value
    let warningMessage = '';

    if (preferredMethod === 'email' && (!emailInput || !emailInput.value.trim())) {
      warningMessage = 'Please enter your email address since you selected it as your preferred contact method.';
    } else if (preferredMethod === 'phone' && (!phoneInput || !phoneInput.value.trim())) {
      warningMessage = 'Please enter your phone number since you selected it as your preferred contact method.';
    } else if (preferredMethod === 'whatsapp' && (!whatsappInput || !whatsappInput.value.trim())) {
      warningMessage = 'Please enter your WhatsApp number since you selected it as your preferred contact method.';
    }

    // Remove existing warning
    const existingWarning = form.querySelector('.preferred-contact-warning');
    if (existingWarning) {
      existingWarning.remove();
    }

    // Show new warning if needed
    if (warningMessage) {
      const warningDiv = document.createElement('div');
      warningDiv.className = 'preferred-contact-warning bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3';
      warningDiv.innerHTML = `
        <div class="flex items-start space-x-2">
          <span class="text-yellow-500 text-lg">⚠️</span>
          <div class="text-sm text-yellow-800">
            <p class="font-medium">Preferred Contact Method</p>
            <p>${warningMessage}</p>
          </div>
        </div>
      `;
      radio.closest('.grid').parentNode.appendChild(warningDiv);
    }
  }



  // Enhanced image upload functionality with multiple images support
  function setupImageUpload() {
    const categories = ['housing', 'jobs', 'buy-sell', 'community'];

    categories.forEach(category => {
      setupCategoryImageUpload(category);
    });
  }

  function setupCategoryImageUpload(category) {
    const imageInput = document.getElementById(`image_upload_${category}`);
    const imagePreviewsContainer = document.getElementById(`image_previews_${category}`);
    const uploadStatus = document.getElementById(`upload_status_${category}`);
    const uploadErrors = document.getElementById(`upload_errors_${category}`);
    const imageCount = document.getElementById(`image_count_${category}`);

    let uploadedImages = [];
    const maxImages = 3;

    if (!imageInput) return; // Skip if this category's input doesn't exist

    imageInput.addEventListener('change', async (e) => {
      const files = Array.from(e.target.files);

      // Check if adding these files would exceed the limit
      if (uploadedImages.length + files.length > maxImages) {
        showError(`You can only upload up to ${maxImages} images. Please select fewer images.`);
        return;
      }

      // Validate each file before uploading
      for (const file of files) {
        if (!validateFile(file)) {
          return; // Stop if any file is invalid
        }
      }

      // Upload files one by one
      for (const file of files) {
        await uploadSingleImage(file);
      }

      // Clear the input so the same files can be selected again if needed
      imageInput.value = '';
    });

    function validateFile(file) {
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        showError('Please select only JPEG, PNG, or WebP images.');
        return false;
      }

      // Check file size (2MB limit)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        showError(`"${file.name}" is too large! Please use images under 2MB. Try compressing your image.`);
        return false;
      }

      return true;
    }

    async function uploadSingleImage(file) {
      const currentUser = window.authFunctions?.getCurrentUser();
      if (!currentUser) {
        showError('Please log in to upload images.');
        return;
      }

      // Create preview immediately
      const previewId = `preview_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      createImagePreview(file, previewId);
      showStatus('Uploading...', 'loading');

      try {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('userId', currentUser.id);

        const response = await fetch('/api/classifieds/upload-image', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          // Update the preview with the uploaded URL
          uploadedImages.push({
            url: result.imageUrl,
            previewId: previewId,
            fileName: file.name
          });

          updateImagePreview(previewId, result.imageUrl, true);
          updateImageCount();
          updateThumbnailSelection();
          showStatus(`${file.name} uploaded successfully!`, 'success');

          // Clear success message after 3 seconds
          setTimeout(() => {
            if (uploadStatus) uploadStatus.classList.add('hidden');
          }, 3000);
        } else {
          // Remove the failed preview
          removeImagePreview(previewId);

          // Show friendly error message
          const errorMsg = result.friendlyError ? result.error : 'Upload failed. Please try again.';
          showError(errorMsg);
        }
      } catch (error) {
        console.error('Upload error:', error);
        removeImagePreview(previewId);
        showError('Upload failed. Please check your connection and try again.');
      }
    }

    function createImagePreview(file, previewId) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const previewHtml = `
          <div id="${previewId}" class="relative group">
            <img src="${e.target.result}" alt="Preview" class="w-full h-24 object-cover rounded-lg border-2 border-slate-200" />
            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
              <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
            <div class="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
              Uploading...
            </div>
          </div>
        `;

        if (imagePreviewsContainer) {
          imagePreviewsContainer.insertAdjacentHTML('beforeend', previewHtml);
          imagePreviewsContainer.classList.remove('hidden');
        }
      };
      reader.readAsDataURL(file);
    }

    function updateImagePreview(previewId, imageUrl, success) {
      const previewElement = document.getElementById(previewId);
      if (!previewElement) return;

      if (success) {
        previewElement.innerHTML = `
          <img src="${imageUrl}" alt="Uploaded" class="w-full h-24 object-cover rounded-lg border-2 border-green-200" />
          <button
            type="button"
            onclick="removeUploadedImage('${previewId}', '${category}')"
            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs transition-colors flex items-center justify-center"
          >
            ×
          </button>
          <div class="absolute bottom-1 left-1 bg-green-500 text-white text-xs px-2 py-1 rounded">
            ✓ Uploaded
          </div>
        `;
      }
    }

    function removeImagePreview(previewId) {
      const previewElement = document.getElementById(previewId);
      if (previewElement) {
        previewElement.remove();
      }

      // Hide container if no images
      if (imagePreviewsContainer && imagePreviewsContainer.children.length === 0) {
        imagePreviewsContainer.classList.add('hidden');
      }
    }

    function updateImageCount() {
      if (imageCount) {
        imageCount.textContent = uploadedImages.length;
      }
    }

    function updateThumbnailSelection() {
      const thumbnailSelection = document.getElementById(`thumbnail_selection_${category}`);
      const thumbnailOptions = document.getElementById(`thumbnail_options_${category}`);

      if (uploadedImages.length > 0) {
        thumbnailSelection.classList.remove('hidden');

        // Clear existing options
        thumbnailOptions.innerHTML = '';

        // Add thumbnail options
        uploadedImages.forEach((image, index) => {
          const isSelected = index === 0; // First image is selected by default
          const optionHtml = `
            <label class="cursor-pointer">
              <input type="radio" name="selected_thumbnail" value="${image.url}" ${isSelected ? 'checked' : ''} class="sr-only" onchange="selectThumbnail('${image.url}')">
              <div class="relative border-2 ${isSelected ? 'border-emerald-500' : 'border-slate-200'} rounded-lg overflow-hidden hover:border-emerald-300 transition-colors">
                <img src="${image.url}" alt="Thumbnail option" class="w-16 h-16 object-cover">
                ${isSelected ? '<div class="absolute top-1 right-1 bg-emerald-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">✓</div>' : ''}
              </div>
            </label>
          `;
          thumbnailOptions.insertAdjacentHTML('beforeend', optionHtml);
        });
      } else {
        thumbnailSelection.classList.add('hidden');
      }
    }

    window.selectThumbnail = function(imageUrl) {
      // Update visual selection
      const thumbnailOptions = document.getElementById(`thumbnail_options_${category}`);
      const labels = thumbnailOptions.querySelectorAll('label');

      labels.forEach(label => {
        const radio = label.querySelector('input[type="radio"]');
        const div = label.querySelector('div');
        const checkmark = label.querySelector('.absolute');

        if (radio.value === imageUrl) {
          div.classList.remove('border-slate-200');
          div.classList.add('border-emerald-500');
          if (!checkmark) {
            div.insertAdjacentHTML('beforeend', '<div class="absolute top-1 right-1 bg-emerald-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">✓</div>');
          }
        } else {
          div.classList.remove('border-emerald-500');
          div.classList.add('border-slate-200');
          if (checkmark) {
            checkmark.remove();
          }
        }
      });
    };

    function showStatus(message, type) {
      if (!uploadStatus) return;

      uploadStatus.innerHTML = `
        <div class="flex items-center space-x-2 text-sm ${type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600'}">
          ${type === 'loading' ? '<div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>' : ''}
          <span>${message}</span>
        </div>
      `;
      uploadStatus.classList.remove('hidden');
    }

    function showError(message) {
      if (!uploadErrors) return;

      uploadErrors.innerHTML = `
        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
          <div class="flex items-start space-x-2">
            <span class="text-red-500 text-lg">⚠️</span>
            <div class="text-sm text-red-800">
              <p class="font-medium">Upload Error</p>
              <p>${message}</p>
            </div>
          </div>
        </div>
      `;
      uploadErrors.classList.remove('hidden');

      // Clear error after 5 seconds
      setTimeout(() => {
        if (uploadErrors) uploadErrors.classList.add('hidden');
      }, 5000);
    }

    // Make functions globally available for this category
    window[`removeUploadedImage_${category}`] = function(previewId) {
      // Remove from uploaded images array
      uploadedImages = uploadedImages.filter(img => img.previewId !== previewId);

      // Remove preview element
      removeImagePreview(previewId);

      // Update count
      updateImageCount();
    };

    // Make uploaded images available for form submission
    window[`getUploadedImages_${category}`] = () => uploadedImages.map(img => img.url);
  }

  // Global function to remove uploaded images (called from HTML)
  window.removeUploadedImage = function(previewId, category) {
    if (window[`removeUploadedImage_${category}`]) {
      window[`removeUploadedImage_${category}`](previewId);
    }
  };

  // Legacy function for backward compatibility
  window.getUploadedImageUrl = () => {
    // Return the first image from any category that has images
    const categories = ['housing', 'jobs', 'buy-sell', 'community'];
    for (const category of categories) {
      const images = window[`getUploadedImages_${category}`]?.() || [];
      if (images.length > 0) {
        return images[0]; // Return first image for backward compatibility
      }
    }
    return null;
  };

  // Dynamic form field updates based on listing type
  window.updateHousingFormFields = function(listingType) {
    const priceLabel = document.getElementById('housing_price_label');
    const priceHelp = document.getElementById('housing_price_help');

    if (!priceLabel || !priceHelp) return;

    switch(listingType) {
      case 'rent':
        priceLabel.textContent = 'Monthly Rent *';
        priceHelp.textContent = 'Enter monthly rental price in USD';
        break;
      case 'sale':
        priceLabel.textContent = 'Sale Price *';
        priceHelp.textContent = 'Enter asking price in USD';
        break;
      case 'roommate':
        priceLabel.textContent = 'Monthly Share *';
        priceHelp.textContent = 'Enter monthly cost per roommate in USD';
        break;
      default:
        priceLabel.textContent = 'Price *';
        priceHelp.textContent = 'Select listing type to see price guidance';
    }
  };

  window.updateBuySellFormFields = function(listingType) {
    const priceLabel = document.getElementById('buy_sell_price_label');
    const priceHelp = document.getElementById('buy_sell_price_help');

    if (!priceLabel || !priceHelp) return;

    switch(listingType) {
      case 'sell':
        priceLabel.textContent = 'Asking Price *';
        priceHelp.textContent = 'Enter your asking price in either currency (at least one required, enter 0 for free items)';
        break;
      case 'buy':
        priceLabel.textContent = 'Budget/Offer *';
        priceHelp.textContent = 'Enter your budget or offer price in either currency (at least one required)';
        break;
      default:
        priceLabel.textContent = 'Price *';
        priceHelp.textContent = 'Enter price in either currency (at least one required)';
    }
  };

  window.updateJobFormFields = function(listingType) {
    const salaryLabel = document.getElementById('job_salary_label');
    const salaryHelp = document.getElementById('job_salary_help');

    if (!salaryLabel || !salaryHelp) return;

    switch(listingType) {
      case 'job_offered':
        salaryLabel.textContent = 'Salary/Rate Offered';
        salaryHelp.textContent = 'Enter the salary or hourly rate you\'re offering in either currency (optional)';
        break;
      case 'looking_for_work':
        salaryLabel.textContent = 'Desired Salary/Rate';
        salaryHelp.textContent = 'Enter your desired salary or hourly rate in either currency (optional)';
        break;
      default:
        salaryLabel.textContent = 'Salary/Rate (Optional)';
        salaryHelp.textContent = 'Enter salary in either currency (optional)';
    }
  };

  // Handle event type change to show/hide appropriate contact methods
  window.handleEventTypeChange = function(eventType) {
    const standardContactMethods = document.getElementById('standard-contact-methods');
    const usefulLinksContactMethods = document.getElementById('useful-links-contact-methods');
    
    if (eventType === 'useful-links') {
      // Show useful links contact methods, hide standard ones
      if (standardContactMethods) standardContactMethods.style.display = 'none';
      if (usefulLinksContactMethods) usefulLinksContactMethods.style.display = 'block';
      
      // Remove required attribute from standard contact method radios
      const standardRadios = document.querySelectorAll('#standard-contact-methods input[type="radio"]');
      standardRadios.forEach(radio => radio.removeAttribute('required'));
      
      // Update form title and description placeholder
      const titleInput = document.getElementById('community_title');
      const descriptionInput = document.getElementById('community_description');
      
      if (titleInput) {
        titleInput.placeholder = 'e.g., Expat WhatsApp Group, Facebook Community, Telegram Channel';
      }
      if (descriptionInput) {
        descriptionInput.placeholder = 'Describe the community resource, what kind of group it is, who should join...';
      }
    } else {
      // Show standard contact methods, hide useful links ones
      if (standardContactMethods) standardContactMethods.style.display = 'block';
      if (usefulLinksContactMethods) usefulLinksContactMethods.style.display = 'none';
      
      // Add required attribute back to standard contact method radios
      const standardRadios = document.querySelectorAll('#standard-contact-methods input[type="radio"]');
      if (standardRadios.length > 0) {
        standardRadios[0].setAttribute('required', 'required');
      }
      
      // Reset form title and description placeholder
      const titleInput = document.getElementById('community_title');
      const descriptionInput = document.getElementById('community_description');
      
      if (titleInput) {
        titleInput.placeholder = 'e.g., Beach Cleanup, Expat Meetup, Spanish Class';
      }
      if (descriptionInput) {
        descriptionInput.placeholder = 'Describe the event, what to expect, who should attend...';
      }
    }
  };

  // Jobs Form Step Navigation
  let currentJobStep = 1;
  let selectedJobType = '';

  window.selectJobType = function(type) {
    selectedJobType = type;

    // Update visual selection
    document.querySelectorAll('.job-type-card').forEach(card => {
      card.classList.remove('border-emerald-500', 'bg-emerald-50');
      card.classList.add('border-slate-200');
    });

    // Highlight selected card
    const selectedCard = document.querySelector(`input[value="${type}"]`).closest('label').querySelector('.job-type-card');
    selectedCard.classList.remove('border-slate-200');
    selectedCard.classList.add('border-emerald-500', 'bg-emerald-50');

    // Enable next button
    document.getElementById('jobs-step-1-next').disabled = false;
  };

  window.nextJobStep = function(step) {
    // Hide current step
    document.getElementById(`jobs-step-${currentJobStep}`).style.display = 'none';

    // Show next step
    currentJobStep = step;
    document.getElementById(`jobs-step-${currentJobStep}`).style.display = 'block';

    // Update progress
    updateJobProgress();

    // Update form fields based on job type
    if (step === 2) {
      updateJobFormFields();
    }
  };

  window.previousJobStep = function(step) {
    // Hide current step
    document.getElementById(`jobs-step-${currentJobStep}`).style.display = 'none';

    // Show previous step
    currentJobStep = step;
    document.getElementById(`jobs-step-${currentJobStep}`).style.display = 'block';

    // Update progress
    updateJobProgress();
  };

  function updateJobProgress() {
    const progressBar = document.getElementById('jobs-progress-bar');
    const currentStepSpan = document.getElementById('jobs-current-step');

    const progressPercentage = (currentJobStep / 3) * 100;
    progressBar.style.width = `${progressPercentage}%`;
    currentStepSpan.textContent = currentJobStep;
  }

  function updateJobFormFields() {
    const titleLabel = document.getElementById('jobs-title-label');
    const titleInput = document.getElementById('job_title');
    const descriptionLabel = document.getElementById('jobs-description-label');
    const descriptionInput = document.getElementById('job_description');
    const salaryLabel = document.getElementById('job_salary_label');
    const salaryHelp = document.getElementById('job_salary_help');
    const availabilityLabel = document.getElementById('availability-label');
    const availabilitySection = document.getElementById('availability-section');
    const cvSection = document.getElementById('cv-upload-section');
    const stepTitle = document.getElementById('jobs-step-2-title');
    const stepSubtitle = document.getElementById('jobs-step-2-subtitle');

    switch(selectedJobType) {
      case 'offering_services':
        stepTitle.textContent = 'Tell us about your service';
        stepSubtitle.textContent = 'Provide details to attract the right clients';
        titleLabel.textContent = 'What service are you offering?';
        titleInput.placeholder = 'e.g., English Tutoring, Massage Therapy, Web Development';
        descriptionLabel.textContent = 'Service Description';
        descriptionInput.placeholder = 'Describe your service, experience, qualifications, and what makes you unique...';
        salaryLabel.textContent = 'Your Rate';
        salaryHelp.textContent = 'Enter your rate per hour/day/project (optional)';
        availabilityLabel.textContent = 'Available Times / Days';
        availabilitySection.style.display = 'block';
        cvSection.style.display = 'none';
        break;

      case 'looking_to_hire':
        stepTitle.textContent = 'Describe what you need';
        stepSubtitle.textContent = 'Help people understand the job or task';
        titleLabel.textContent = 'What kind of help do you need?';
        titleInput.placeholder = 'e.g., House Cleaner, English Tutor, Moving Help';
        descriptionLabel.textContent = 'Job/Task Description';
        descriptionInput.placeholder = 'Describe the job requirements, responsibilities, qualifications needed...';
        salaryLabel.textContent = 'Budget or Rate Offered';
        salaryHelp.textContent = 'Enter what you\'re willing to pay (optional)';
        availabilityLabel.textContent = 'When do you need this?';
        availabilitySection.style.display = 'block';
        cvSection.style.display = 'none';
        break;

      case 'looking_for_work':
        stepTitle.textContent = 'Tell us about yourself';
        stepSubtitle.textContent = 'Show potential employers your skills';
        titleLabel.textContent = 'What work are you looking for?';
        titleInput.placeholder = 'e.g., Pet Sitting, Cooking, Handyman Work';
        descriptionLabel.textContent = 'About You & Your Skills';
        descriptionInput.placeholder = 'Describe your experience, skills, and what kind of work you\'re seeking...';
        salaryLabel.textContent = 'Expected Rate';
        salaryHelp.textContent = 'Enter your desired rate (optional)';
        availabilityLabel.textContent = 'Your Availability';
        availabilitySection.style.display = 'block';
        cvSection.style.display = 'block';
        break;
    }
  }

  // Character counter for description
  document.addEventListener('DOMContentLoaded', function() {
    const descriptionInput = document.getElementById('job_description');
    const counter = document.getElementById('description-count');

    if (descriptionInput && counter) {
      descriptionInput.addEventListener('input', function() {
        counter.textContent = this.value.length;
      });
    }

    // CV Upload functionality
    const cvUpload = document.getElementById('cv_upload');
    if (cvUpload) {
      cvUpload.addEventListener('change', handleCVUpload);
    }
  });

  // CV Upload handler
  async function handleCVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a PDF, DOC, or DOCX file.');
      event.target.value = '';
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB.');
      event.target.value = '';
      return;
    }

    // Check if user is logged in
    const currentUser = window.authFunctions?.getCurrentUser();
    if (!currentUser) {
      alert('Please log in to upload files.');
      event.target.value = '';
      return;
    }

    // Show uploading state
    const preview = document.getElementById('cv-preview');
    preview.innerHTML = `
      <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center space-x-3">
          <div class="text-2xl animate-spin">⏳</div>
          <div>
            <div class="font-medium text-slate-900">Uploading ${file.name}...</div>
            <div class="text-sm text-blue-600">Please wait</div>
          </div>
        </div>
      </div>
    `;
    preview.classList.remove('hidden');

    try {
      // Upload CV file
      const formData = new FormData();
      formData.append('cv', file);
      formData.append('userId', currentUser.id);

      const response = await fetch('/api/classifieds/upload-cv', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Store CV info globally for form submission
        window.uploadedCVInfo = {
          url: result.cvUrl,
          fileName: result.originalName,
          fileSize: result.fileSize
        };

        // Show success state
        preview.innerHTML = `
          <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
            <div class="flex items-center space-x-3">
              <div class="text-2xl">📄</div>
              <div>
                <div class="font-medium text-slate-900">${result.originalName}</div>
                <div class="text-sm text-green-600">✓ Uploaded successfully - ${(result.fileSize / 1024 / 1024).toFixed(2)} MB</div>
              </div>
            </div>
            <button type="button" onclick="removeCVFile()" class="text-red-600 hover:text-red-800 font-medium text-sm">
              Remove
            </button>
          </div>
        `;
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('CV upload error:', error);

      // Show error state
      preview.innerHTML = `
        <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
          <div class="flex items-center space-x-3">
            <div class="text-2xl">❌</div>
            <div>
              <div class="font-medium text-slate-900">Upload failed</div>
              <div class="text-sm text-red-600">${error.message}</div>
            </div>
          </div>
          <button type="button" onclick="removeCVFile()" class="text-red-600 hover:text-red-800 font-medium text-sm">
            Remove
          </button>
        </div>
      `;

      // Clear the input
      event.target.value = '';
    }
  }

  window.removeCVFile = function() {
    document.getElementById('cv_upload').value = '';
    document.getElementById('cv-preview').classList.add('hidden');
    window.uploadedCVInfo = null;
  };

  // Rate type handling
  window.updateRateLabels = function() {
    const rateType = document.getElementById('rate_type').value;
    const rateSection = document.getElementById('rate-amount-section');
    const localLabel = document.getElementById('rate-local-label');
    const usdLabel = document.getElementById('rate-usd-label');
    const helpText = document.getElementById('job_salary_help');

    if (rateType && rateType !== 'negotiable') {
      rateSection.style.display = 'grid';

      // Update labels based on rate type
      const rateTypeText = {
        'hourly': 'per hour',
        'daily': 'per day',
        'weekly': 'per week',
        'monthly': 'per month',
        'project': 'per project'
      };

      const suffix = rateTypeText[rateType] || '';
      localLabel.textContent = `${localLabel.textContent.split(' (')[0]} (${suffix})`;
      usdLabel.textContent = `US Dollars (${suffix})`;
      helpText.textContent = `Enter your rate ${suffix} (optional)`;
    } else if (rateType === 'negotiable') {
      rateSection.style.display = 'none';
      helpText.textContent = 'Rate will be discussed with interested parties';
    } else {
      rateSection.style.display = 'none';
      helpText.textContent = 'Select rate type first, then enter amount (optional)';
    }
  };


</script>

<style>
  /* Beautiful modal animations */
  @keyframes bounce-in {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.3);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.05);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .animate-bounce-in {
    animation: bounce-in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Backdrop blur effect */
  #form-feedback::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: -1;
  }

  /* Mobile-first responsive design for forms */
  @media (max-width: 640px) {
    /* Ensure form inputs are properly sized on mobile */
    .grid.grid-cols-1.md\\:grid-cols-3 {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .grid.grid-cols-1.md\\:grid-cols-2 {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    /* Better touch targets for mobile */
    input, select, textarea {
      min-height: 44px;
      font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Responsive padding for mobile */
    .p-4.sm\\:p-6.lg\\:p-8 {
      padding: 1rem;
    }

    /* Mobile modal adjustments */
    #form-feedback {
      margin: 1rem;
      width: calc(100% - 2rem);
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    /* Tablet styles */
    .grid.grid-cols-1.md\\:grid-cols-3 {
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }
  }

  /* Spinner animation */
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Jobs Form Step Styles */
  .jobs-step {
    min-height: 400px;
  }

  .job-type-card {
    transition: all 0.2s ease-in-out;
  }

  .job-type-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Progress bar animation */
  #jobs-progress-bar {
    transition: width 0.3s ease-in-out;
  }

  /* Mobile-specific improvements */
  @media (max-width: 640px) {
    .jobs-step {
      min-height: 300px;
    }

    /* Make job type cards more mobile-friendly */
    .job-type-card {
      padding: 1rem !important;
    }

    .job-type-card .text-3xl {
      font-size: 2rem;
    }

    /* Improve language selection on mobile */
    .grid.grid-cols-2.md\\:grid-cols-4 {
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
    }

    /* Better spacing for mobile forms */
    .space-y-6 > * + * {
      margin-top: 1.5rem;
    }

    /* Thumbnail selection mobile optimization */
    #thumbnail_options_jobs .flex {
      flex-wrap: wrap;
      gap: 0.5rem;
    }
  }
</style>
