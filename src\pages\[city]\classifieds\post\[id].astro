---
import Layout from '../../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../../components/MinimalAcctButton.astro';
import ProtectedContact from '../../../../components/ProtectedContact.astro';
import { getCityBySlug } from '../../../../lib/database';
import { getClassifiedPost, CLASSIFIED_CATEGORIES, timeAgo, formatPrice, formatDualPrice } from '../../../../lib/classifieds';
import { getCurrencyForCountry } from '../../../../lib/currencies';
import Breadcrumb from '../../../../components/Breadcrumb.astro';

const { city: citySlug, id } = Astro.params;

if (!citySlug || !id) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get classified post
const { data: post, error: postError } = await getClassifiedPost(id);

if (postError || !post) {
  return Astro.redirect(`/${citySlug}/classifieds`);
}

// Get local currency for the city's country
const localCurrency = getCurrencyForCountry(city.country || 'Mexico');

const categoryInfo = CLASSIFIED_CATEGORIES[post.category as keyof typeof CLASSIFIED_CATEGORIES];
const pageTitle = `${post.title} - ${city.name} Classifieds`;
const pageDescription = post.description.substring(0, 160);
---

<Layout title={pageTitle} description={pageDescription}>
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 py-4 md:py-6">
      <div class="max-w-7xl mx-auto px-4 md:px-6">
        <Breadcrumb
          className="mb-4"
          items={[
            { label: 'Main', href: '/', icon: '🌎' },
            { label: city.name, href: `/${citySlug}` },
            { label: 'Classifieds', href: `/${citySlug}/classifieds` },
            { label: categoryInfo.name, href: `/${citySlug}/classifieds/${post.category}` },
            { label: 'Post Details', isActive: true }
          ]}
        />

        <!-- Back Navigation for Mobile -->
        <div class="flex items-center justify-between md:hidden mb-4">
          <a
            href={`/${citySlug}/classifieds/${post.category}`}
            class="flex items-center text-sm text-slate-600 hover:text-slate-800 font-medium"
          >
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to {categoryInfo.name}
          </a>
        </div>
      </div>
    </div>

    <!-- Post Details -->
    <div class="py-4 md:py-8">
      <div class="max-w-7xl mx-auto px-4 md:px-6">
        <!-- Mobile-First Responsive Layout -->
        <div class="bg-white rounded-lg md:rounded-xl border border-slate-200 overflow-hidden">
          <!-- Header -->
          <div class="p-4 md:p-6 border-b border-slate-200">
            <!-- Category and Meta Info -->
            <div class="flex flex-wrap items-center gap-2 mb-4">
              <div class="flex items-center space-x-2">
                <span class="text-xl md:text-2xl">{categoryInfo.icon}</span>
                <span class="text-xs md:text-sm bg-slate-100 text-slate-600 px-2 md:px-3 py-1 rounded-full font-medium">
                  {categoryInfo.name}
                </span>
              </div>

              <!-- Listing Type Indicators -->
              {post.category_specific_data && (
                <div class="flex flex-wrap items-center gap-1 md:gap-2">
                  {/* Housing indicators */}
                  {post.category === 'housing' && post.category_specific_data.listing_type && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs md:text-sm font-medium bg-blue-100 text-blue-800">
                      {(post.category_specific_data.listing_type === 'rent' || post.category_specific_data.listing_type === 'rental') && '🏠 For Rent'}
                      {post.category_specific_data.listing_type === 'sale' && '🏡 For Sale'}
                      {post.category_specific_data.listing_type === 'roommate' && '👥 Roommate Wanted'}
                    </span>
                  )}
                  {/* Jobs indicators */}
                  {post.category === 'jobs' && post.category_specific_data.job_listing_type && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs md:text-sm font-medium bg-purple-100 text-purple-800">
                      {post.category_specific_data.job_listing_type === 'job_offered' && '💼 Job Offered'}
                      {post.category_specific_data.job_listing_type === 'looking_for_work' && '🔍 Looking for Work'}
                    </span>
                  )}
                  {/* Buy & Sell indicators */}
                  {post.category === 'buy-sell' && post.category_specific_data.listing_type && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs md:text-sm font-medium bg-green-100 text-green-800">
                      {post.category_specific_data.listing_type === 'sell' && '💰 For Sale'}
                      {post.category_specific_data.listing_type === 'buy' && '🔍 Want to Buy'}
                    </span>
                  )}
                </div>
              )}

              <span class="text-xs md:text-sm text-slate-500 ml-auto">
                Posted {timeAgo(post.created_at)}
              </span>
            </div>

            <!-- Title and Price -->
            <div class="space-y-3">
              <h1 class="text-xl md:text-2xl lg:text-3xl font-bold text-slate-900 leading-tight">
                {post.title}
              </h1>

              <div class="text-lg md:text-xl font-semibold text-emerald-600">
                {post.price_local || post.price_usd ?
                  formatDualPrice(post.price_local, post.price_usd, localCurrency.symbol, localCurrency.code) :
                  formatPrice(post.price)
                }
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="p-3 md:p-4">
            <!-- Image Gallery -->
            {(post.image_url || (post.additional_images && post.additional_images.length > 0)) && (
              <div class="mb-4">
                <div id="image-gallery" class="space-y-4">
                  <!-- Main Image Display -->
                  <div class="group relative bg-slate-100 rounded-xl overflow-hidden mx-auto max-w-lg md:max-w-2xl lg:max-w-3xl">
                    <!-- Responsive aspect ratio container -->
                    <div class="relative w-full aspect-[4/3] md:aspect-[16/10] lg:aspect-[3/2]">
                      <img
                        id="main-image"
                        src={post.image_url || (post.additional_images && post.additional_images[0])}
                        alt={post.title}
                        class="absolute inset-0 w-full h-full object-cover cursor-pointer hover:scale-[1.02] transition-transform duration-300"
                        loading="lazy"
                        onclick="openImageModal()"
                      />

                      <!-- Image Counter -->
                      <div id="image-counter" class="absolute top-2 right-2 md:top-3 md:right-3 bg-black bg-opacity-70 text-white px-2 py-1 md:px-3 md:py-1.5 rounded-full text-xs md:text-sm font-medium backdrop-blur-sm">
                        <span id="current-image">1</span> / <span id="total-images">1</span>
                      </div>

                      <!-- Expand Icon -->
                      <div class="absolute top-2 left-2 md:top-3 md:left-3 bg-black bg-opacity-70 text-white p-1.5 md:p-2 rounded-full backdrop-blur-sm opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity duration-200">
                        <svg class="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                        </svg>
                      </div>

                      <!-- Navigation Arrows (hidden if only one image) -->
                      <button
                        id="prev-image"
                        class="absolute left-2 md:left-3 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-2 md:p-3 rounded-full transition-all duration-200 hidden backdrop-blur-sm"
                        onclick="previousImage()"
                      >
                        <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                      </button>

                      <button
                        id="next-image"
                        class="absolute right-2 md:right-3 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-2 md:p-3 rounded-full transition-all duration-200 hidden backdrop-blur-sm"
                        onclick="nextImage()"
                      >
                        <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Thumbnail Gallery (hidden if only one image) -->
                  <div id="thumbnail-gallery" class="flex justify-center space-x-2 md:space-x-3 overflow-x-auto pb-2 px-2 hidden">
                    <!-- Thumbnails will be populated by JavaScript -->
                  </div>
                </div>
              </div>
            )}

            <!-- Description -->
            <div class="mb-4 md:mb-6">
              <h2 class="text-base md:text-lg font-semibold text-slate-900 mb-2">Description</h2>
              <div class="prose prose-slate max-w-none">
                <p class="text-slate-700 leading-relaxed whitespace-pre-wrap text-sm md:text-base">{post.description}</p>
              </div>
            </div>

            <!-- Category-specific data -->
            {post.category_specific_data && (
              <div class="bg-slate-50 rounded-lg p-3 md:p-4 mb-4">
                <h3 class="font-semibold text-slate-900 mb-3 text-sm md:text-base">
                  {post.category === 'housing' && 'Property Details'}
                  {post.category === 'jobs' && 'Job Details'}
                  {post.category === 'buy-sell' && 'Item Details'}
                  {post.category === 'community' && 'Event Details'}
                </h3>
                <div class="space-y-3">
                  {Object.entries(post.category_specific_data).map(([key, value]) => {
                    // Skip listing_type and CV fields as they're already shown elsewhere
                    if (key === 'listing_type' || key === 'job_listing_type' || key === 'cv_url' || key === 'cv_filename' || key === 'cv_size') return null;

                    // Create user-friendly labels
                    const friendlyLabels = {
                      housing_type: 'Property Type',
                      bedrooms: 'Bedrooms',
                      bathrooms: 'Bathrooms',
                      furnished: 'Furnished',
                      job_type: 'Job Type',
                      experience_level: 'Experience Level',
                      remote_ok: 'Remote Work',
                      item_category: 'Category',
                      condition: 'Condition',
                      brand: 'Brand',
                      event_type: 'Event Type',
                      event_date: 'Date',
                      event_time: 'Time',
                      event_location: 'Location'
                    } as any;

                    const label = friendlyLabels[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const displayValue = Array.isArray(value) ? value.join(', ') : value;

                    return (
                      <div class="flex flex-col sm:flex-row sm:justify-between py-2 border-b border-slate-200 last:border-b-0">
                        <span class="text-sm text-slate-600 font-medium mb-1 sm:mb-0">{label}:</span>
                        <span class="text-sm font-semibold text-slate-900 sm:text-right">{displayValue}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            <!-- CV/Portfolio Section for Jobs -->
            {post.category === 'jobs' && post.category_specific_data?.cv_url && (
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                  <h3 class="font-semibold text-blue-900 text-sm md:text-base flex items-center">
                    <span class="mr-2">📄</span>
                    CV / Portfolio
                  </h3>
                  <a 
                    href={post.category_specific_data.cv_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200"
                  >
                    Download
                  </a>
                </div>
                
                <!-- File Info - Compact -->
                <div class="flex items-center space-x-2 mb-3 text-xs text-slate-600">
                  <span class="truncate font-medium">
                    {post.category_specific_data.cv_filename || 'CV/Portfolio.pdf'}
                  </span>
                  {post.category_specific_data.cv_size && (
                    <span class="text-slate-500">
                      • {(post.category_specific_data.cv_size / 1024 / 1024).toFixed(1)} MB
                    </span>
                  )}
                </div>

                <!-- Embedded PDF Viewer - Compact -->
                <div class="bg-white rounded border border-blue-200 overflow-hidden">
                  <div class="relative h-48 sm:h-64 md:h-80">
                    <iframe
                      src={`${post.category_specific_data.cv_url}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                      class="w-full h-full border-0"
                      title="CV/Portfolio Preview"
                      loading="lazy"
                      onload="handlePDFLoad(this)"
                      onerror="handlePDFError(this)"
                    ></iframe>
                    <!-- Fallback for browsers that don't support PDF embedding -->
                    <div class="absolute inset-0 flex items-center justify-center bg-slate-100 hidden" id="pdf-fallback">
                      <div class="text-center p-4">
                        <div class="text-3xl mb-2">📄</div>
                        <p class="text-slate-600 text-sm mb-3">PDF preview not available</p>
                        <a 
                          href={post.category_specific_data.cv_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors duration-200"
                        >
                          Open PDF
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <!-- Contact Information -->
            <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-3 md:p-4">
              <h3 class="font-semibold text-emerald-900 mb-3 text-sm md:text-base">
                {post.category === 'community' && post.category_specific_data?.event_type === 'useful-links' ? 'Community Links' : 'Contact Information'}
              </h3>
              <div class="space-y-3">
                {/* Show useful links for community events */}
                {post.category === 'community' && post.category_specific_data?.event_type === 'useful-links' ? (
                  <div class="space-y-3">
                    <p class="text-sm text-emerald-700 mb-4">Join this community through the following platforms:</p>

                    <!-- Protected Social Media Links -->
                    <div class="protected-social-links space-y-2">
                      {/* WhatsApp Group Link */}
                      {post.whatsapp_group_link && (
                        <div class="social-link-item" data-link-type="whatsapp" data-link-value={post.whatsapp_group_link}>
                          <!-- Logged in users see actual link -->
                          <div class="link-authenticated hidden">
                            <a
                              href={post.whatsapp_group_link}
                              target="_blank"
                              rel="noopener noreferrer"
                              class="group flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.telegram_link && !post.facebook_link) ? '⭐ ' : ''}💬
                              </span>
                              <span class="text-sm">Join WhatsApp Group</span>
                            </a>
                          </div>

                          <!-- Non-logged in users see login prompt -->
                          <div class="link-login-required">
                            <button
                              onclick="window.authFunctions?.showAuthModal()"
                              class="group w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 cursor-pointer"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.telegram_link && !post.facebook_link) ? '⭐ ' : ''}💬
                              </span>
                              <span class="text-sm">Login to Join WhatsApp Group</span>
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Telegram Link */}
                      {post.telegram_link && (
                        <div class="social-link-item" data-link-type="telegram" data-link-value={post.telegram_link}>
                          <!-- Logged in users see actual link -->
                          <div class="link-authenticated hidden">
                            <a
                              href={post.telegram_link}
                              target="_blank"
                              rel="noopener noreferrer"
                              class="group flex items-center justify-center px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.whatsapp_group_link && !post.facebook_link) ? '⭐ ' : ''}✈️
                              </span>
                              <span class="text-sm">Join Telegram Channel</span>
                            </a>
                          </div>

                          <!-- Non-logged in users see login prompt -->
                          <div class="link-login-required">
                            <button
                              onclick="window.authFunctions?.showAuthModal()"
                              class="group w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 cursor-pointer"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.whatsapp_group_link && !post.facebook_link) ? '⭐ ' : ''}✈️
                              </span>
                              <span class="text-sm">Login to Join Telegram Channel</span>
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Facebook Group Link */}
                      {post.facebook_link && (
                        <div class="social-link-item" data-link-type="facebook" data-link-value={post.facebook_link}>
                          <!-- Logged in users see actual link -->
                          <div class="link-authenticated hidden">
                            <a
                              href={post.facebook_link}
                              target="_blank"
                              rel="noopener noreferrer"
                              class="group flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.whatsapp_group_link && !post.telegram_link) ? '⭐ ' : ''}📘
                              </span>
                              <span class="text-sm">Join Facebook Group</span>
                            </a>
                          </div>

                          <!-- Non-logged in users see login prompt -->
                          <div class="link-login-required">
                            <button
                              onclick="window.authFunctions?.showAuthModal()"
                              class="group w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 cursor-pointer"
                            >
                              <span class="mr-2 text-lg group-hover:scale-110 transition-transform">
                                {(!post.whatsapp_group_link && !post.telegram_link) ? '⭐ ' : ''}📘
                              </span>
                              <span class="text-sm">Login to Join Facebook Group</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  /* Standard contact information for other posts */
                  <ProtectedContact
                    email={post.contact_email}
                    phone={post.contact_phone}
                    whatsapp={post.contact_whatsapp}
                    preferred_contact_method={post.preferred_contact_method}
                    type="classified"
                    size="medium"
                    style="text"
                  />
                )}
              </div>
            </div>
          </div>

          <!-- Admin Controls -->
          <div id="admin-controls" class="p-4 md:p-6 bg-red-50 border-t border-red-200" style="display: none;">
            <h3 class="font-semibold text-red-900 mb-4 text-base md:text-lg">Admin Controls</h3>
            <button
              id="admin-delete-post"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors text-sm md:text-base"
            >
              Delete Post
            </button>
          </div>

          <!-- Footer -->
          <div class="p-4 md:p-6 bg-slate-50 border-t border-slate-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div class="text-sm text-slate-500">
                Posted {new Date(post.created_at).toLocaleDateString()}
              </div>
              <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <a
                  href={`/${citySlug}/classifieds/${post.category}`}
                  class="inline-flex items-center text-sm text-slate-600 hover:text-slate-800 font-medium"
                >
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                  Back to {categoryInfo.name}
                </a>
                <a
                  href={`/${citySlug}/classifieds`}
                  class="inline-flex items-center text-sm text-emerald-600 hover:text-emerald-700 font-medium"
                >
                  All Classifieds
                  <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Full-Screen Image Modal -->
  <div id="image-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center">
      <!-- Close Button -->
      <button
        onclick="closeImageModal()"
        class="absolute top-4 right-4 z-10 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-3 rounded-full transition-all duration-200 backdrop-blur-sm"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Modal Image -->
      <img
        id="modal-image"
        src=""
        alt=""
        class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
      />

      <!-- Modal Navigation (for multiple images) -->
      <button
        id="modal-prev"
        onclick="modalPreviousImage()"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-4 rounded-full transition-all duration-200 backdrop-blur-sm hidden"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <button
        id="modal-next"
        onclick="modalNextImage()"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-4 rounded-full transition-all duration-200 backdrop-blur-sm hidden"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>

      <!-- Modal Image Counter -->
      <div id="modal-counter" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm hidden">
        <span id="modal-current">1</span> / <span id="modal-total">1</span>
      </div>
    </div>
  </div>
</Layout>

<style>
  /* Enhanced mobile responsiveness */
  @media (max-width: 640px) {
    .prose p {
      font-size: 0.875rem;
      line-height: 1.5;
    }
  }

  /* Improved text readability */
  .prose {
    color: #374151;
  }

  /* Better spacing for mobile */
  @media (max-width: 768px) {
    .space-y-3 > * + * {
      margin-top: 0.75rem;
    }
  }

  /* Consistent button styling */
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Smooth transitions */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }

  /* Image gallery enhancements */
  #image-gallery {
    max-width: 100%;
  }

  /* Responsive image sizing */
  @media (max-width: 640px) {
    #image-gallery .group {
      max-width: 100%;
      margin: 0 auto;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    #image-gallery .group {
      max-width: 600px;
    }
  }

  @media (min-width: 1025px) {
    #image-gallery .group {
      max-width: 700px;
    }
  }

  /* Thumbnail gallery scrolling */
  #thumbnail-gallery {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }

  #thumbnail-gallery::-webkit-scrollbar {
    height: 4px;
  }

  #thumbnail-gallery::-webkit-scrollbar-track {
    background: transparent;
  }

  #thumbnail-gallery::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
  }

  #thumbnail-gallery::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Image hover effects */
  .thumbnail-btn:hover img {
    transform: scale(1.05);
  }

  /* Modal improvements */
  #image-modal {
    backdrop-filter: blur(4px);
  }
</style>

<script is:inline define:vars={{ postId: post.id, citySlug, imageUrl: post.image_url, additionalImages: post.additional_images }}>
  // Image Gallery Functionality
  let currentImageIndex = 0;
  let allImages = [];

  function initializeImageGallery() {
    // Collect all images
    allImages = [];
    if (imageUrl) {
      allImages.push(imageUrl);
    }
    if (additionalImages && Array.isArray(additionalImages)) {
      allImages.push(...additionalImages);
    }

    console.log('Gallery initialized with images:', allImages);

    if (allImages.length <= 1) {
      // Hide navigation for single image
      document.getElementById('prev-image')?.classList.add('hidden');
      document.getElementById('next-image')?.classList.add('hidden');
      document.getElementById('thumbnail-gallery')?.classList.add('hidden');

      // Update counter
      const totalImagesSpan = document.getElementById('total-images');
      if (totalImagesSpan) {
        totalImagesSpan.textContent = allImages.length;
      }
      return;
    }

    // Show navigation for multiple images
    document.getElementById('prev-image')?.classList.remove('hidden');
    document.getElementById('next-image')?.classList.remove('hidden');
    document.getElementById('thumbnail-gallery')?.classList.remove('hidden');

    // Update total count
    const totalImagesSpan = document.getElementById('total-images');
    if (totalImagesSpan) {
      totalImagesSpan.textContent = allImages.length;
    }

    // Create thumbnails
    createThumbnails();

    // Update current image display
    updateImageDisplay();
  }

  function createThumbnails() {
    const thumbnailGallery = document.getElementById('thumbnail-gallery');
    if (!thumbnailGallery) return;

    thumbnailGallery.innerHTML = allImages.map((imageUrl, index) => `
      <button
        onclick="goToImage(${index})"
        class="thumbnail-btn flex-shrink-0 w-16 h-16 md:w-20 md:h-20 rounded-lg md:rounded-xl overflow-hidden border-2 transition-all duration-200 shadow-sm hover:shadow-md ${index === 0 ? 'border-emerald-500 ring-1 ring-emerald-200' : 'border-slate-300 hover:border-slate-400'}"
      >
        <img
          src="${imageUrl}"
          alt="Thumbnail ${index + 1}"
          class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
          loading="lazy"
        />
      </button>
    `).join('');
  }

  function updateImageDisplay() {
    const mainImage = document.getElementById('main-image');
    const currentImageSpan = document.getElementById('current-image');

    if (mainImage && allImages[currentImageIndex]) {
      mainImage.src = allImages[currentImageIndex];
      mainImage.alt = `Image ${currentImageIndex + 1}`;
    }

    if (currentImageSpan) {
      currentImageSpan.textContent = currentImageIndex + 1;
    }

    // Update thumbnail selection
    const thumbnailButtons = document.querySelectorAll('.thumbnail-btn');
    thumbnailButtons.forEach((btn, index) => {
      if (index === currentImageIndex) {
        btn.classList.remove('border-slate-200', 'hover:border-slate-300');
        btn.classList.add('border-emerald-500');
      } else {
        btn.classList.remove('border-emerald-500');
        btn.classList.add('border-slate-200', 'hover:border-slate-300');
      }
    });
  }

  window.previousImage = function() {
    if (allImages.length <= 1) return;
    currentImageIndex = (currentImageIndex - 1 + allImages.length) % allImages.length;
    updateImageDisplay();
  }

  window.nextImage = function() {
    if (allImages.length <= 1) return;
    currentImageIndex = (currentImageIndex + 1) % allImages.length;
    updateImageDisplay();
  }

  window.goToImage = function(index) {
    if (index >= 0 && index < allImages.length) {
      currentImageIndex = index;
      updateImageDisplay();
    }
  }

  // Modal functions
  window.openImageModal = function() {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const modalCurrent = document.getElementById('modal-current');
    const modalTotal = document.getElementById('modal-total');
    const modalCounter = document.getElementById('modal-counter');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    if (modal && modalImage) {
      modalImage.src = allImages[currentImageIndex];
      modalImage.alt = `Image ${currentImageIndex + 1}`;

      if (modalCurrent) modalCurrent.textContent = currentImageIndex + 1;
      if (modalTotal) modalTotal.textContent = allImages.length;

      // Show/hide navigation based on image count
      if (allImages.length > 1) {
        modalCounter?.classList.remove('hidden');
        modalPrev?.classList.remove('hidden');
        modalNext?.classList.remove('hidden');
      } else {
        modalCounter?.classList.add('hidden');
        modalPrev?.classList.add('hidden');
        modalNext?.classList.add('hidden');
      }

      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
  }

  window.closeImageModal = function() {
    const modal = document.getElementById('image-modal');
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = ''; // Restore scrolling
    }
  }

  window.modalPreviousImage = function() {
    if (allImages.length <= 1) return;
    currentImageIndex = (currentImageIndex - 1 + allImages.length) % allImages.length;
    updateModalImage();
    updateImageDisplay(); // Also update the main gallery
  }

  window.modalNextImage = function() {
    if (allImages.length <= 1) return;
    currentImageIndex = (currentImageIndex + 1) % allImages.length;
    updateModalImage();
    updateImageDisplay(); // Also update the main gallery
  }

  function updateModalImage() {
    const modalImage = document.getElementById('modal-image');
    const modalCurrent = document.getElementById('modal-current');

    if (modalImage && allImages[currentImageIndex]) {
      modalImage.src = allImages[currentImageIndex];
      modalImage.alt = `Image ${currentImageIndex + 1}`;
    }

    if (modalCurrent) {
      modalCurrent.textContent = currentImageIndex + 1;
    }
  }

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    const modal = document.getElementById('image-modal');
    const isModalOpen = modal && !modal.classList.contains('hidden');

    if (e.key === 'Escape' && isModalOpen) {
      closeImageModal();
    } else if (e.key === 'ArrowLeft') {
      if (isModalOpen) {
        modalPreviousImage();
      } else {
        previousImage();
      }
    } else if (e.key === 'ArrowRight') {
      if (isModalOpen) {
        modalNextImage();
      } else {
        nextImage();
      }
    }
  });

  // Close modal when clicking outside the image
  document.getElementById('image-modal')?.addEventListener('click', (e) => {
    if (e.target.id === 'image-modal') {
      closeImageModal();
    }
  });

  // Initialize social media link protection
  function updateSocialLinkVisibility() {
    const currentUser = window.authFunctions?.getCurrentUser();
    const isLoggedIn = !!currentUser;

    // Handle social media links
    const socialLinkItems = document.querySelectorAll('.social-link-item');
    socialLinkItems.forEach(item => {
      const authenticatedDiv = item.querySelector('.link-authenticated');
      const loginRequiredDiv = item.querySelector('.link-login-required');

      if (isLoggedIn) {
        authenticatedDiv?.classList.remove('hidden');
        loginRequiredDiv?.classList.add('hidden');
      } else {
        authenticatedDiv?.classList.add('hidden');
        loginRequiredDiv?.classList.remove('hidden');
      }
    });
  }

  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize image gallery
    initializeImageGallery();

    // Initialize social media link protection
    updateSocialLinkVisibility();

    // Listen for auth state changes
    document.addEventListener('authStateChanged', updateSocialLinkVisibility);
    // Wait for auth to be ready
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && userProfile && userProfile.role === 'administrator') {
        const adminControls = document.getElementById('admin-controls');
        if (adminControls) {
          adminControls.style.display = 'block';
        }
      } else if (currentUser && currentUser.email === '<EMAIL>') {
        // <NAME_EMAIL> even if profile isn't loaded
        const adminControls = document.getElementById('admin-controls');
        if (adminControls) {
          adminControls.style.display = 'block';
        }
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);

    // Admin delete post handler
    document.getElementById('admin-delete-post')?.addEventListener('click', async () => {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Delete attempt - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
        alert('Unauthorized - Admin access required');
        return;
      }

      try {
        const deleteButton = document.getElementById('admin-delete-post');

        // Update button to show loading state
        deleteButton.disabled = true;
        deleteButton.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
        deleteButton.classList.add('opacity-75');

        const response = await fetch('/api/admin/delete-post', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            postId: postId,
            adminUserId: currentUser.id
          })
        });

        await response.json();

        if (response.ok) {
          // Show success state
          deleteButton.innerHTML = '✅ Deleted Successfully';
          deleteButton.classList.remove('bg-red-600', 'hover:bg-red-700');
          deleteButton.classList.add('bg-green-600');

          // Redirect after brief success feedback
          setTimeout(() => {
            window.location.href = `/${citySlug}/classifieds`;
          }, 1500);
        } else {
          // Show error state
          deleteButton.innerHTML = '❌ Delete Failed';
          deleteButton.classList.remove('bg-red-600');
          deleteButton.classList.add('bg-red-800');

          // Reset after 3 seconds
          setTimeout(() => {
            deleteButton.disabled = false;
            deleteButton.innerHTML = 'Delete Post';
            deleteButton.classList.remove('bg-red-800', 'opacity-75');
            deleteButton.classList.add('bg-red-600', 'hover:bg-red-700');
          }, 3000);
        }
      } catch (error) {
        console.error('Error deleting post:', error);

        const deleteButton = document.getElementById('admin-delete-post');
        // Show error state
        deleteButton.innerHTML = '❌ Error Occurred';
        deleteButton.classList.remove('bg-red-600');
        deleteButton.classList.add('bg-red-800');

        // Reset after 3 seconds
        setTimeout(() => {
          deleteButton.disabled = false;
          deleteButton.innerHTML = 'Delete Post';
          deleteButton.classList.remove('bg-red-800', 'opacity-75');
          deleteButton.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 3000);
      }
    });
  });

  // PDF handling functions
  window.handlePDFLoad = function(iframe) {
    // PDF loaded successfully, hide fallback
    const fallback = iframe.parentElement.querySelector('#pdf-fallback');
    if (fallback) {
      fallback.classList.add('hidden');
    }
  };

  window.handlePDFError = function(iframe) {
    // PDF failed to load, show fallback
    const fallback = iframe.parentElement.querySelector('#pdf-fallback');
    if (fallback) {
      fallback.classList.remove('hidden');
    }
    iframe.style.display = 'none';
  };

  // Check if PDF is actually loading (some browsers don't trigger onerror for PDFs)
  document.addEventListener('DOMContentLoaded', function() {
    const pdfIframes = document.querySelectorAll('iframe[title="CV/Portfolio Preview"]');
    pdfIframes.forEach(iframe => {
      setTimeout(() => {
        // If iframe is still loading after 5 seconds, assume it might not work
        if (iframe.contentDocument === null) {
          // Can't access content, might be a PDF - this is normal
          return;
        }
        
        try {
          // Try to access the iframe content
          const doc = iframe.contentDocument || iframe.contentWindow.document;
          if (doc.body && doc.body.innerHTML.trim() === '') {
            // Empty content might indicate PDF loading issue
            handlePDFError(iframe);
          }
        } catch (e) {
          // Cross-origin or PDF content - this is expected and fine
        }
      }, 3000);
    });
  });
</script>
