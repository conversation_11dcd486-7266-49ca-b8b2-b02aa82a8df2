import type { APIRoute } from 'astro';
import { updateCityListingCounts } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Update all city listing counts
    const { error } = await updateCityListingCounts();

    if (error) {
      console.error('Error updating city counts:', error);
      return new Response(JSON.stringify({ error: 'Failed to update city counts' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
