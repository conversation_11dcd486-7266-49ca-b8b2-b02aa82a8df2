import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    const userId = url.searchParams.get('userId');
    
    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's classified posts
    const result = await query(`
      SELECT
        cp.id,
        cp.title,
        cp.category,
        cp.city_slug,
        cp.is_active,
        cp.created_at,
        cp.view_count,
        COALESCE(c.name, cp.city_slug) as city_name
      FROM classified_posts cp
      LEFT JOIN cities c ON cp.city_slug = c.path_slug
      WHERE cp.user_id = $1
      ORDER BY cp.created_at DESC
    `, [userId]);

    const posts = result.rows;

    // Calculate stats
    const stats = {
      total: posts?.length || 0,
      active: posts?.filter(p => p.is_active).length || 0,
      hidden: posts?.filter(p => !p.is_active).length || 0,
      totalViews: posts?.reduce((sum, p) => sum + (p.view_count || 0), 0) || 0
    };

    return new Response(JSON.stringify({ 
      posts: posts || [],
      stats 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in user classifieds API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
