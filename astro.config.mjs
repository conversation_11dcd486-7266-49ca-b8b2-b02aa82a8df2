// @ts-check
import { defineConfig } from 'astro/config';
import node from '@astrojs/node';
import tailwind from '@astrojs/tailwind';

// https://astro.build/config
export default defineConfig({
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  integrations: [
    tailwind()
  ],
  server: {
    port: 3000,
    host: true
  },
  vite: {
    server: {
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        'expatslist.org',
        'www.expatslist.org',
        '.expatslist.org'  // Allow all subdomains
      ]
    }
  }

});
