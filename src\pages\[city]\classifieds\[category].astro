---
import Layout from '../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../components/MinimalAcctButton.astro';
import Pagination from '../../../components/Pagination.astro';
import { getCityBySlug } from '../../../lib/database';
import { getClassifiedPosts, CLASSIFIED_CATEGORIES, timeAgo, formatPrice, formatDualPrice } from '../../../lib/classifieds';
import { getCurrencyForCountry } from '../../../lib/currencies';
import Breadcrumb from '../../../components/Breadcrumb.astro';

const { city: citySlug, category } = Astro.params;

if (!citySlug || !category) {
  return Astro.redirect('/');
}

// Validate category
if (!CLASSIFIED_CATEGORIES[category as keyof typeof CLASSIFIED_CATEGORIES]) {
  return Astro.redirect(`/${citySlug}/classifieds`);
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get local currency for the city's country
const localCurrency = getCurrencyForCountry(city.country || 'Mexico');

// Pagination setup
const url = new URL(Astro.request.url);
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 12; // Optimal for mobile and desktop

// Get classified posts for this category with pagination
const offset = (currentPage - 1) * itemsPerPage;
const { data: posts } = await getClassifiedPosts(citySlug, category, itemsPerPage, offset);

// Get total count for pagination
const { data: totalCountResult } = await getClassifiedPosts(citySlug, category, 1000); // Get all to count
const totalPosts = totalCountResult?.length || 0;
const totalPages = Math.ceil(totalPosts / itemsPerPage);

const categoryInfo = CLASSIFIED_CATEGORIES[category as keyof typeof CLASSIFIED_CATEGORIES];
const pageTitle = `${categoryInfo.name} - ${city.name} Classifieds`;
const pageDescription = `Browse ${categoryInfo.name.toLowerCase()} listings in ${city.name}. ${categoryInfo.description}`;
---

<Layout title={pageTitle} description={pageDescription}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Mobile-Optimized Header -->
    <div class="bg-white border-b border-slate-200 py-4 sm:py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <Breadcrumb
          className="mb-3 sm:mb-4"
          items={[
            { label: 'Main', href: '/', icon: '🌎' },
            { label: city.name, href: `/${citySlug}` },
            { label: 'Classifieds', href: `/${citySlug}/classifieds` },
            { label: categoryInfo.name, isActive: true }
          ]}
        />

        <!-- Mobile-First Layout -->
        <div class="flex items-center justify-between">
          <!-- Left: Icon + Title (Compact) -->
          <div class="flex items-center space-x-3 flex-1 min-w-0">
            <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0">
              <span class="text-xl sm:text-2xl">{categoryInfo.icon}</span>
            </div>
            <div class="min-w-0 flex-1">
              <h1 class="text-xl sm:text-2xl font-bold text-slate-900 truncate">{categoryInfo.name}</h1>
              <!-- Hide description on mobile, show on larger screens -->
              <p class="hidden sm:block text-slate-600 text-sm">{categoryInfo.shortDesc}</p>
            </div>
          </div>

          <!-- Right: Post Ad Button (Compact on Mobile) -->
          <a
            href={`/${citySlug}/classifieds/create?category=${category}`}
            class="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-lg font-semibold transition-all duration-200 flex items-center shadow-sm hover:shadow-md flex-shrink-0 ml-3"
          >
            <span class="mr-1 sm:mr-2 text-lg sm:text-base">+</span>
            <span class="text-sm sm:text-base">Post Ad</span>
          </a>
        </div>

        <!-- Mobile: Show short description below on mobile only -->
        <div class="sm:hidden mt-2">
          <p class="text-xs text-slate-600">{categoryInfo.shortDesc}</p>
        </div>
      </div>
    </div>

    <!-- Listings -->
    <div class="py-4 sm:py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        {posts && posts.length > 0 ? (
          <div class="bg-white border border-slate-200 rounded-lg overflow-hidden">
            {posts.map((post: any) => (
              <div class="border-b border-slate-200 last:border-b-0 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 hover:border-emerald-200 hover:shadow-lg transition-all duration-300 group cursor-pointer relative" onclick={`window.location.href='/${citySlug}/classifieds/post/${post.id}'`}>
                <div class="p-2 sm:p-3">
                  <!-- Optimized Text-Only Layout -->
                  <div class="flex items-start gap-2">
                    <!-- Left: Category Icon Only -->
                    <div class="flex-shrink-0">
                      <div class="w-8 h-8 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-lg flex items-center justify-center">
                        <span class="text-lg">{categoryInfo.icon}</span>
                      </div>
                    </div>

                    <!-- Center: Content (Full Width) -->
                    <div class="flex-1 min-w-0">
                      <!-- Title Row -->
                      <div class="flex items-start justify-between gap-2 mb-1">
                        <a
                          href={`/${citySlug}/classifieds/post/${post.id}`}
                          class="font-bold text-slate-900 group-hover:text-emerald-700 transition-colors text-base leading-tight truncate flex-1 hover:underline"
                        >
                          {post.title}
                        </a>

                        <!-- Event Type Badge -->
                        {post.category === 'community' && post.category_specific_data?.event_type && (
                          <span class={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-bold flex-shrink-0 ${
                            post.category_specific_data.event_type === 'useful-links' ? 'bg-emerald-100 text-emerald-800' :
                            post.category_specific_data.event_type === 'meetup' ? 'bg-blue-100 text-blue-800' :
                            post.category_specific_data.event_type === 'event' ? 'bg-purple-100 text-purple-800' :
                            'bg-orange-100 text-orange-800'
                          }`}>
                            {post.category_specific_data.event_type === 'useful-links' && '🔗 Links'}
                            {post.category_specific_data.event_type === 'meetup' && '👥 Meetup'}
                            {post.category_specific_data.event_type === 'event' && '📅 Event'}
                            {post.category_specific_data.event_type === 'discussion' && '💬 Discussion'}
                          </span>
                        )}
                      </div>

                      <!-- Category-Specific Type Information -->
                      <div class="flex items-center gap-2 mb-1">
                        {/* Housing: Listing Type + Property Type */}
                        {post.category === 'housing' && (
                          <div class="flex items-center gap-2 text-xs">
                            {post.category_specific_data?.listing_type && (
                              <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800 font-medium">
                                {(post.category_specific_data.listing_type === 'rent' || post.category_specific_data.listing_type === 'rental') && '🏠 For Rent'}
                                {post.category_specific_data.listing_type === 'sale' && '🏡 For Sale'}
                                {post.category_specific_data.listing_type === 'roommate' && '👥 Roommate Wanted'}
                              </span>
                            )}
                            {post.category_specific_data?.property_type && (
                              <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                                {post.category_specific_data.property_type}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Jobs: Listing Type + Job Type */}
                        {post.category === 'jobs' && (
                          <div class="flex items-center gap-2 text-xs">
                            {post.category_specific_data?.job_listing_type && (
                              <span class="px-2 py-1 rounded-full bg-purple-100 text-purple-800 font-medium">
                                {post.category_specific_data.job_listing_type === 'job_offered' && '💼 Job Available'}
                                {post.category_specific_data.job_listing_type === 'looking_for_work' && '🔍 Seeking Work'}
                              </span>
                            )}
                            {post.category_specific_data?.job_type && (
                              <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                                {post.category_specific_data.job_type}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Buy & Sell: Listing Type + Item Category */}
                        {post.category === 'buy-sell' && (
                          <div class="flex items-center gap-2 text-xs">
                            {post.category_specific_data?.listing_type && (
                              <span class="px-2 py-1 rounded-full bg-green-100 text-green-800 font-medium">
                                {post.category_specific_data.listing_type === 'sell' && '💰 For Sale'}
                                {post.category_specific_data.listing_type === 'buy' && '🔍 Want to Buy'}
                              </span>
                            )}
                            {post.category_specific_data?.item_category && (
                              <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                                {post.category_specific_data.item_category}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Community: Event Type (already shown in header, but can add subtype if needed) */}
                        {post.category === 'community' && post.category_specific_data?.event_subtype && (
                          <div class="flex items-center gap-2 text-xs">
                            <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                              {post.category_specific_data.event_subtype}
                            </span>
                          </div>
                        )}
                      </div>

                      <!-- Description -->
                      <p class="text-sm text-slate-600 leading-snug mb-1 line-clamp-2">
                        {post.description}
                      </p>

                      <!-- Bottom Row: Social Media Icons + Time/Price -->
                      <div class="flex items-center justify-between gap-2 text-xs">
                        <div class="flex items-center gap-1 flex-wrap">

                        </div>

                        <!-- Time + Views + Price -->
                        <div class="flex items-center gap-2 text-slate-500">
                          <span>{timeAgo(post.created_at)}</span>
                          {/* View Count - Following business directory UI pattern */}
                          {post.view_count > 0 && (
                            <div class={`flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${
                              post.view_count > 100 ? 'bg-emerald-100 text-emerald-700' :
                              post.view_count > 50 ? 'bg-blue-100 text-blue-700' :
                              'bg-slate-100 text-slate-600'
                            }`}>
                              <span>👁️</span>
                              <span>{post.view_count}</span>
                            </div>
                          )}
                          {post.category !== 'community' && (post.price_local || post.price_usd || post.price) && (
                            <span class="font-bold text-emerald-600">
                              {post.price_local || post.price_usd ?
                                formatDualPrice(post.price_local, post.price_usd, localCurrency.symbol, localCurrency.code) :
                                formatPrice(post.price)
                              }
                            </span>
                          )}
                        </div>
                      </div>

                      <!-- Community Links (Compact) -->
                      {post.category === 'community' && (post.whatsapp_group_link || post.telegram_link || post.facebook_link || post.contact_email || post.contact_phone || post.contact_whatsapp) && (
                        <div class="mt-1 flex flex-wrap gap-1">
                          {/* Community Links */}
                          {post.whatsapp_group_link && (
                            <div class="protected-contact-item" data-contact-type="whatsapp" data-contact-value={post.whatsapp_group_link}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={post.whatsapp_group_link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-colors"
                                >
                                  💬 WhatsApp
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 WhatsApp
                                </button>
                              </div>
                            </div>
                          )}

                          {post.telegram_link && (
                            <div class="protected-contact-item" data-contact-type="telegram" data-contact-value={post.telegram_link}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={post.telegram_link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors"
                                >
                                  ✈️ Telegram
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 Telegram
                                </button>
                              </div>
                            </div>
                          )}

                          {post.facebook_link && (
                            <div class="protected-contact-item" data-contact-type="facebook" data-contact-value={post.facebook_link}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={post.facebook_link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  class="inline-flex items-center px-2 py-1 bg-blue-700 hover:bg-blue-800 text-white text-xs font-medium rounded transition-colors"
                                >
                                  📘 Facebook
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 Facebook
                                </button>
                              </div>
                            </div>
                          )}

                          {/* Contact Info */}
                          {post.contact_email && (
                            <div class="protected-contact-item" data-contact-type="email" data-contact-value={post.contact_email}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={`mailto:${post.contact_email}`}
                                  class="inline-flex items-center px-2 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium rounded transition-colors"
                                >
                                  📧 Email
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 Email
                                </button>
                              </div>
                            </div>
                          )}

                          {post.contact_phone && (
                            <div class="protected-contact-item" data-contact-type="phone" data-contact-value={post.contact_phone}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={`tel:${post.contact_phone}`}
                                  class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors"
                                >
                                  📞 Call
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 Phone
                                </button>
                              </div>
                            </div>
                          )}

                          {post.contact_whatsapp && (
                            <div class="protected-contact-item" data-contact-type="whatsapp" data-contact-value={post.contact_whatsapp}>
                              <div class="contact-authenticated hidden">
                                <a
                                  href={`https://wa.me/${post.contact_whatsapp.replace(/[^0-9]/g, '')}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-colors"
                                >
                                  💬 Contact
                                </a>
                              </div>
                              <div class="contact-login-required">
                                <button
                                  onclick="window.authFunctions?.showLoginModal()"
                                  class="inline-flex items-center px-2 py-1 bg-slate-300 text-slate-600 text-xs font-medium rounded cursor-pointer hover:bg-slate-400 transition-colors"
                                >
                                  🔒 Contact
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <!-- Right: Admin Button -->
                    <div class="flex-shrink-0">
                      <button
                        class="admin-delete-btn hidden px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                        data-post-id={post.id}
                        data-post-title={post.title}
                        onclick="deleteClassifiedPost(this)"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <!-- Empty State -->
          <div class="text-center py-16">
            <div class="text-6xl mb-6">{categoryInfo.icon}</div>
            <h2 class="text-2xl font-bold text-slate-900 mb-4">
              No {categoryInfo.name.toLowerCase()} listings yet
            </h2>
            <p class="text-slate-600 mb-8 max-w-md mx-auto">
              Be the first to post in this category! Help build the expat community in {city.name}.
            </p>
            <a
              href={`/${citySlug}/classifieds/create?category=${category}`}
              class="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200"
            >
              <span class="mr-2">+</span>
              Post First Listing
            </a>
          </div>
        )}

        <!-- World-Class Pagination -->
        {posts && posts.length > 0 && (
          <div class="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              baseUrl={`/${citySlug}/classifieds/${category}`}
              totalItems={totalPosts}
              itemsPerPage={itemsPerPage}
              itemName={`${categoryInfo.name.toLowerCase()} listings in ${city.name}`}
            />
          </div>
        )}
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug, category }}>
  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', () => {
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Category Classifieds Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {
        console.log('Admin detected, showing classified delete buttons');
        // Show all admin delete buttons
        const deleteButtons = document.querySelectorAll('.admin-delete-btn');
        deleteButtons.forEach(btn => {
          btn.classList.remove('hidden');
        });
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);

    // Initialize protected contact visibility
    updateContactVisibility();

    // Listen for auth state changes to update contact visibility
    document.addEventListener('authStateChanged', updateContactVisibility);
  });

  // Function to update contact visibility based on auth state
  function updateContactVisibility() {
    // Wait for auth functions to be available
    if (!window.authFunctions) {
      setTimeout(updateContactVisibility, 100);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();
    const isLoggedIn = !!currentUser;

    // Update all protected contact items
    document.querySelectorAll('.protected-contact-item').forEach(item => {
      const authenticatedDiv = item.querySelector('.contact-authenticated');
      const loginRequiredDiv = item.querySelector('.contact-login-required');

      if (isLoggedIn) {
        authenticatedDiv?.classList.remove('hidden');
        loginRequiredDiv?.classList.add('hidden');
      } else {
        authenticatedDiv?.classList.add('hidden');
        loginRequiredDiv?.classList.remove('hidden');
      }
    });
  }

  // Global function to delete classified posts
  window.deleteClassifiedPost = async function(button) {
    const postId = button.getAttribute('data-post-id');

    const currentUser = window.authFunctions?.getCurrentUser();
    const userProfile = window.authFunctions?.getUserProfile();

    console.log('Delete attempt - User:', currentUser?.email, 'Profile:', userProfile?.role);

    if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
      alert('Unauthorized - Admin access required');
      return;
    }

    try {
      // Update button to show loading state
      button.disabled = true;
      button.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
      button.classList.add('opacity-75');

      const response = await fetch('/api/admin/delete-post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId: postId,
          adminUserId: currentUser.id
        })
      });

      await response.json();

      if (response.ok) {
        // Show success state briefly
        button.innerHTML = '✅ Deleted';
        button.classList.remove('bg-red-600', 'hover:bg-red-700');
        button.classList.add('bg-green-600');

        // Animate removal after brief success feedback
        setTimeout(() => {
          const postElement = button.closest('div').closest('div');
          if (postElement) {
            postElement.style.transition = 'all 0.3s ease-out';
            postElement.style.transform = 'translateX(-100%)';
            postElement.style.opacity = '0';
            setTimeout(() => postElement.remove(), 300);
          }
        }, 1000);
      } else {
        // Show error state
        button.innerHTML = '❌ Failed';
        button.classList.remove('bg-red-600');
        button.classList.add('bg-red-800');

        // Reset after 2 seconds
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = 'Delete';
          button.classList.remove('bg-red-800', 'opacity-75');
          button.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 2000);
      }
    } catch (error) {
      console.error('Error deleting post:', error);

      // Show error state
      button.innerHTML = '❌ Error';
      button.classList.remove('bg-red-600');
      button.classList.add('bg-red-800');

      // Reset after 2 seconds
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800', 'opacity-75');
        button.classList.add('bg-red-600', 'hover:bg-red-700');
      }, 2000);
    }
  };

  // Track view counts for classified posts
  function trackClassifiedView(postId) {
    fetch('/api/classifieds/track-view', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ postId })
    }).catch(error => {
      console.error('Error tracking view:', error);
    });
  }

  // Track views when posts are clicked
  document.addEventListener('DOMContentLoaded', () => {
    // Track view when user clicks on a classified post
    document.querySelectorAll('a[href*="/classifieds/post/"]').forEach(link => {
      link.addEventListener('click', () => {
        const href = link.getAttribute('href');
        const postId = href?.split('/').pop();
        if (postId) {
          trackClassifiedView(postId);
        }
      });
    });
  });
</script>


