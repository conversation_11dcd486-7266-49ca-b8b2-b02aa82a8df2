---
import Layout from '../layouts/Layout.astro';
import MinimalAcctButton from '../components/MinimalAcctButton.astro';
import SearchResults from '../components/SearchResults.astro';
import { getCategories, query } from '../lib/database';

export const prerender = false;

const url = new URL(Astro.request.url);
const searchQuery = url.searchParams.get('q') || '';

let searchResults: any[] = [];
let searchData: any = { results: [], query: {}, stats: {} };

if (searchQuery.trim()) {
  try {
    const searchUrl = new URL('/api/search/advanced', Astro.request.url);
    searchUrl.searchParams.set('q', searchQuery);
    searchUrl.searchParams.set('limit', '50'); // Increased from 20 to 50

    const response = await fetch(searchUrl.toString());
    
    if (response.ok) {
      searchData = await response.json();
      if (searchData.success) {
        searchResults = searchData.results || [];
      }
    }
  } catch (error) {
    console.error('Search error:', error);
  }
}
---

<Layout title="Search - ExpatsListMX">
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      
      <!-- Header -->
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Search</h1>
        <MinimalAcctButton />
      </div>

      <!-- Enhanced Search Form -->
      <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <form method="GET" action="/search" id="search-form">
          <div class="flex gap-4">
            <div class="flex-1 relative">
              <input
                type="text"
                name="q"
                id="search-input"
                value={searchQuery}
                placeholder="Search businesses, classifieds, or services..."
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                autocomplete="off"
              />
              <!-- Search Suggestions -->
              <div id="search-suggestions" class="hidden absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-64 overflow-y-auto">
                <!-- Suggestions will be populated by JavaScript -->
              </div>
            </div>
            <button
              type="submit"
              class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔍 Search
            </button>
          </div>
        </form>
      </div>

      <!-- Results -->
      {searchQuery ? (
        <div class="space-y-6">
          <h2 class="text-xl font-semibold">
            Search Results for "{searchQuery}"
          </h2>
          
          <SearchResults 
            results={searchResults} 
            query={searchData.query || { originalQuery: searchQuery, searchQuery: searchQuery, extractedLocation: null }} 
            stats={searchData.stats || { businessCount: 0, classifiedCount: 0, totalResults: searchResults.length }} 
          />
        </div>
      ) : (
        <div class="text-center py-12">
          <h2 class="text-xl font-semibold mb-4">Search Tips</h2>
          <p class="text-gray-600">Enter a search term to find businesses and classifieds.</p>
        </div>
      )}
      
    </div>
  </div>

  <!-- Search Suggestions JavaScript -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const searchInput = document.getElementById('search-input');
      const suggestionsDiv = document.getElementById('search-suggestions');
      let suggestionTimeout;

      if (searchInput && suggestionsDiv) {
        searchInput.addEventListener('input', (e) => {
          const query = e.target.value.trim();
          clearTimeout(suggestionTimeout);

          if (query.length < 2) {
            suggestionsDiv.classList.add('hidden');
            return;
          }

          suggestionTimeout = setTimeout(async () => {
            try {
              const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}&limit=8`);
              const data = await response.json();

              if (data.suggestions && data.suggestions.length > 0) {
                suggestionsDiv.innerHTML = data.suggestions.map(suggestion => `
                  <div class="px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 flex items-center space-x-3"
                       onclick="selectSuggestion('${suggestion.text.replace(/'/g, "\\'")}')">
                    <span class="text-lg">${suggestion.icon}</span>
                    <div class="flex-1">
                      <div class="text-sm font-medium text-gray-900">${suggestion.text}</div>
                      <div class="text-xs text-gray-500">${suggestion.type} • ${suggestion.count} result${suggestion.count !== 1 ? 's' : ''}</div>
                    </div>
                  </div>
                `).join('');
                suggestionsDiv.classList.remove('hidden');
              } else {
                suggestionsDiv.classList.add('hidden');
              }
            } catch (error) {
              console.error('Error fetching suggestions:', error);
              suggestionsDiv.classList.add('hidden');
            }
          }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
          if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            suggestionsDiv.classList.add('hidden');
          }
        });
      }
    });

    // Global function for suggestion selection
    window.selectSuggestion = function(text) {
      const searchInput = document.getElementById('search-input');
      const suggestionsDiv = document.getElementById('search-suggestions');

      if (searchInput) {
        searchInput.value = text;
        suggestionsDiv.classList.add('hidden');
        document.getElementById('search-form').submit();
      }
    };
  </script>
</Layout>