---
export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: string;
  isActive?: boolean;
}

export interface Props {
  items: BreadcrumbItem[];
  className?: string;
  sticky?: boolean;
}

const { items, className = '', sticky = false } = Astro.props;
---

<div class={sticky ? 'bg-white border-b border-slate-200 sticky top-0 z-40 shadow-sm' : ''}>
  <div class={sticky ? 'max-w-7xl mx-auto px-4 sm:px-6 py-4' : ''}>
    <nav class={`flex items-center space-x-2 text-sm text-slate-600 overflow-x-auto ${className}`}>
      {items.map((item, index) => (
        <>
          {item.href ? (
            <a
              href={item.href}
              class="hover:text-blue-600 transition-colors font-medium px-2 py-1 rounded-md hover:bg-blue-50 flex items-center whitespace-nowrap"
            >
              {item.icon && <span class="mr-1">{item.icon}</span>}
              {item.label}
            </a>
          ) : (
            <span class={`font-semibold px-2 py-1 rounded-md truncate max-w-xs whitespace-nowrap ${item.isActive ? 'text-blue-600 bg-blue-50' : 'text-slate-800'}`}>
              {item.icon && <span class="mr-1">{item.icon}</span>}
              {item.label}
            </span>
          )}

          {index < items.length - 1 && (
            <span class="text-slate-400 text-sm">›</span>
          )}
        </>
      ))}
    </nav>
  </div>
</div>
