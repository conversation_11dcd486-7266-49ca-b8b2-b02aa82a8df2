@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    line-height: 1.6;
    background-color: #f3f4f6;
  }
}

@layer components {
  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-white to-indigo-50;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* City tile utilities */
  .city-tile {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    aspect-ratio: 4/3;
  }

  .city-tile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%);
    z-index: 1;
    transition: opacity 0.3s ease;
  }

  .city-tile:hover::before {
    opacity: 0.7;
  }

  /* Mobile-optimized touch targets */
  @media (max-width: 768px) {
    .btn-primary, .btn-secondary {
      @apply px-6 py-4 text-base;
      min-height: 44px;
    }

    .card, .card-premium {
      @apply p-4;
    }

    .city-tile {
      aspect-ratio: 3/2;
    }

    /* Mobile-specific home page optimizations */
    .gradient-bg {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }

    /* Compact mobile spacing */
    .mobile-compact {
      @apply space-y-2;
    }

    /* Mobile search optimization */
    input[type="text"] {
      font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Mobile city tiles optimization */
    .city-card-mobile {
      min-height: 200px;
    }

    /* Business listing mobile optimizations */
    .business-card-mobile {
      min-height: 140px;
      padding: 1rem;
    }

    .business-card-mobile h3 {
      font-size: 1.125rem;
      line-height: 1.3;
    }

    .business-card-mobile .contact-pills {
      gap: 0.375rem;
    }

    .business-card-mobile .feature-pills {
      gap: 0.25rem;
    }

    /* Improved touch targets for contact buttons */
    .protected-contact-item button,
    .protected-contact-item a {
      min-height: 32px;
      min-width: 32px;
      padding: 0.375rem 0.5rem;
    }

    /* Better text contrast on mobile */
    .business-card-mobile .text-shadow {
      text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    }
  }

  /* Small mobile devices */
  @media (max-width: 480px) {
    .city-card-mobile {
      min-height: 180px;
    }

    /* Even more compact spacing for very small screens */
    .mobile-xs-compact {
      @apply space-y-1;
    }

    /* Extra small screen business card optimizations */
    .business-card-mobile {
      min-height: 120px;
      padding: 0.75rem;
    }

    .business-card-mobile h3 {
      font-size: 1rem;
      line-height: 1.2;
    }

    .business-card-mobile .business-photo {
      width: 3rem;
      height: 3rem;
    }

    /* Simplified layout for very small screens */
    .business-card-mobile .feature-pills {
      display: none; /* Hide feature pills on very small screens */
    }

    .business-card-mobile .engagement-stats {
      font-size: 0.625rem;
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Keyframe animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
