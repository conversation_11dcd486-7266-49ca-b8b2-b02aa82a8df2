{"name": "expatslist-frontend", "type": "module", "version": "0.0.1", "engines": {"node": ">=18.20.8"}, "scripts": {"dev": "astro dev", "start": "astro preview --host", "build": "astro build", "preview": "astro preview --host", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.2.2", "@astrojs/tailwind": "^6.0.2", "@supabase/supabase-js": "^2.49.9", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.15.2", "astro": "^5.8.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "flag-icons": "^7.3.2", "lucide-react": "^0.511.0", "pg": "^8.16.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@astrojs/check": "^0.9.4", "typescript": "^5.6.3"}}