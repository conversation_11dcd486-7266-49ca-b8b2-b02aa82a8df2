---
interface Props {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  className?: string;
  showSummary?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
  itemName?: string;
}

const {
  currentPage,
  totalPages,
  baseUrl,
  className = '',
  showSummary = true,
  totalItems = 0,
  itemsPerPage = 12,
  itemName = 'items'
} = Astro.props;

// Calculate display range
const startIndex = (currentPage - 1) * itemsPerPage;
const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

// Generate page URL
function getPageUrl(page: number): string {
  const url = new URL(baseUrl, 'http://localhost');
  if (page > 1) {
    url.searchParams.set('page', page.toString());
  }
  return url.pathname + url.search;
}
---

{totalPages > 1 && (
  <div class={`space-y-6 ${className}`}>
    <!-- Results Summary -->
    {showSummary && totalItems > 0 && (
      <div class="bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 rounded-xl p-4 sm:p-6">
        <div class="text-center">
          <p class="text-slate-700 text-sm sm:text-base">
            Showing <span class="font-bold text-emerald-600">{startIndex + 1}-{endIndex}</span> of <span class="font-bold text-emerald-600">{totalItems}</span> {itemName}
          </p>
          <p class="text-xs text-slate-500 mt-1">
            Page {currentPage} of {totalPages}
          </p>
        </div>
      </div>
    )}

    <!-- World-Class Pagination -->
    <div class="flex justify-center">
      <div class="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-4 sm:p-6">
        <div class="flex items-center justify-center space-x-2 sm:space-x-4">
          
          <!-- First Page Button -->
          {currentPage > 2 && (
            <a
              href={getPageUrl(1)}
              class="hidden sm:flex items-center justify-center w-10 h-10 bg-slate-100 hover:bg-slate-200 text-slate-600 hover:text-slate-800 rounded-lg transition-all duration-200 font-medium"
              title="First page"
            >
              ««
            </a>
          )}

          <!-- Previous Button -->
          {currentPage > 1 && (
            <a
              href={getPageUrl(currentPage - 1)}
              class="flex items-center justify-center px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <span class="mr-1 sm:mr-2">←</span>
              <span class="hidden sm:inline">Previous</span>
              <span class="sm:hidden">Prev</span>
            </a>
          )}

          <!-- Page Numbers (Smart Display) -->
          <div class="flex items-center space-x-1 sm:space-x-2">
            {(() => {
              const showPages = 5;
              let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
              let endPage = Math.min(totalPages, startPage + showPages - 1);
              
              // Adjust if we're near the end
              if (endPage - startPage < showPages - 1) {
                startPage = Math.max(1, endPage - showPages + 1);
              }

              return Array.from({ length: endPage - startPage + 1 }, (_, index) => {
                const pageNum = startPage + index;
                const isActive = pageNum === currentPage;
                return (
                  <a
                    href={getPageUrl(pageNum)}
                    class={`flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-lg font-semibold transition-all duration-200 ${
                      isActive 
                        ? 'bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg transform scale-110' 
                        : 'bg-slate-100 hover:bg-emerald-100 text-slate-600 hover:text-emerald-700 hover:scale-105'
                    }`}
                  >
                    {pageNum}
                  </a>
                );
              });
            })()}
          </div>

          <!-- Next Button -->
          {currentPage < totalPages && (
            <a
              href={getPageUrl(currentPage + 1)}
              class="flex items-center justify-center px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <span class="hidden sm:inline">Next</span>
              <span class="sm:hidden">Next</span>
              <span class="ml-1 sm:ml-2">→</span>
            </a>
          )}

          <!-- Last Page Button -->
          {currentPage < totalPages - 1 && (
            <a
              href={getPageUrl(totalPages)}
              class="hidden sm:flex items-center justify-center w-10 h-10 bg-slate-100 hover:bg-slate-200 text-slate-600 hover:text-slate-800 rounded-lg transition-all duration-200 font-medium"
              title="Last page"
            >
              »»
            </a>
          )}
        </div>

        <!-- Mobile: Quick Jump -->
        <div class="mt-4 sm:hidden">
          <div class="flex items-center justify-center space-x-2 text-xs text-slate-500">
            <span>Jump to:</span>
            <select 
              onchange={`window.location.href = '${getPageUrl(1).split('?')[0]}?page=' + this.value`}
              class="border border-slate-300 rounded px-2 py-1 text-xs bg-white focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <option value={page} selected={page === currentPage}>
                  Page {page}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
)}

<style>
  /* Ensure smooth transitions */
  a {
    transition: all 0.2s ease-in-out;
  }
  
  /* Hover effects for better UX */
  a:hover {
    transform: translateY(-1px);
  }
  
  /* Active page styling */
  .scale-110 {
    transform: scale(1.1);
  }
  
  .scale-105:hover {
    transform: scale(1.05);
  }
</style>
