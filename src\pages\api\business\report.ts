import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    const { businessId, reportType, details, userEmail } = data;

    // Validate required fields
    if (!businessId || !reportType || !details) {
      return new Response(JSON.stringify({
        error: 'Missing required fields: businessId, reportType, details'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get business information for the report
    const businessResult = await query(`
      SELECT business_name, slug, city_id 
      FROM listings 
      WHERE id = $1
    `, [businessId]);

    if (businessResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Business not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const business = businessResult.rows[0];

    // Create a support request for the business report
    const subject = `Business Report: ${reportType} - ${business.business_name}`;
    const message = `Business Report Details:

Business: ${business.business_name}
Business ID: ${businessId}
Report Type: ${reportType}
Reporter Email: ${userEmail || 'Anonymous'}

Issue Details:
${details}

This report was submitted through the business listing page and requires admin review.`;

    // Insert into support_requests table
    await query(`
      INSERT INTO support_requests (
        user_email, type, subject, message, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
    `, [
      userEmail || '<EMAIL>',
      'business_correction',
      subject,
      message,
      'open'
    ]);

    return new Response(JSON.stringify({
      success: true,
      message: 'Report submitted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error submitting business report:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
