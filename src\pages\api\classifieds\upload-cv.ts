import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';

// Create a dedicated client for server-side storage operations
const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const POST: APIRoute = async ({ request }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('cv') as File;
    const userId = formData.get('userId') as string;

    if (!file || !userId) {
      return new Response(JSON.stringify({
        error: 'Please select a CV/Portfolio file to upload and make sure you\'re logged in.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    if (!allowedTypes.includes(file.type)) {
      return new Response(JSON.stringify({
        error: 'Please upload a PDF, DOC, or DOCX file for your CV/Portfolio.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file size (max 5MB for documents)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return new Response(JSON.stringify({
        error: 'File too large! Please use files under 5MB. Try compressing your document.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Convert file to buffer for upload
    const fileBuffer = await file.arrayBuffer();

    // Generate unique filename
    const fileExt = file.name.split('.').pop() || 'pdf';
    const fileName = `cv_${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExt}`;

    // Upload to Supabase Storage (use existing classified-images bucket)
    const { data, error } = await supabase.storage
      .from('classified-images')
      .upload(fileName, fileBuffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      return new Response(JSON.stringify({
        error: `Failed to upload CV: ${error.message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('classified-images')
      .getPublicUrl(fileName);

    return new Response(JSON.stringify({
      success: true,
      cvUrl: publicUrl,
      fileName: fileName,
      originalName: file.name,
      fileSize: file.size,
      path: data.path
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: `Internal server error: ${error.message}`
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
