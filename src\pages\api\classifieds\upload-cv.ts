import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';

// Create a dedicated client for server-side storage operations
const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const POST: APIRoute = async ({ request }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('cv') as File;
    const userId = formData.get('userId') as string;

    if (!file || !userId) {
      return new Response(JSON.stringify({
        error: 'Please select a CV/Portfolio file to upload and make sure you\'re logged in.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    if (!allowedTypes.includes(file.type)) {
      return new Response(JSON.stringify({
        error: 'Please upload a PDF, DOC, or DOCX file for your CV/Portfolio.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file size (max 5MB for documents)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return new Response(JSON.stringify({
        error: 'File too large! Please use files under 5MB. Try compressing your document.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Basic file validation - check file headers
    const fileBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(fileBuffer);
    
    const isValidFile = (
      // PDF
      (uint8Array[0] === 0x25 && uint8Array[1] === 0x50 && uint8Array[2] === 0x44 && uint8Array[3] === 0x46) ||
      // DOC (older format)
      (uint8Array[0] === 0xD0 && uint8Array[1] === 0xCF && uint8Array[2] === 0x11 && uint8Array[3] === 0xE0) ||
      // DOCX (ZIP-based)
      (uint8Array[0] === 0x50 && uint8Array[1] === 0x4B && uint8Array[2] === 0x03 && uint8Array[3] === 0x04)
    );

    if (!isValidFile) {
      return new Response(JSON.stringify({
        error: 'Invalid file format! Please upload a valid PDF, DOC, or DOCX file.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop() || 'pdf';
    const fileName = `cv_${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExt}`;

    // Upload to Supabase Storage (create a separate bucket for CVs)
    const { data, error } = await supabase.storage
      .from('classified-documents')
      .upload(fileName, fileBuffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Storage upload error:', error);
      return new Response(JSON.stringify({
        error: `Failed to upload CV: ${error.message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('classified-documents')
      .getPublicUrl(fileName);

    return new Response(JSON.stringify({
      success: true,
      cvUrl: publicUrl,
      fileName: fileName,
      originalName: file.name,
      fileSize: file.size,
      path: data.path
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('CV upload error:', error);
    return new Response(JSON.stringify({
      error: `Internal server error: ${error.message}`
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
