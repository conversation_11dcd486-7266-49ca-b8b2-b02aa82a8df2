---
import Layout from '../layouts/Layout.astro';
import { getCities, getCategories } from '../lib/database';

// Get all cities for the dropdown
const { data: cities } = await getCities();

// Get all categories
const { data: categories } = await getCategories();

const pageTitle = 'List Your Business on ExpatsList';
const pageDescription = 'Get your business in front of thousands of expats. Join ExpatsList and connect with your target audience.';
---

<Layout title={pageTitle} description={pageDescription}>

  <main class="min-h-screen bg-gray-50 pt-16 sm:pt-12">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-50 to-indigo-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            List Your Business on <span class="text-blue-600">ExpatsList</span>
          </h1>
          <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Connect with thousands of expats looking for trusted local services.
            Get verified and stand out from the competition.
          </p>

          <!-- Benefits -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-7xl mx-auto mb-12">
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">🎯</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Targeted Audience</h3>
              <p class="text-sm text-gray-600">Reach expats who are actively looking for services like yours</p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">✅</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Get Verified</h3>
              <p class="text-sm text-gray-600">Build trust with a verified badge and professional listing</p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">📈</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Grow Your Business</h3>
              <p class="text-sm text-gray-600">Increase visibility and attract new customers</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Submit Your Business</h2>
          <p class="text-gray-600">Fill out the form below to get your business listed. We'll review your submission and get back to you within 24 hours.</p>
        </div>

        <form id="business-form" class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="business_name" class="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                id="business_name"
                name="business_name"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Your business name"
              />
            </div>

            <div>
              <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">
                Display Name
              </label>
              <input
                type="text"
                id="display_name"
                name="display_name"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Shorter name for display (optional)"
              />
            </div>
          </div>

          <!-- City and Category -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="city_id" class="block text-sm font-medium text-gray-700 mb-2">
                City *
              </label>
              <select
                id="city_id"
                name="city_id"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a city</option>
                {cities?.map((cityOption) => (
                  <option value={cityOption.id}>
                    {cityOption.name}, {cityOption.country}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label for="category_primary_id" class="block text-sm font-medium text-gray-700 mb-2">
                Primary Category *
              </label>
              <select
                id="category_primary_id"
                name="category_primary_id"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a category</option>
                {categories?.map((category) => (
                  <option value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>
          </div>

          <!-- Descriptions -->
          <div>
            <label for="description_short" class="block text-sm font-medium text-gray-700 mb-2">
              Short Description *
            </label>
            <textarea
              id="description_short"
              name="description_short"
              required
              rows="2"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brief description of your business (1-2 sentences)"
            ></textarea>
          </div>

          <div>
            <label for="description_long" class="block text-sm font-medium text-gray-700 mb-2">
              Detailed Description
            </label>
            <textarea
              id="description_long"
              name="description_long"
              rows="4"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Detailed description of your services, specialties, and what makes you unique"
            ></textarea>
          </div>

          <!-- Contact Information -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+52 ************"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div class="mt-6">
              <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">
                Website URL
              </label>
              <input
                type="url"
                id="website_url"
                name="website_url"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://yourbusiness.com"
              />
            </div>

            <div class="mt-6">
              <label for="address_full" class="block text-sm font-medium text-gray-700 mb-2">
                Full Address
              </label>
              <textarea
                id="address_full"
                name="address_full"
                rows="2"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Street address, neighborhood, city, postal code"
              ></textarea>
            </div>
          </div>

          <!-- Social Media -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Social Media (Optional)</h3>
            <p class="text-sm text-gray-600 mb-4">Add your social media profiles to help customers connect with you</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="facebook" class="block text-sm font-medium text-gray-700 mb-2">
                  Facebook
                </label>
                <input
                  type="text"
                  id="facebook"
                  name="facebook"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="facebook.com/yourbusiness or @yourbusiness"
                />
              </div>

              <div>
                <label for="instagram" class="block text-sm font-medium text-gray-700 mb-2">
                  Instagram
                </label>
                <input
                  type="text"
                  id="instagram"
                  name="instagram"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="instagram.com/yourbusiness or @yourbusiness"
                />
              </div>

              <div>
                <label for="whatsapp" class="block text-sm font-medium text-gray-700 mb-2">
                  WhatsApp Business
                </label>
                <input
                  type="tel"
                  id="whatsapp"
                  name="whatsapp"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+52 ************"
                />
              </div>

              <div>
                <label for="linkedin" class="block text-sm font-medium text-gray-700 mb-2">
                  LinkedIn
                </label>
                <input
                  type="text"
                  id="linkedin"
                  name="linkedin"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="linkedin.com/company/yourbusiness"
                />
              </div>

              <div>
                <label for="twitter" class="block text-sm font-medium text-gray-700 mb-2">
                  Twitter/X
                </label>
                <input
                  type="text"
                  id="twitter"
                  name="twitter"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="twitter.com/yourbusiness or @yourbusiness"
                />
              </div>

              <div>
                <label for="youtube" class="block text-sm font-medium text-gray-700 mb-2">
                  YouTube
                </label>
                <input
                  type="text"
                  id="youtube"
                  name="youtube"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="youtube.com/@yourbusiness"
                />
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="languages_spoken" class="block text-sm font-medium text-gray-700 mb-2">
                  Languages Spoken
                </label>
                <input
                  type="text"
                  id="languages_spoken"
                  name="languages_spoken"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="English, Spanish, French (comma separated)"
                />
              </div>

              <div>
                <label for="price_range" class="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <select
                  id="price_range"
                  name="price_range"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select price range</option>
                  <option value="$">$ - Budget-friendly</option>
                  <option value="$$">$$ - Moderate</option>
                  <option value="$$$">$$$ - Upscale</option>
                  <option value="$$$$">$$$$ - Luxury</option>
                </select>
              </div>
            </div>

            <!-- Checkboxes -->
            <div class="mt-6 space-y-3">
              <label class="flex items-center">
                <input type="checkbox" id="owner_is_expat" name="owner_is_expat" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Business owner is an expat</span>
              </label>

              <label class="flex items-center">
                <input type="checkbox" id="pet_friendly" name="pet_friendly" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Pet-friendly</span>
              </label>

              <label class="flex items-center">
                <input type="checkbox" id="kid_friendly" name="kid_friendly" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Kid-friendly</span>
              </label>
            </div>

            <div class="mt-6">
              <label for="services_offered" class="block text-sm font-medium text-gray-700 mb-2">
                Services Offered
              </label>
              <textarea
                id="services_offered"
                name="services_offered"
                rows="2"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="List your main services (comma separated)"
              ></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="border-t border-gray-200 pt-6">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-600">
                <p>By submitting, you agree to our <a href="/terms" class="text-blue-600 hover:text-blue-700">Terms of Service</a> and <a href="/privacy" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>.</p>
              </div>
              <button
                type="submit"
                class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                Submit Business
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Verification Info -->
      <div class="mt-12 bg-blue-50 rounded-xl p-8">
        <div class="text-center">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Want to Get Verified?</h3>
          <p class="text-gray-700 mb-6">
            After your listing is approved, you can upgrade to a verified listing for just <strong>$49 USD</strong> - a one-time lifetime fee.
            Verified businesses get a trust badge, priority placement, and enhanced credibility with expat customers.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div class="text-center">
              <div class="text-2xl mb-2">✅</div>
              <div class="text-sm font-medium">Verified Badge</div>
            </div>
            <div class="text-center">
              <div class="text-2xl mb-2">📸</div>
              <div class="text-sm font-medium">Custom Photos</div>
            </div>
            <div class="text-center">
              <div class="text-2xl mb-2">⭐</div>
              <div class="text-sm font-medium">Priority Placement</div>
            </div>
          </div>

          <!-- Verification Payment Button -->
          <div class="text-center mt-6">
            <button
              onclick="handleVerificationPayment()"
              class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <span class="mr-2">💳</span>
              Pay for Verification - $49 USD
            </button>
            <p class="text-xs text-gray-500 mt-2">
              Secure payment via PayPal • One-time lifetime fee
            </p>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  document.getElementById('business-form')?.addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);
    const data: any = {};

    // Convert form data to object
    for (const [key, value] of formData.entries()) {
      if (key === 'languages_spoken' || key === 'services_offered') {
        data[key] = value.toString().split(',').map(s => s.trim()).filter(s => s);
      } else if (key === 'owner_is_expat' || key === 'pet_friendly' || key === 'kid_friendly') {
        data[key] = formData.has(key);
      } else {
        data[key] = value;
      }
    }

    // Add user ID if authenticated
    if (window.authFunctions) {
      const currentUser = window.authFunctions.getCurrentUser();
      if (currentUser) {
        data.user_id = currentUser.id;
      }
    }

    try {
      const response = await fetch('/api/submit-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        let message = 'Business submitted successfully! We\'ll review it and get back to you within 24 hours.';
        if (result.hasVerificationPayment) {
          message += ' We noticed you have paid for verification - your listing will be prioritized for review and verification upon approval.';
        }
        showBusinessSuccess(message);
        (e.target as HTMLFormElement).reset();
      } else {
        showBusinessError('Error submitting business: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      showBusinessError('Error submitting business. Please try again.');
    }
  });

  // Modern feedback functions (no popups!)
  function showBusinessSuccess(message) {
    clearBusinessFeedback();
    const formContainer = document.querySelector('.max-w-2xl');
    const successDiv = document.createElement('div');
    successDiv.id = 'business-feedback';
    successDiv.className = 'mb-6 bg-green-50 border border-green-200 rounded-lg p-4 animate-slide-down';
    successDiv.innerHTML = `
      <div class="flex items-start space-x-3">
        <span class="text-green-500 text-xl flex-shrink-0">🎉</span>
        <div class="flex-1">
          <h4 class="font-semibold text-green-900 mb-1">Business Submitted!</h4>
          <p class="text-green-800 text-sm">${message}</p>
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(successDiv, formContainer.firstChild);
      successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    setTimeout(clearBusinessFeedback, 8000);
  }

  function showBusinessError(message) {
    clearBusinessFeedback();
    const formContainer = document.querySelector('.max-w-2xl');
    const errorDiv = document.createElement('div');
    errorDiv.id = 'business-feedback';
    errorDiv.className = 'mb-6 bg-red-50 border border-red-200 rounded-lg p-4 animate-slide-down';
    errorDiv.innerHTML = `
      <div class="flex items-start space-x-3">
        <span class="text-red-500 text-xl flex-shrink-0">⚠️</span>
        <div class="flex-1">
          <h4 class="font-semibold text-red-900 mb-1">Submission Failed</h4>
          <p class="text-red-800 text-sm">${message}</p>
          <button onclick="clearBusinessFeedback()" class="mt-2 text-red-600 hover:text-red-700 text-sm underline">
            Dismiss
          </button>
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(errorDiv, formContainer.firstChild);
      errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    setTimeout(clearBusinessFeedback, 10000);
  }

  function clearBusinessFeedback() {
    const feedbackDiv = document.getElementById('business-feedback');
    if (feedbackDiv) {
      feedbackDiv.style.opacity = '0';
      feedbackDiv.style.transform = 'translateY(-10px)';
      setTimeout(() => feedbackDiv.remove(), 300);
    }
  }

  // Handle verification payment
  function handleVerificationPayment() {
    // Check if user is authenticated
    if (!window.authFunctions) {
      showBusinessError('Please wait for the page to load completely and try again.');
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();

    if (!currentUser) {
      showBusinessError('Please sign in first to proceed with verification payment.');
      if (window.authFunctions.showAuthModal) {
        window.authFunctions.showAuthModal();
      }
      return;
    }

    // Show inline confirmation instead of popup
    showVerificationConfirmation(currentUser);
  }

  function showVerificationConfirmation(currentUser) {
    clearBusinessFeedback();
    const formContainer = document.querySelector('.max-w-2xl');
    const confirmDiv = document.createElement('div');
    confirmDiv.id = 'business-feedback';
    confirmDiv.className = 'mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6 animate-slide-down';
    confirmDiv.innerHTML = `
      <div class="text-center">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span class="text-2xl">💳</span>
        </div>
        <h4 class="font-semibold text-blue-900 mb-2">Business Verification Payment</h4>
        <p class="text-blue-800 text-sm mb-4">
          You will be redirected to PayPal to pay $49 USD for business verification.<br>
          This is a one-time lifetime fee. After payment, your verification request will be sent to our admin team for approval.
        </p>
        <div class="flex space-x-3 justify-center">
          <button onclick="proceedToPayPal('${currentUser.id}')" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
            Continue to PayPal
          </button>
          <button onclick="clearBusinessFeedback()" class="bg-slate-200 hover:bg-slate-300 text-slate-700 px-6 py-2 rounded-lg font-medium transition-colors">
            Cancel
          </button>
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(confirmDiv, formContainer.firstChild);
      confirmDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  function proceedToPayPal(userId) {
    // Create PayPal payment URL
    const paypalUrl = 'https://www.paypal.com/cgi-bin/webscr?' +
      'cmd=_xclick&' +
      'business=<EMAIL>&' +
      'item_name=ExpatsList Business Verification&' +
      'amount=49.00&' +
      'currency_code=USD&' +
      'return=' + encodeURIComponent(window.location.origin + '/verification-success') + '&' +
      'cancel_return=' + encodeURIComponent(window.location.origin + '/list-business') + '&' +
      'custom=' + encodeURIComponent(userId) + '&' +
      'notify_url=' + encodeURIComponent(window.location.origin + '/api/paypal-ipn');

    // Redirect to PayPal
    window.location.href = paypalUrl;
    clearBusinessFeedback();
  }

  // Make functions globally available
  window.handleVerificationPayment = handleVerificationPayment;
  window.proceedToPayPal = proceedToPayPal;
</script>

<style>
  @keyframes slide-down {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-down {
    animation: slide-down 0.4s ease-out;
  }
</style>
