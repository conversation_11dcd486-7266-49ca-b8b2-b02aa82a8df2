import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    const { businessId, userId, type } = data;

    // Validate required fields
    if (!businessId || !userId || !type) {
      return new Response(JSON.stringify({
        error: 'Missing required fields: businessId, userId, type'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate type
    if (!['thumbs_up', 'recommendation', 'report'].includes(type)) {
      return new Response(JSON.stringify({
        error: 'Invalid recommendation type'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if user already rated this business
    const existingRating = await query(`
      SELECT id FROM business_recommendations 
      WHERE user_id = $1 AND business_id = $2 AND recommendation_type = $3
    `, [userId, businessId, type]);

    if (existingRating.rows.length > 0) {
      return new Response(JSON.stringify({
        error: 'You have already rated this business'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Insert the rating
    await query(`
      INSERT INTO business_recommendations (user_id, business_id, recommendation_type, created_at)
      VALUES ($1, $2, $3, NOW())
    `, [userId, businessId, type]);

    // Update the business thumbs_up_count
    if (type === 'thumbs_up') {
      await query(`
        UPDATE listings 
        SET thumbs_up_count = thumbs_up_count + 1,
            updated_at = NOW()
        WHERE id = $1
      `, [businessId]);
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Rating submitted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error submitting business rating:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
