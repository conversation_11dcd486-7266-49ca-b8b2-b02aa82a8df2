import type { APIRoute } from 'astro';
import { incrementViewCount, incrementClickCount } from '../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId, type } = await request.json();

    if (!listingId || !type) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Handle view tracking
    if (type === 'view') {
      const { error } = await incrementViewCount(listingId);
      if (error) {
        console.error('Error tracking view:', error);
        return new Response(JSON.stringify({ error: 'Failed to track view' }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Handle click tracking using direct database connection
    const { error } = await incrementClickCount(listingId, type);

    if (error) {
      console.error('Error tracking click:', error);
      return new Response(JSON.stringify({ error: 'Failed to track click' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Invalidate relevant caches to show updated analytics immediately
    try {
      const { cache, cacheKeys } = await import('../../lib/cache');
      const { getListingById } = await import('../../lib/database');

      // Get listing details to invalidate specific caches
      const { data: listing } = await getListingById(listingId);

      if (listing) {
        // Invalidate specific caches for this listing
        cache.delete(cacheKeys.businessListing(listing.city_id, listing.category_primary_id, listing.slug));
        cache.delete(cacheKeys.listingsForSubcategory(listing.city_id, listing.category_primary_id));

        // Get parent category for category-level cache invalidation
        const { query } = await import('../../lib/database');
        const categoryResult = await query('SELECT parent_id FROM categories WHERE id = $1', [listing.category_primary_id]);
        const parentCategoryId = categoryResult.rows[0]?.parent_id;

        if (parentCategoryId) {
          cache.delete(cacheKeys.listingsForCategory(listing.city_id, parentCategoryId));
        }

        console.log('Specific caches invalidated after click tracking for listing:', listingId);
      } else {
        // Fallback: clear all listing caches if we can't get specific listing details
        cache.clear();
        console.log('All caches cleared after click tracking (fallback)');
      }
    } catch (cacheError) {
      console.error('Error invalidating cache:', cacheError);
      // Don't fail the request if cache clearing fails
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
