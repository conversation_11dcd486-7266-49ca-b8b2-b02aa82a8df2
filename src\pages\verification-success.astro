---
import Layout from '../layouts/Layout.astro';
import MinimalAcctButton from '../components/MinimalAcctButton.astro';

const pageTitle = 'Verification Payment Successful - ExpatsList';
const pageDescription = 'Your verification payment has been received. We will review your request and get back to you soon.';
---

<Layout title={pageTitle} description={pageDescription}>
  <MinimalAcctButton />

  <main class="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center">
        <!-- Success Icon -->
        <div class="w-24 h-24 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-8">
          <span class="text-4xl text-white">✅</span>
        </div>

        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Payment Successful!
        </h1>
        
        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Thank you for your verification payment. Your request has been sent to our admin team for review.
        </p>

        <!-- Status Card -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 border border-green-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-6">What Happens Next?</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Step 1 -->
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-xl">📧</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Payment Confirmed</h3>
              <p class="text-sm text-gray-600">We've received your $49 USD payment via PayPal</p>
            </div>

            <!-- Step 2 -->
            <div class="text-center">
              <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-xl">👨‍💼</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Admin Review</h3>
              <p class="text-sm text-gray-600">Our team will review your verification request within 2-3 business days</p>
            </div>

            <!-- Step 3 -->
            <div class="text-center">
              <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-xl">🏆</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Get Verified</h3>
              <p class="text-sm text-gray-600">Once approved, your business will display the verified badge</p>
            </div>
          </div>
        </div>

        <!-- Important Notes -->
        <div class="bg-blue-50 rounded-xl p-6 mb-8 border border-blue-200">
          <h3 class="text-lg font-semibold text-blue-900 mb-4">Important Information</h3>
          <div class="text-left space-y-2 text-sm text-blue-800">
            <p>• <strong>Payment Reference:</strong> Keep your PayPal receipt for your records</p>
            <p>• <strong>Processing Time:</strong> Verification typically takes 2-3 business days</p>
            <p>• <strong>Lifetime Verification:</strong> This is a one-time payment - your verification status is permanent</p>
            <p>• <strong>Contact Support:</strong> If you have questions, contact us through your account dashboard</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/account"
            class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <span class="mr-2">👤</span>
            View My Account
          </a>
          
          <a
            href="/list-business"
            class="inline-flex items-center px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl border border-gray-200"
          >
            <span class="mr-2">📝</span>
            Submit Business Listing
          </a>
        </div>

        <!-- Support Contact -->
        <div class="mt-12 text-center">
          <p class="text-gray-600 mb-4">Need help or have questions?</p>
          <a
            href="/support"
            class="text-blue-600 hover:text-blue-700 font-medium"
          >
            Contact Support →
          </a>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  // Track verification payment success
  document.addEventListener('DOMContentLoaded', () => {
    // You can add analytics tracking here if needed
    console.log('Verification payment successful');
    
    // Optional: Send success event to admin
    if (window.authFunctions) {
      const currentUser = window.authFunctions.getCurrentUser();
      if (currentUser) {
        fetch('/api/verification-payment-success', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: currentUser.id,
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('Optional tracking failed:', error);
        });
      }
    }
  });
</script>
