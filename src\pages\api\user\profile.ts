import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ url }) => {
  try {
    const userId = url.searchParams.get('id');

    if (!userId) {
      return new Response(JSON.stringify({
        error: 'User ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get user profile using direct database connection
    const result = await query(`
      SELECT 
        id, email, display_name, bio, location, role, 
        is_verified, total_posts, created_at, updated_at
      FROM user_profiles 
      WHERE id = $1 AND is_active = true
    `, [userId]);

    if (result.rows.length === 0) {
      // Profile doesn't exist yet - return null (not an error for new users)
      return new Response(JSON.stringify({
        success: true,
        profile: null
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const profile = result.rows[0];

    return new Response(JSON.stringify({
      success: true,
      profile: {
        id: profile.id,
        email: profile.email,
        display_name: profile.display_name,
        bio: profile.bio,
        location: profile.location,
        role: profile.role,
        is_verified: profile.is_verified,
        total_posts: profile.total_posts,
        created_at: profile.created_at,
        updated_at: profile.updated_at
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error loading user profile:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
