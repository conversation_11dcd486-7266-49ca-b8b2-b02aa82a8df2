import type { APIRoute } from 'astro';
import { query } from '../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userId, timestamp } = await request.json();

    if (!userId) {
      return new Response(JSON.stringify({
        error: 'User ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Log the verification payment success event
    const logQuery = `
      INSERT INTO user_activity_log (
        user_id,
        activity_type,
        activity_data,
        created_at
      ) VALUES ($1, $2, $3, $4)
    `;

    await query(logQuery, [
      userId,
      'verification_payment_success_page_viewed',
      JSON.stringify({ timestamp }),
      new Date()
    ]);

    return new Response(JSON.stringify({
      success: true
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error logging verification payment success:', error);
    
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
