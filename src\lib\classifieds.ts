import { query } from './database';

export interface ClassifiedPost {
  id: string;
  user_id: string | null;
  city_slug: string;
  category: 'housing' | 'jobs' | 'buy-sell' | 'community';
  title: string;
  description: string;
  price: number | null;
  price_local: number | null;
  price_usd: number | null;
  local_currency_code: string | null;
  local_currency_symbol: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  contact_whatsapp: string | null;
  preferred_contact_method: 'email' | 'phone' | 'whatsapp' | null;
  image_url: string | null;
  expires_at: string;
  is_active: boolean;
  view_count: number;
  created_at: string;
  updated_at: string;
  // Useful links fields for community resources
  facebook_link: string | null;
  telegram_link: string | null;
  whatsapp_group_link: string | null;
  website_link: string | null;
}

export interface ClassifiedCategoryCount {
  category: string;
  count: number;
}

// Get classified posts for a city
export async function getClassifiedPosts(
  citySlug: string,
  category?: string,
  limit: number = 20,
  offset: number = 0
) {
  try {
    let sql = `
      SELECT *
      FROM classified_posts
      WHERE city_slug = $1
        AND is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
    `;
    
    const params: any[] = [citySlug];
    
    if (category) {
      sql += ` AND category = $${params.length + 1}`;
      params.push(category);
    }
    
    sql += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);
    
    const result = await query(sql, params);
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get classified post counts by category for a city
export async function getClassifiedCategoryCounts(citySlug: string) {
  try {
    const sql = `
      SELECT
        category,
        COUNT(*) as count
      FROM classified_posts
      WHERE city_slug = $1
        AND is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
      GROUP BY category
      ORDER BY category
    `;
    
    const result = await query(sql, [citySlug]);
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get recent classified posts for homepage preview
export async function getRecentClassifiedPosts(citySlug: string, limit: number = 6) {
  try {
    const sql = `
      SELECT *
      FROM classified_posts
      WHERE city_slug = $1
        AND is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
      ORDER BY created_at DESC
      LIMIT $2
    `;
    
    const result = await query(sql, [citySlug, limit]);
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Update view count for a classified post
export async function incrementClassifiedViewCount(postId: string) {
  try {
    await query(`
      UPDATE classified_posts
      SET view_count = COALESCE(view_count, 0) + 1
      WHERE id = $1
    `, [postId]);
    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Get a single classified post by ID
export async function getClassifiedPost(id: string) {
  try {
    const sql = `
      SELECT *
      FROM classified_posts
      WHERE id = $1
        AND is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
    `;

    const result = await query(sql, [id]);
    return { data: result.rows[0] || null, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Category display information
export const CLASSIFIED_CATEGORIES = {
  housing: {
    name: 'Housing',
    icon: '🏠',
    description: 'Find apartments, rooms, houses for rent or sale. Perfect for expats looking for their next home.',
    shortDesc: 'Apartments, rooms, rentals'
  },
  jobs: {
    name: 'Jobs & Services',
    icon: '💼',
    description: 'Connect with talented expats offering professional services, find skilled help for your projects, or discover exciting work opportunities in the community.',
    shortDesc: 'Services, opportunities, talent'
  },
  'buy-sell': {
    name: 'Buy & Sell',
    icon: '🛍️',
    description: 'Buy and sell furniture, electronics, vehicles, and household items within the expat community.',
    shortDesc: 'Furniture, electronics, vehicles'
  },
  community: {
    name: 'Community & Events',
    icon: '💬',
    description: 'Join meetups, events, discussions, and get recommendations from fellow expats.',
    shortDesc: 'Meetups, events, discussions'
  }
} as const;

// Helper function to format time ago
export function timeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return date.toLocaleDateString();
}

// Helper function to format price
export function formatPrice(price: number | null): string {
  if (!price) return 'Free';
  return `$${price.toLocaleString()}`;
}

// Helper function to format dual currency price
export function formatDualPrice(
  priceLocal: number | null,
  priceUsd: number | null,
  localCurrencySymbol: string = '$',
  localCurrencyCode: string = 'MXN'
): string {
  if (!priceLocal && !priceUsd) return 'Free';

  const parts = [];

  if (priceLocal) {
    parts.push(`${localCurrencySymbol}${priceLocal.toLocaleString()} ${localCurrencyCode}`);
  }

  if (priceUsd) {
    parts.push(`$${priceUsd.toLocaleString()} USD`);
  }

  return parts.join(' / ');
}

// Get total classified count across all cities
export async function getTotalClassifiedCount() {
  try {
    const sql = `
      SELECT COUNT(*) as total
      FROM classified_posts
      WHERE is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
    `;

    const result = await query(sql, []);
    return { data: result.rows[0]?.total || 0, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: 0, error };
  }
}

export type ClassifiedCategory = keyof typeof CLASSIFIED_CATEGORIES;
