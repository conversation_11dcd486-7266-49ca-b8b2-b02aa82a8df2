---
import ProtectedContact from './ProtectedContact.astro';

interface Props {
  results: any[];
  query: any;
  stats: any;
}

const { results, query, stats } = Astro.props;
---

<!-- Search Results Header -->
<div class="mb-6">
  <div class="flex items-center justify-between mb-4">
    <div>
      <h2 class="text-2xl font-bold text-slate-900">
        Search Results
        {query?.extractedLocation && (
          <span class="text-blue-600">in {query.extractedLocation.name}</span>
        )}
      </h2>
      <p class="text-slate-600 mt-1">
        Found {stats?.totalResults || 0} results for "{query?.originalQuery || 'your search'}"
        {stats?.searchEnhanced && (
          <span class="text-emerald-600 font-medium"> (enhanced search)</span>
        )}
      </p>
      {query?.expandedTerms && (
        <p class="text-xs text-slate-500 mt-1">
          Also searching for: {query.expandedTerms.slice(1).join(', ')}
        </p>
      )}
    </div>
    
    <!-- Results Stats -->
    <div class="flex items-center space-x-4 text-sm text-slate-500">
      <div class="flex items-center space-x-1">
        <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
        <span>{stats.businessCount} Businesses</span>
      </div>
      <div class="flex items-center space-x-1">
        <span class="w-3 h-3 bg-emerald-500 rounded-full"></span>
        <span>{stats.classifiedCount} Classifieds</span>
      </div>
    </div>
  </div>  <!-- Location Context -->
  {query?.extractedLocation && (
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <span class="text-blue-800 font-medium">
          Showing results for "{query?.searchQuery || query?.originalQuery}" in {query?.extractedLocation?.name}
        </span>
      </div>
    </div>
  )}
</div>

<!-- Search Results Grid -->
<div class="space-y-4">
  {results.map((result) => (
    <div class="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden">
      {result.result_type === 'business' ? (
        <!-- Business Listing -->
        <div class="p-6">
          <div class="flex items-start space-x-4">
            <!-- Business Image -->
            <div class="flex-shrink-0">
              {result.google_photo_1 ? (
                <div class="relative">
                  <img 
                    src={`/api/image-proxy?url=${encodeURIComponent(result.google_photo_1)}`} 
                    alt={result.business_name}
                    class="w-20 h-20 rounded-lg object-cover border border-slate-200"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                  />
                  <div class="w-20 h-20 rounded-lg bg-gradient-to-br from-blue-100 to-emerald-100 flex items-center justify-center border border-slate-200" style="display: none;">
                    <span class="text-2xl">{result.category_icon || '🏢'}</span>
                  </div>
                </div>
              ) : (
                <div class="w-20 h-20 rounded-lg bg-gradient-to-br from-blue-100 to-emerald-100 flex items-center justify-center border border-slate-200">
                  <span class="text-2xl">{result.category_icon || '🏢'}</span>
                </div>
              )}
            </div>

            <!-- Business Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <!-- Business Name & Badges -->
                  <div class="flex items-center space-x-2 mb-2">
                    <h3 class="text-lg font-semibold text-slate-900 truncate">
                      <a
                        href={`/${result.city_slug}/${result.category_slug}/${result.subcategory_slug}/${result.slug}`}
                        class="hover:text-blue-600 transition-colors"
                      >
                        {result.business_name || result.display_name}
                      </a>
                    </h3>
                    <!-- Verification Badge -->
                    {result.is_verified_expatslist && (
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        Verified
                      </span>
                    )}

                    <!-- Pinned Badge -->
                    {result.is_pinned && (
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        📌 Featured
                      </span>
                    )}
                  </div>

                  <!-- Category & Location -->
                  <div class="flex items-center space-x-4 text-sm text-slate-600 mb-2">
                    <span class="flex items-center space-x-1">
                      <span>{result.category_icon || '📂'}</span>
                      <span>{result.category_name}</span>
                    </span>
                    <span class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      </svg>
                      <span>{result.city_name}</span>
                    </span>
                  </div>

                  <!-- Description -->
                  {result.description_short && (
                    <p class="text-slate-700 text-sm leading-relaxed mb-3 line-clamp-2">
                      {result.description_short}
                    </p>
                  )}

                  <!-- Contact Info - Protected -->
                  <div class="flex items-center space-x-4 text-sm">
                    <!-- Protected Contact Information -->
                    <ProtectedContact
                      phone={result.contact_info?.phone}
                      email={result.contact_info?.email}
                      whatsapp={result.contact_info?.whatsapp}
                      type="business"
                      size="small"
                      style="text"
                    />

                    {result.contact_info?.website && (
                      <a
                        href={result.contact_info.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        class="flex items-center space-x-1 text-blue-600 hover:text-blue-700"
                      >
                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        <span>Website</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <!-- Classified Listing -->
        <div class="p-6 bg-gradient-to-r from-emerald-50 to-blue-50">
          <div class="flex items-start space-x-4">
            <!-- Classified Image -->
            <div class="flex-shrink-0">
              {result.image_url ? (
                <img 
                  src={result.image_url} 
                  alt={result.title}
                  class="w-20 h-20 rounded-lg object-cover border border-slate-200"
                />
              ) : (
                <div class="w-20 h-20 rounded-lg bg-gradient-to-br from-emerald-100 to-blue-100 flex items-center justify-center border border-slate-200">
                  <span class="text-2xl">📝</span>
                </div>
              )}
            </div>

            <!-- Classified Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <!-- Title & Category -->
                  <div class="flex items-center space-x-2 mb-2">
                    <h3 class="text-lg font-semibold text-slate-900">
                      <a 
                        href={`/${result.city_slug}/classifieds/${result.category}/${result.id}`}
                        class="hover:text-emerald-600 transition-colors"
                      >
                        {result.title}
                      </a>
                    </h3>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                      Classified
                    </span>
                  </div>                  <!-- Category & Location -->
                  <div class="flex items-center space-x-4 text-sm text-slate-600 mb-2">
                    <span class="flex items-center space-x-1">
                      <span>📂</span>
                      <span class="capitalize">{result.category}</span>
                    </span>
                    <span class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      </svg>
                      <span>{result.city_name}</span>
                    </span>
                    <span class="text-slate-500">
                      {new Date(result.created_at).toLocaleDateString()}
                    </span>
                  </div>

                  <!-- Description -->
                  {result.description && (
                    <p class="text-slate-700 text-sm leading-relaxed mb-3 line-clamp-2">
                      {result.description}
                    </p>
                  )}

                  <!-- Price -->
                  {(result.price_local || result.price_usd || result.price) && (
                    <div class="flex items-center space-x-2 text-sm font-medium">
                      {result.price_local && (
                        <span class="text-emerald-600">
                          ${result.price_local} MXN
                        </span>
                      )}
                      {result.price_usd && (
                        <span class="text-blue-600">
                          ${result.price_usd} USD
                        </span>
                      )}
                      {!result.price_local && !result.price_usd && result.price && (
                        <span class="text-slate-600">
                          ${result.price}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  ))}
</div><!-- No Results -->
{results.length === 0 && (
  <div class="text-center py-12">
    <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <svg class="w-12 h-12 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
    <h3 class="text-lg font-medium text-slate-900 mb-2">No results found</h3>
    <p class="text-slate-600 mb-4">
      We couldn't find anything matching "{query.originalQuery}"
      {query.extractedLocation && ` in ${query.extractedLocation.name}`}
    </p>
    <div class="space-y-2 text-sm text-slate-500">
      <p>Try:</p>
      <ul class="space-y-1">
        <li>• Using different keywords</li>
        <li>• Checking your spelling</li>
        <li>• Searching in a different location</li>
        <li>• Using broader search terms</li>
      </ul>
    </div>
  </div>
)}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>