# 🌍 ExpatsLists - Global Expat Community Platform

**ExpatsLists** is a comprehensive platform connecting expats worldwide through local business directories and classified ads. Built with modern web technologies to provide a seamless experience for expatriate communities.

## 🎯 Project Overview

ExpatsLists serves as a digital hub for expat communities, offering:

- **🏢 Business Directory**: Discover expat-friendly businesses, services, and professionals
- **📋 Classified Ads**: Buy, sell, find housing, jobs, and community events
- **🌐 Multi-City Support**: Dedicated pages for major expat destinations
- **📱 Mobile-First Design**: Optimized for mobile devices with responsive layouts
- **🔐 User Authentication**: Secure user accounts with Supabase integration

## 🚀 Tech Stack

### **Frontend**
- **[Astro](https://astro.build/)** - Modern static site generator
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript

### **Backend & Database**
- **[Supabase](https://supabase.com/)** - Backend-as-a-Service
- **PostgreSQL** - Relational database
- **Row Level Security (RLS)** - Data protection

### **Deployment & Infrastructure**
- **Docker** - Containerization
- **Node.js 18.20.8** - Runtime environment

## 📁 Project Structure

```
expatslist/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ProtectedContact.astro
│   │   ├── MinimalAcctButton.astro
│   │   └── SearchResults.astro
│   ├── layouts/             # Page layouts
│   │   └── Layout.astro
│   ├── lib/                 # Utility functions & database
│   │   ├── database.ts      # Supabase client & queries
│   │   ├── classifieds.ts   # Classified ads logic
│   │   ├── cache.ts         # Caching utilities
│   │   └── currencies.ts    # Currency handling
│   ├── pages/               # File-based routing
│   │   ├── [city]/          # Dynamic city pages
│   │   │   ├── [category]/  # Business categories
│   │   │   └── classifieds/ # Classified ads
│   │   └── index.astro      # Homepage
│   └── styles/              # Global styles
├── public/                  # Static assets
├── migrations/              # Database migrations
├── Dockerfile              # Container configuration
└── package.json            # Dependencies & scripts
```

## 🛠️ Development Setup

### **Prerequisites**
- Node.js 18.20.8 or higher
- npm or yarn package manager
- Supabase account (for database)

### **Installation**

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd expatslist
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Configure your `.env` file with:
   ```env
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

4. **Database Setup**
   - Run migrations in `migrations/` folder in your Supabase SQL editor
   - Set up Row Level Security policies

### **Development Commands**

| Command | Action |
|---------|--------|
| `npm run dev` | Start development server at `localhost:4321` |
| `npm run build` | Build production site to `./dist/` |
| `npm run preview` | Preview production build locally |
| `npm run astro check` | Type-check the project |

## 🌟 Key Features

### **Business Directory**
- **Multi-category listings**: Restaurants, services, healthcare, etc.
- **Location-based search**: Find businesses by city/region
- **Contact protection**: Secure contact information display
- **Mobile-optimized cards**: Touch-friendly business listings

### **Classified Ads**
- **Four main categories**:
  - 🏠 **Housing**: Rentals, sales, roommates
  - 💼 **Jobs & Services**: Employment, freelance work
  - 🛍️ **Buy & Sell**: Furniture, electronics, vehicles
  - 💬 **Community & Events**: Meetups, announcements, useful links

- **Enhanced Event Types** (Recently Updated):
  - 🎉 Social Events
  - 👥 Meetups  
  - 📚 Classes/Workshops
  - 📢 Announcements
  - 🔗 **Useful Links** (NEW) - Facebook groups, Telegram channels, WhatsApp groups
  - 📝 Other

### **Community Features**
- **Useful Links Category**: Share Facebook groups, Telegram channels, WhatsApp groups
- **Enhanced Contact Methods**: Support for social media group links
- **Expat-focused**: Tailored for expatriate community needs

## 🗄️ Database Schema

### **Core Tables**
- `businesses` - Business directory listings
- `classified_posts` - Classified advertisements
- `cities` - Supported cities/locations
- `verification_payments` - Payment tracking for business verification
- `admin_notifications` - Admin alert system
- `user_activity_log` - User activity tracking

### **Key Features**
- **Row Level Security**: Data protection at database level
- **JSONB fields**: Flexible data storage for category-specific information
- **Audit trails**: Comprehensive logging and tracking

## 🚢 Deployment

### **Docker Deployment**
```bash
# Build container
docker build -t expatslist .

# Run container
docker run -p 3000:3000 expatslist
```

### **Environment Variables**
Ensure all required environment variables are set:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## 🔧 Recent Updates

### **Community Event Optimization** ✨
- **Consolidated event types** from 8+ to 6 clear categories
- **Added "Useful Links" category** for sharing community resources
- **Enhanced contact methods** with Facebook, Telegram, WhatsApp group support
- **Improved mobile UX** with better form layouts
- **Consistent experience** across create/edit forms

### **Project Cleanup** 🧹
- **Removed VS Code installation files** accidentally committed
- **Deleted unused GitHub Actions** workflow
- **Enhanced .gitignore** with proper IDE and OS file exclusions
- **Cleaned repository structure** for professional development

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is proprietary software. All rights reserved.

## 🌐 Live Demo

Visit the live platform at: [ExpatsLists.com](https://expatslists.com)

---

**Built with ❤️ for the global expat community**