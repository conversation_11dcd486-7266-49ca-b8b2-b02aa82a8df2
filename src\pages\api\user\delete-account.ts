import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.userId) {
      return new Response(JSON.stringify({
        error: 'User ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Start a transaction to ensure all deletions happen together
    const client = await query('BEGIN');

    try {
      // 1. Delete user's classified posts
      await query(`
        DELETE FROM classified_posts 
        WHERE user_id = $1
      `, [data.userId]);

      // 2. Delete user's business listings (if any)
      await query(`
        DELETE FROM listings 
        WHERE contact_info->>'user_id' = $1
      `, [data.userId]);

      // 3. Delete user profile
      await query(`
        DELETE FROM user_profiles 
        WHERE id = $1
      `, [data.userId]);

      // 4. Note: Supabase auth user will be deleted separately via Supabase client
      
      // Commit the transaction
      await query('COMMIT');

      return new Response(JSON.stringify({
        success: true,
        message: 'Account and all associated data deleted successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });

    } catch (error) {
      // Rollback on error
      await query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('Error deleting account:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete account'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
