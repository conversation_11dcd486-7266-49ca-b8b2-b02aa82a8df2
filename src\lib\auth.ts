import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM';

// Create a single shared Supabase client instance with consistent configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'expatslist-auth'
  }
});

// User roles
export type UserRole = 'classified_user' | 'business_owner' | 'administrator';

export interface UserProfile {
  id: string;
  email: string;
  display_name?: string;
  role: UserRole;
  is_verified: boolean;
  business_id?: string;
  created_at: string;
  updated_at: string;
  total_posts: number;
  is_active: boolean;
}

// Auth helper functions
export async function signInWithEmail(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (error) {
    console.error('Error signing in:', error);
    throw error;
  }
  
  return data;
}

export async function signUpWithEmail(email: string, password: string, displayName?: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        display_name: displayName
      }
    }
  });
  
  if (error) {
    console.error('Error signing up:', error);
    throw error;
  }
  
  return data;
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}

export async function resetPassword(email: string) {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`
  });

  if (error) {
    console.error('Error resetting password:', error);
    throw error;
  }

  return data;
}

export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    // Don't log session missing errors as they're normal for non-authenticated users
    if (error.message !== 'Auth session missing!') {
      console.error('Error getting current user:', error);
    }
    return null;
  }
  return user;
}

export async function deleteUser() {
  const { error } = await supabase.auth.admin.deleteUser();
  if (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}

export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) {
    // Don't log session missing errors as they're normal for non-authenticated users
    if (error.message !== 'Auth session missing!') {
      console.error('Error getting session:', error);
    }
    return null;
  }
  return session;
}

// User profile functions
export async function createUserProfile(user: any, role: UserRole = 'classified_user') {
  const { data, error } = await supabase
    .from('user_profiles')
    .upsert({
      id: user.id,
      email: user.email,
      display_name: user.user_metadata?.display_name || user.email?.split('@')[0],
      role,
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }

  return data;
}

export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .eq('is_active', true)
    .single();

  if (error && error.code !== 'PGRST116') { // Not found error is OK
    console.error('Error getting user profile:', error);
    throw error;
  }

  return data;
}

export async function updateUserProfile(userId: string, updates: Partial<UserProfile>) {
  const { data, error } = await supabase
    .from('user_profiles')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }

  return data;
}

// Check user permissions
export async function hasPermission(userId: string, permission: string): Promise<boolean> {
  const profile = await getUserProfile(userId);
  if (!profile) return false;

  switch (permission) {
    case 'post_classified':
      return profile.is_active;
    case 'manage_business':
      return profile.role === 'business_owner' || profile.role === 'administrator';
    case 'admin_access':
      return profile.role === 'administrator';
    case 'moderate_content':
      return profile.role === 'administrator';
    default:
      return false;
  }
}



// Business interaction functions
export async function addBusinessRecommendation(
  businessId: string, 
  type: 'thumbs_up' | 'recommendation' | 'report',
  comment?: string
) {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('User must be authenticated');
  }

  const { data, error } = await supabase
    .from('business_recommendations')
    .upsert({
      user_id: user.id,
      business_id: businessId,
      recommendation_type: type,
      comment: comment || null
    })
    .select()
    .single();

  if (error) {
    console.error('Error adding business recommendation:', error);
    throw error;
  }

  return data;
}

export async function getBusinessRecommendations(businessId: string) {
  const { data, error } = await supabase
    .from('business_recommendations')
    .select(`
      *,
      user_profiles!inner(display_name, role)
    `)
    .eq('business_id', businessId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error getting business recommendations:', error);
    throw error;
  }

  return data;
}

export async function getUserBusinessRecommendation(businessId: string, userId: string) {
  const { data, error } = await supabase
    .from('business_recommendations')
    .select('*')
    .eq('business_id', businessId)
    .eq('user_id', userId)
    .maybeSingle();

  if (error) {
    console.error('Error getting user business recommendation:', error);
    throw error;
  }

  return data;
}


