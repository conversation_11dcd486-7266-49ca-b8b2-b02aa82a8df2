// Weather service using Open-Meteo API
// Free weather API with no authentication required

interface WeatherData {
  temperature: number;
  weatherCode: number;
  description: string;
  icon: string;
  uvIndex: number;
  uvLevel: string;
  uvColor: string;
  airQuality: number;
  airQualityLevel: string;
  airQualityColor: string;
}

interface CityCoordinates {
  lat: number;
  lon: number;
}

// City coordinates for weather data
const cityCoordinates: Record<string, CityCoordinates> = {
  'Cancún': { lat: 21.1743, lon: -86.8466 },
  'Playa del Carmen': { lat: 20.6274, lon: -87.0739 },
  'Tulum': { lat: 20.2114, lon: -87.4654 },
  'Puerto Morelos': { lat: 20.8468, lon: -86.8721 },
  'Is<PERSON> Mujeres': { lat: 21.2311, lon: -86.7312 }
};

// Weather code to description mapping (Open-Meteo weather codes)
const weatherDescriptions: Record<number, { description: string; icon: string }> = {
  0: { description: 'Clear sky', icon: '☀️' },
  1: { description: 'Mainly clear', icon: '🌤️' },
  2: { description: 'Partly cloudy', icon: '⛅' },
  3: { description: 'Overcast', icon: '☁️' },
  45: { description: 'Foggy', icon: '🌫️' },
  48: { description: 'Depositing rime fog', icon: '🌫️' },
  51: { description: 'Light drizzle', icon: '🌦️' },
  53: { description: 'Moderate drizzle', icon: '🌦️' },
  55: { description: 'Dense drizzle', icon: '🌧️' },
  56: { description: 'Light freezing drizzle', icon: '🌨️' },
  57: { description: 'Dense freezing drizzle', icon: '🌨️' },
  61: { description: 'Slight rain', icon: '🌧️' },
  63: { description: 'Moderate rain', icon: '🌧️' },
  65: { description: 'Heavy rain', icon: '⛈️' },
  66: { description: 'Light freezing rain', icon: '🌨️' },
  67: { description: 'Heavy freezing rain', icon: '🌨️' },
  71: { description: 'Slight snow fall', icon: '🌨️' },
  73: { description: 'Moderate snow fall', icon: '❄️' },
  75: { description: 'Heavy snow fall', icon: '❄️' },
  77: { description: 'Snow grains', icon: '🌨️' },
  80: { description: 'Slight rain showers', icon: '🌦️' },
  81: { description: 'Moderate rain showers', icon: '🌧️' },
  82: { description: 'Violent rain showers', icon: '⛈️' },
  85: { description: 'Slight snow showers', icon: '🌨️' },
  86: { description: 'Heavy snow showers', icon: '❄️' },
  95: { description: 'Thunderstorm', icon: '⛈️' },
  96: { description: 'Thunderstorm with slight hail', icon: '⛈️' },
  99: { description: 'Thunderstorm with heavy hail', icon: '⛈️' }
};

// UV Index level mapping
function getUVLevel(uvIndex: number): { level: string; color: string } {
  if (uvIndex <= 2) return { level: 'Low', color: 'text-green-600' };
  if (uvIndex <= 5) return { level: 'Moderate', color: 'text-yellow-600' };
  if (uvIndex <= 7) return { level: 'High', color: 'text-orange-600' };
  if (uvIndex <= 10) return { level: 'Very High', color: 'text-red-600' };
  return { level: 'Extreme', color: 'text-purple-600' };
}

// Air Quality Index level mapping (European AQI scale)
function getAirQualityLevel(aqi: number): { level: string; color: string } {
  if (aqi <= 20) return { level: 'Good', color: 'text-green-600' };
  if (aqi <= 40) return { level: 'Fair', color: 'text-yellow-600' };
  if (aqi <= 60) return { level: 'Moderate', color: 'text-orange-600' };
  if (aqi <= 80) return { level: 'Poor', color: 'text-red-600' };
  if (aqi <= 100) return { level: 'Very Poor', color: 'text-purple-600' };
  return { level: 'Hazardous', color: 'text-red-800' };
};

// Cache for weather data (cache for 30 minutes)
const weatherCache = new Map<string, { data: WeatherData; timestamp: number }>();
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

export async function getWeatherForCity(cityName: string): Promise<WeatherData | null> {
  try {
    // Check cache first
    const cached = weatherCache.get(cityName);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }

    const coordinates = cityCoordinates[cityName];
    if (!coordinates) {
      console.warn(`No coordinates found for city: ${cityName}`);
      return null;
    }

    // Call Open-Meteo API with UV index and air quality
    const url = `https://api.open-meteo.com/v1/forecast?latitude=${coordinates.lat}&longitude=${coordinates.lon}&current=temperature_2m,weather_code,uv_index&air_quality=true&current_air_quality=european_aqi&timezone=auto`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Weather API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.current) {
      throw new Error('Invalid weather data received');
    }

    const temperature = Math.round(data.current.temperature_2m);
    const weatherCode = data.current.weather_code;
    const weatherInfo = weatherDescriptions[weatherCode] || { description: 'Unknown', icon: '🌤️' };
    
    // UV Index data
    const uvIndex = Math.round(data.current.uv_index || 0);
    const uvInfo = getUVLevel(uvIndex);
    
    // Air Quality data
    const airQuality = Math.round(data.current_air_quality?.european_aqi || 25);
    const airQualityInfo = getAirQualityLevel(airQuality);

    const weatherData: WeatherData = {
      temperature,
      weatherCode,
      description: weatherInfo.description,
      icon: weatherInfo.icon,
      uvIndex,
      uvLevel: uvInfo.level,
      uvColor: uvInfo.color,
      airQuality,
      airQualityLevel: airQualityInfo.level,
      airQualityColor: airQualityInfo.color
    };

    // Cache the result
    weatherCache.set(cityName, {
      data: weatherData,
      timestamp: Date.now()
    });

    return weatherData;
  } catch (error) {
    console.error(`Error fetching weather for ${cityName}:`, error);
    return null;
  }
}

// Get weather for multiple cities
export async function getWeatherForCities(cityNames: string[]): Promise<Record<string, WeatherData | null>> {
  const weatherPromises = cityNames.map(async (cityName) => {
    const weather = await getWeatherForCity(cityName);
    return { cityName, weather };
  });

  const results = await Promise.all(weatherPromises);
  
  const weatherData: Record<string, WeatherData | null> = {};
  results.forEach(({ cityName, weather }) => {
    weatherData[cityName] = weather;
  });

  return weatherData;
}

// Get weather data by coordinates (for backward compatibility)
export async function getWeatherData(latitude: number, longitude: number): Promise<WeatherData | null> {
  try {
    // Validate coordinates
    if (latitude === undefined || longitude === undefined || latitude === null || longitude === null) {
      console.error('Invalid coordinates provided to getWeatherData:', { latitude, longitude });
      return null;
    }

    // Call Open-Meteo API with UV index and air quality
    const url = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,weather_code,uv_index&air_quality=true&current_air_quality=european_aqi&timezone=auto`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Weather API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.current) {
      throw new Error('Invalid weather data received');
    }

    const temperature = Math.round(data.current.temperature_2m);
    const weatherCode = data.current.weather_code;
    const weatherInfo = weatherDescriptions[weatherCode] || { description: 'Unknown', icon: '🌤️' };

    // UV Index data
    const uvIndex = Math.round(data.current.uv_index || 0);
    const uvInfo = getUVLevel(uvIndex);

    // Air Quality data
    const airQuality = Math.round(data.current_air_quality?.european_aqi || 25);
    const airQualityInfo = getAirQualityLevel(airQuality);

    const weatherData: WeatherData = {
      temperature,
      weatherCode,
      description: weatherInfo.description,
      icon: weatherInfo.icon,
      uvIndex,
      uvLevel: uvInfo.level,
      uvColor: uvInfo.color,
      airQuality,
      airQualityLevel: airQualityInfo.level,
      airQualityColor: airQualityInfo.color
    };

    return weatherData;
  } catch (error) {
    console.error(`Error fetching weather for coordinates ${latitude}, ${longitude}:`, error);
    return null;
  }
}

// Fallback weather data for when API fails
export function getFallbackWeather(cityName: string): WeatherData {
  // Typical weather for Mexican Caribbean coast
  return {
    temperature: 27,
    weatherCode: 1,
    description: 'Tropical',
    icon: '🌤️',
    uvIndex: 8,
    uvLevel: 'Very High',
    uvColor: 'text-red-600',
    airQuality: 25,
    airQualityLevel: 'Good',
    airQualityColor: 'text-green-600'
  };
}