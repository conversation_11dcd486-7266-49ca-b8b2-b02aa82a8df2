import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    const { businessId, userId, userEmail, businessEmail, businessPhone, businessName } = data;

    // Validate required fields
    if (!businessId || !userId || !userEmail || !businessEmail || !businessPhone || !businessName) {
      return new Response(JSON.stringify({
        error: 'Missing required fields'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if business exists
    const businessResult = await query(`
      SELECT business_name, slug, city_id, claimed_by_user_id 
      FROM listings 
      WHERE id = $1
    `, [businessId]);

    if (businessResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Business not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const business = businessResult.rows[0];

    // Check if business is already claimed
    if (business.claimed_by_user_id) {
      return new Response(JSON.stringify({
        error: 'This business has already been claimed'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if user has already submitted a claim for this business
    const existingClaim = await query(`
      SELECT id FROM support_requests 
      WHERE user_email = $1 
        AND type = 'business_correction' 
        AND subject LIKE $2
        AND status IN ('open', 'in_progress')
    `, [userEmail, `Business Claim Request:%${businessName}%`]);

    if (existingClaim.rows.length > 0) {
      return new Response(JSON.stringify({
        error: 'You have already submitted a claim request for this business'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create a support request for the business claim
    const subject = `Business Claim Request: ${businessName}`;
    const message = `Business Claim Request Details:

Business: ${businessName}
Business ID: ${businessId}
Claimant User ID: ${userId}
Claimant Email: ${userEmail}
Business Email: ${businessEmail}
Business Phone: ${businessPhone}

The user is requesting to claim ownership of this business listing. Please verify the provided contact information matches the business and approve/deny the claim accordingly.

If approved, set the claimed_by_user_id field in the listings table to: ${userId}`;

    // Insert into support_requests table
    await query(`
      INSERT INTO support_requests (
        user_email, type, subject, message, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
    `, [
      userEmail,
      'business_correction',
      subject,
      message,
      'open'
    ]);

    return new Response(JSON.stringify({
      success: true,
      message: 'Claim request submitted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error submitting business claim:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
