// Currency configuration for different countries
export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  exchangeRate?: number; // Approximate rate to USD for display purposes
}

export const COUNTRY_CURRENCIES: Record<string, CurrencyInfo> = {
  'Mexico': {
    code: 'MXN',
    symbol: '$',
    name: 'Mexican Peso',
    exchangeRate: 17.5 // Approximate rate
  },
  'Costa Rica': {
    code: 'CRC',
    symbol: '₡',
    name: 'Costa Rican Colón',
    exchangeRate: 520
  },
  'Panama': {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    exchangeRate: 1
  },
  'Colombia': {
    code: 'COP',
    symbol: '$',
    name: 'Colombian Peso',
    exchangeRate: 4000
  },
  'Ecuador': {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    exchangeRate: 1
  },
  'Peru': {
    code: 'PEN',
    symbol: 'S/',
    name: 'Peruvian Sol',
    exchangeRate: 3.7
  },
  'Argentina': {
    code: 'ARS',
    symbol: '$',
    name: 'Argentine Peso',
    exchangeRate: 350
  },
  'Brazil': {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    exchangeRate: 5.0
  },
  'Guatemala': {
    code: 'GTQ',
    symbol: 'Q',
    name: 'Guatemalan Quetzal',
    exchangeRate: 7.8
  },
  'Nicaragua': {
    code: 'NIO',
    symbol: 'C$',
    name: 'Nicaraguan Córdoba',
    exchangeRate: 36
  },
  'Honduras': {
    code: 'HNL',
    symbol: 'L',
    name: 'Honduran Lempira',
    exchangeRate: 24.5
  },
  'El Salvador': {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    exchangeRate: 1
  },
  'Belize': {
    code: 'BZD',
    symbol: 'BZ$',
    name: 'Belize Dollar',
    exchangeRate: 2.0
  },
  'Portugal': {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    exchangeRate: 0.92
  },
  'Spain': {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    exchangeRate: 0.92
  },
  'Hungary': {
    code: 'HUF',
    symbol: 'Ft',
    name: 'Hungarian Forint',
    exchangeRate: 360
  }
};

// Get currency info for a country
export function getCurrencyForCountry(country: string): CurrencyInfo {
  return COUNTRY_CURRENCIES[country] || {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    exchangeRate: 1
  };
}

// Format price with local currency
export function formatLocalPrice(amount: number, country: string): string {
  const currency = getCurrencyForCountry(country);
  if (!amount) return 'Free';
  
  // Format with appropriate decimal places
  const decimals = currency.code === 'USD' || currency.code === 'EUR' ? 2 : 0;
  return `${currency.symbol}${amount.toLocaleString('en-US', { 
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals 
  })}`;
}

// Format price in USD
export function formatUSDPrice(amount: number): string {
  if (!amount) return 'Free';
  return `$${amount.toLocaleString('en-US', { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 2 
  })} USD`;
}

// Convert local currency to USD (approximate)
export function convertToUSD(localAmount: number, country: string): number {
  const currency = getCurrencyForCountry(country);
  if (currency.code === 'USD') return localAmount;
  return localAmount / (currency.exchangeRate || 1);
}

// Convert USD to local currency (approximate)
export function convertFromUSD(usdAmount: number, country: string): number {
  const currency = getCurrencyForCountry(country);
  if (currency.code === 'USD') return usdAmount;
  return usdAmount * (currency.exchangeRate || 1);
}

// Check if country uses USD as primary currency
export function usesUSD(country: string): boolean {
  const currency = getCurrencyForCountry(country);
  return currency.code === 'USD';
}
