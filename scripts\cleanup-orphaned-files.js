#!/usr/bin/env node

/**
 * Cleanup Orphaned Files Script
 * 
 * This script finds and removes files in Supabase storage that are no longer
 * referenced by any active classified listings.
 * 
 * Usage: node scripts/cleanup-orphaned-files.js [--dry-run] [--force]
 */

import { createClient } from '@supabase/supabase-js';
import pkg from 'pg';
const { Client } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Need service role for storage admin
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Database connection
const dbClient = new Client({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const isForce = args.includes('--force');

console.log('🧹 Orphaned Files Cleanup Script');
console.log('================================');
console.log(`Mode: ${isDryRun ? 'DRY RUN (no files will be deleted)' : 'LIVE CLEANUP'}`);
console.log('');

async function main() {
  try {
    // Connect to database
    await dbClient.connect();
    console.log('✅ Connected to database');

    // Get all files from Supabase storage
    console.log('📁 Fetching files from Supabase storage...');
    const { data: files, error: storageError } = await supabase.storage
      .from('classified-images')
      .list('', { limit: 1000 });

    if (storageError) {
      throw new Error(`Storage error: ${storageError.message}`);
    }

    console.log(`📊 Found ${files.length} files in storage`);

    // Get all active file references from database
    console.log('🔍 Fetching active file references from database...');
    const result = await dbClient.query(`
      SELECT 
        image_url,
        additional_images,
        category_specific_data
      FROM classified_posts 
      WHERE 
        image_url IS NOT NULL 
        OR additional_images IS NOT NULL 
        OR (category_specific_data IS NOT NULL AND category_specific_data::text LIKE '%cv_url%')
    `);

    // Extract all referenced file names
    const referencedFiles = new Set();
    
    result.rows.forEach(row => {
      // Extract from image_url
      if (row.image_url) {
        const fileName = row.image_url.split('/').pop();
        if (fileName) referencedFiles.add(fileName);
      }

      // Extract from additional_images
      if (row.additional_images) {
        try {
          const additionalImages = JSON.parse(row.additional_images);
          additionalImages.forEach(url => {
            const fileName = url.split('/').pop();
            if (fileName) referencedFiles.add(fileName);
          });
        } catch (e) {
          console.warn('⚠️  Failed to parse additional_images:', e.message);
        }
      }

      // Extract from category_specific_data (CV files)
      if (row.category_specific_data) {
        try {
          const categoryData = JSON.parse(row.category_specific_data);
          if (categoryData.cv_url) {
            const fileName = categoryData.cv_url.split('/').pop();
            if (fileName) referencedFiles.add(fileName);
          }
        } catch (e) {
          console.warn('⚠️  Failed to parse category_specific_data:', e.message);
        }
      }
    });

    console.log(`📊 Found ${referencedFiles.size} referenced files in database`);

    // Find orphaned files
    const orphanedFiles = files.filter(file => !referencedFiles.has(file.name));
    
    console.log('');
    console.log('📋 CLEANUP SUMMARY');
    console.log('==================');
    console.log(`Total files in storage: ${files.length}`);
    console.log(`Referenced files: ${referencedFiles.size}`);
    console.log(`Orphaned files: ${orphanedFiles.length}`);

    if (orphanedFiles.length === 0) {
      console.log('✨ No orphaned files found! Storage is clean.');
      return;
    }

    // Calculate total size of orphaned files
    const totalSize = orphanedFiles.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

    console.log(`💾 Total size to be freed: ${totalSizeMB} MB`);
    console.log('');

    // List orphaned files
    console.log('🗑️  ORPHANED FILES:');
    orphanedFiles.forEach((file, index) => {
      const sizeMB = file.metadata?.size ? (file.metadata.size / 1024 / 1024).toFixed(2) : 'unknown';
      console.log(`${index + 1}. ${file.name} (${sizeMB} MB)`);
    });

    if (isDryRun) {
      console.log('');
      console.log('🔍 DRY RUN COMPLETE - No files were deleted');
      console.log('Run without --dry-run to actually delete these files');
      return;
    }

    // Confirm deletion
    if (!isForce) {
      console.log('');
      console.log('⚠️  WARNING: This will permanently delete the orphaned files!');
      console.log('Add --force flag to proceed with deletion');
      return;
    }

    // Delete orphaned files
    console.log('');
    console.log('🗑️  Deleting orphaned files...');
    
    const filesToDelete = orphanedFiles.map(file => file.name);
    const { error: deleteError } = await supabase.storage
      .from('classified-images')
      .remove(filesToDelete);

    if (deleteError) {
      throw new Error(`Delete error: ${deleteError.message}`);
    }

    console.log(`✅ Successfully deleted ${orphanedFiles.length} orphaned files`);
    console.log(`💾 Freed up ${totalSizeMB} MB of storage space`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await dbClient.end();
    console.log('');
    console.log('🏁 Cleanup script completed');
  }
}

main();