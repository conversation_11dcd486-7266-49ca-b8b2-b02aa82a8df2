import type { APIRoute } from 'astro';
import { cacheWarmer, cacheUtils } from '../../../lib/cache-warmer';

export const GET: APIRoute = async ({ url }) => {
  try {
    const action = url.searchParams.get('action');

    switch (action) {
      case 'status':
        const metrics = cacheUtils.getStatus();
        return new Response(JSON.stringify({
          success: true,
          metrics
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });

      case 'warm':
        await cacheWarmer.forceWarm();
        return new Response(JSON.stringify({
          success: true,
          message: 'Cache warming initiated'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });

      case 'clear':
        cacheUtils.clearAll();
        return new Response(JSON.stringify({
          success: true,
          message: 'Cache cleared'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });

      default:
        return new Response(JSON.stringify({
          error: 'Invalid action. Use: status, warm, or clear'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
    }
  } catch (error) {
    console.error('Cache API error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    const { action, citySlug } = await request.json();

    switch (action) {
      case 'warm-city':
        if (!citySlug) {
          return new Response(JSON.stringify({
            error: 'citySlug is required for warm-city action'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        await cacheUtils.warmCity(citySlug);
        return new Response(JSON.stringify({
          success: true,
          message: `Cache warmed for city: ${citySlug}`
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });

      default:
        return new Response(JSON.stringify({
          error: 'Invalid action'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
    }
  } catch (error) {
    console.error('Cache API error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
