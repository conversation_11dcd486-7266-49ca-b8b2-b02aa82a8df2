import type { APIRoute } from 'astro';
import { getCities, getCategories, query } from '../lib/database';

export const GET: APIRoute = async () => {
  try {
    // Get all cities
    const { data: cities } = await getCities();
    
    // Get all categories
    const { data: categories } = await getCategories();
    
    // Get all active listings for business pages
    const listingsResult = await query(`
      SELECT l.slug, l.city_id, l.category_primary_id, l.updated_at,
             c.path_slug as city_slug, cat.slug as category_slug
      FROM listings l
      LEFT JOIN cities c ON l.city_id = c.id
      LEFT JOIN categories cat ON l.category_primary_id = cat.id
      WHERE l.listing_status = 'active' AND l.deleted_at IS NULL
      ORDER BY l.updated_at DESC
    `);
    const listings = listingsResult.rows;

    const baseUrl = 'https://expatslist.org';
    
    // Generate sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Homepage -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- Cities pages -->
  ${cities?.map(city => `
  <url>
    <loc>${baseUrl}/${city.path_slug}</loc>
    <lastmod>${new Date(city.updated_at || city.created_at).toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>`).join('') || ''}
  
  <!-- Category pages -->
  ${cities?.map(city => 
    categories?.filter(cat => cat.parent_id !== null).map(category => `
  <url>
    <loc>${baseUrl}/${city.path_slug}/${category.slug}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('')
  ).join('') || ''}
  
  <!-- Main category pages -->
  ${cities?.map(city => 
    categories?.filter(cat => cat.parent_id === null).map(mainCategory => `
  <url>
    <loc>${baseUrl}/${city.path_slug}/${mainCategory.slug}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join('')
  ).join('') || ''}
  
  <!-- Business pages -->
  ${listings?.map(listing => `
  <url>
    <loc>${baseUrl}/${listing.city_slug}/${listing.category_slug}/${listing.slug}</loc>
    <lastmod>${new Date(listing.updated_at).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`).join('') || ''}
</urlset>`;

    return new Response(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=7200' // 1 hour browser, 2 hours CDN
      }
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return new Response('Error generating sitemap', { status: 500 });
  }
};
