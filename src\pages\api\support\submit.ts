import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userId, userEmail, type, subject, message } = await request.json();

    if (!type || !subject || !message) {
      return new Response(JSON.stringify({ error: 'Type, subject, and message are required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Insert support request into database
    const result = await query(`
      INSERT INTO support_requests (
        user_id, user_email, type, subject, message, status, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
      RETURNING *
    `, [userId, userEmail, type, subject, message, 'open']);

    return new Response(JSON.stringify({
      success: true,
      request: result.rows[0]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in support submit API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
