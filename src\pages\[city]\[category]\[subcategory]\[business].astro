---
import Layout from '../../../../layouts/Layout.astro';
import ProtectedContact from '../../../../components/ProtectedContact.astro';
import { getCityBySlug, getCategories, getBusinessListing, incrementViewCount } from '../../../../lib/database';
import Breadcrumb from '../../../../components/Breadcrumb.astro';

// Set cache headers for better performance
Astro.response.headers.set('Cache-Control', 'public, max-age=600, s-maxage=1200'); // 10 min browser, 20 min CDN

const { city: citySlug, category: categorySlug, subcategory: subcategorySlug, business: businessSlug } = Astro.params;

if (!citySlug || !categorySlug || !subcategorySlug || !businessSlug) {
  return Astro.redirect('/404');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);
if (cityError || !city) {
  return Astro.redirect('/404');
}

// Get categories from database
const { data: allCategories, error: categoriesError } = await getCategories();
if (categoriesError || !allCategories) {
  return Astro.redirect('/404');
}

// Find main category and subcategory
const mainCategory = allCategories.find(cat => cat.slug === categorySlug && cat.parent_id === null);
const subcategory = allCategories.find(cat => cat.slug === subcategorySlug && cat.parent_id === mainCategory?.id);

if (!mainCategory || !subcategory) {
  return Astro.redirect(`/${citySlug}`);
}

// Get listing data
const { data: listing, error: listingError } = await getBusinessListing(city.id, subcategory.id, businessSlug);
if (listingError || !listing) {
  return Astro.redirect(`/${citySlug}/${categorySlug}/${subcategorySlug}`);
}

// Increment view count
await incrementViewCount(listing.id);

// Generate display name
const displayName = listing.business_name || listing.listing_title || 'Business';

// Get category-specific icon for fallbacks
function getCategoryIcon(subcategorySlug: string): string {
  const iconMap: Record<string, string> = {
    // Housing & Relocation
    'rental-agencies': '🏢',
    'real-estate-agents': '🏘️',
    'property-management': '🏠',
    'moving-shipping-companies': '📦',
    'car-rental': '🚗',
    'furniture-stores': '🛋️',

    // Health & Wellness
    'medical-services': '🏥',
    'emergency-medical-services': '🚑',
    'pharmacies': '💊',
    'mental-health-therapy': '🧠',
    'alternative-complementary-medicine': '🌿',
    'fitness-gyms-personal-trainers': '💪',
    'massage-spas-beauty-salons': '💆',
    'yoga-meditation-studios': '🧘',

    // Food & Dining
    'restaurants': '🍽️',
    'cafes-coffee-shops': '☕',
    'grocery-stores-specialty-foods': '🛒',
    'local-food-markets-street-food': '🥬',

    // Professional, Legal & Financial
    'legal-immigration-services': '⚖️',
    'financial-services': '💰',
    'insurance-brokers': '🛡️',
    'business-support': '💼',
    'coworking-spaces': '💻',
    'currency-exchange': '💱',

    // Everyday Services & Repairs
    'connectivity': '📶',
    'home-maintenance-repair': '🔧',
    'appliance-repair': '🔌',
    'clothing-shoe-repair-alterations': '👕',
    'cleaning-services': '🧽',
    'gardeners-pool-maintenance': '🌱',
    'pet-services': '🐕',
    'vehicle-services': '🚗',
    'locksmiths': '🔑',

    // Shopping, Leisure & Community
    'retail-shopping': '🛍️',
    'tour-agencies': '🗺️',
    'bars-nightclubs': '🍻',
    'home-improvement': '🔨',
    'extra-curricular-activities': '🎨',
    'community-social': '👥',

    // Private Education & Childcare
    'schools': '🏫',
    'preschools-daycares': '👶',
    'language-schools': '📚',
    'after-school-programs-tutoring-workshops': '🎓'
  };
  return iconMap[subcategorySlug] || '🏪';
}

const categoryIcon = getCategoryIcon(subcategory.slug);

// Business type detection for conditional layouts
const businessType = {
  isRental: subcategory.slug.includes('rental-agencies') || subcategory.slug.includes('property-management') || subcategory.slug.includes('car-rental'),
  isRestaurant: subcategory.slug.includes('restaurants') || subcategory.slug.includes('cafes-coffee-shops'),
  isRealEstate: subcategory.slug.includes('real-estate-agents') || subcategory.slug.includes('rental-agencies'),
  isHealthcare: subcategory.slug.includes('medical-services') || subcategory.slug.includes('pharmacies') || subcategory.slug.includes('emergency-medical-services'),
  isSpa: subcategory.slug.includes('massage-spas-beauty-salons') || subcategory.slug.includes('yoga-meditation-studios'),
  isService: subcategory.slug.includes('legal-immigration-services') || subcategory.slug.includes('financial-services') || subcategory.slug.includes('business-support'),
  isRetail: subcategory.slug.includes('retail-shopping') || subcategory.slug.includes('grocery-stores-specialty-foods'),
  isCarRental: subcategory.slug.includes('car-rental')
};

// SEO
const pageTitle = `${displayName} - ${subcategory.name} in ${city.name} | ExpatsList`;
const pageDescription = listing.description_short || `Find ${displayName} in ${city.name}. ${subcategory.name} services for expats.`;
const canonicalUrl = `https://expatslist.org/${citySlug}/${categorySlug}/${subcategorySlug}/${businessSlug}`;
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <main class="min-h-screen bg-gray-100 pt-16 sm:pt-12">
    <!-- Standardized Breadcrumb Navigation -->
    <Breadcrumb
      sticky={true}
      items={[
        { label: 'Main', href: '/', icon: '🌎' },
        { label: city.name, href: `/${citySlug}`, icon: '📍' },
        { label: subcategory.name, href: `/${citySlug}/${categorySlug}/${subcategorySlug}` },
        { label: displayName, isActive: true }
      ]}
    />    <!-- Premium Business Header -->
    <div class="bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 text-white">
      <div class="max-w-4xl mx-auto px-3 py-6">
        <div class="flex items-center gap-4">
          <!-- Premium Business Photo or Icon -->
          <div class="w-16 h-16 lg:w-20 lg:h-20 rounded-xl flex items-center justify-center flex-shrink-0 shadow-xl overflow-hidden">
            {listing.google_photo_1 ? (
              <>
                <img
                  src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}
                  alt={`${displayName} - Main Photo`}
                  class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                  loading="eager"
                  onerror={`this.style.display='none'; ${listing.google_photo_2 ? 'this.nextElementSibling.style.display=\'block\';' : 'this.nextElementSibling.nextElementSibling.style.display=\'flex\';'}`}
                />
                {listing.google_photo_2 && (
                  <img
                    src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_2)}`}
                    alt={`${displayName} - Second Photo`}
                    class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                    loading="eager"
                    style="display: none;"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                  />
                )}
                <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" style="display: none;">
                  <span class="text-2xl lg:text-3xl text-white">{categoryIcon}</span>
                </div>
              </>
            ) : (
              <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <span class="text-2xl lg:text-3xl text-white">{categoryIcon}</span>
              </div>
            )}
          </div>

          <!-- Business Info -->
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl lg:text-3xl font-bold text-white mb-1 tracking-tight">
              {displayName}
            </h1>
            <div class="flex flex-wrap items-center gap-2 text-slate-300 mb-2">
              <span class="text-sm lg:text-base">{subcategory.name}</span>
              {listing.is_verified_expatslist && (
                <>
                  <span class="text-slate-500 hidden sm:inline">•</span>
                  <span class="bg-emerald-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center">
                    <span class="mr-1">✓</span>
                    Verified
                  </span>
                </>
              )}
              {listing.claimed_by_user_id && (
                <>
                  <span class="text-slate-500 hidden sm:inline">•</span>
                  <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center">
                    <span class="mr-1">🏢</span>
                    Claimed
                  </span>
                </>
              )}
            </div>
            {listing.description_short && (
              <div class="description-preview">
                <p
                  class="text-slate-300 text-sm lg:text-base leading-relaxed"
                  id="hero-description"
                  data-full-text={listing.description_short}
                  data-short-text={listing.description_short.length > 120 ? listing.description_short.substring(0, 120) + '...' : listing.description_short}
                >
                  {listing.description_short.length > 120
                    ? listing.description_short.substring(0, 120) + '...'
                    : listing.description_short
                  }
                </p>
                {listing.description_short.length > 120 && (
                  <button
                    onclick="toggleHeroDescription()"
                    class="text-blue-400 hover:text-blue-300 text-xs mt-1 underline"
                    id="hero-read-more"
                  >
                    Read more
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>    <!-- Ultra-Compact Mobile-First Layout -->
    <div class="max-w-4xl mx-auto px-3 py-4">
      <div class="space-y-3">

        <!-- 1. PRIMARY: Contact & Connect (Most Important) -->
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-md border border-blue-200/50 p-4">
          <h3 class="text-lg font-bold text-slate-900 mb-3 flex items-center">
            <span class="w-6 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-2">
              <span class="text-white text-xs">📞</span>
            </span>
            Contact & Connect
          </h3>

          <!-- Uniform Contact Grid -->
          <div class="grid grid-cols-2 gap-2">
            {/* Website Button */}
            {listing.contact_info?.website && listing.contact_info.website.trim() !== '' && (
              <a
                href={listing.contact_info.website.startsWith('http') ? listing.contact_info.website : `https://${listing.contact_info.website}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="website"
                class="group flex items-center justify-center px-3 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">🌐</span>
                <span class="text-xs">Website</span>
              </a>
            )}

            {/* Protected Contact Information */}
            <ProtectedContact
              phone={listing.contact_info?.phone}
              email={listing.contact_info?.email}
              whatsapp={listing.contact_info?.whatsapp}
              type="business"
              size="small"
              style="button"
            />

            {/* Social Media Buttons - Uniform Size */}
            {listing.contact_info?.facebook && (
              <a
                href={listing.contact_info.facebook.startsWith('http') ? listing.contact_info.facebook : `https://facebook.com/${listing.contact_info.facebook}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="facebook"
                class="group flex items-center justify-center px-3 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">📘</span>
                <span class="text-xs">Facebook</span>
              </a>
            )}

            {listing.contact_info?.instagram && (
              <a
                href={listing.contact_info.instagram.startsWith('http') ? listing.contact_info.instagram : `https://instagram.com/${listing.contact_info.instagram}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="instagram"
                class="group flex items-center justify-center px-3 py-2.5 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">📷</span>
                <span class="text-xs">Instagram</span>
              </a>
            )}

            {listing.contact_info?.twitter && (
              <a
                href={listing.contact_info.twitter.startsWith('http') ? listing.contact_info.twitter : `https://twitter.com/${listing.contact_info.twitter}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="twitter"
                class="group flex items-center justify-center px-3 py-2.5 bg-sky-500 hover:bg-sky-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">🐦</span>
                <span class="text-xs">Twitter</span>
              </a>
            )}

            {listing.contact_info?.linkedin && (
              <a
                href={listing.contact_info.linkedin.startsWith('http') ? listing.contact_info.linkedin : `https://linkedin.com/company/${listing.contact_info.linkedin}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="linkedin"
                class="group flex items-center justify-center px-3 py-2.5 bg-blue-700 hover:bg-blue-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">💼</span>
                <span class="text-xs">LinkedIn</span>
              </a>
            )}

            {listing.contact_info?.youtube && (
              <a
                href={listing.contact_info.youtube.startsWith('http') ? listing.contact_info.youtube : `https://youtube.com/${listing.contact_info.youtube}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="youtube"
                class="group flex items-center justify-center px-3 py-2.5 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">📺</span>
                <span class="text-xs">YouTube</span>
              </a>
            )}

            {listing.contact_info?.tiktok && (
              <a
                href={listing.contact_info.tiktok.startsWith('http') ? listing.contact_info.tiktok : `https://tiktok.com/@${listing.contact_info.tiktok}`}
                target="_blank"
                rel="noopener noreferrer"
                data-track="tiktok"
                class="group flex items-center justify-center px-3 py-2.5 bg-black hover:bg-gray-800 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">🎵</span>
                <span class="text-xs">TikTok</span>
              </a>
            )}
          </div>
        </div>

        <!-- 2. Photos & Features Combined (Space Efficient) -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-3">

          <!-- Photos Section (Compact) -->
          <div class="lg:col-span-2 bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl shadow-md border border-slate-200/50 p-3">
            <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
              <span class="w-5 h-5 bg-gradient-to-r from-slate-600 to-slate-700 rounded-md flex items-center justify-center mr-2">
                <span class="text-white text-xs">📸</span>
              </span>
              Photos
            </h3>
            <div class="grid grid-cols-2 gap-2">
              <!-- Photo 1 with Fallback -->
              <div class="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                {listing.google_photo_1 ? (
                  <>
                    <img
                      src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}
                      alt={`${displayName} - Main Photo`}
                      class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                      onerror={`this.style.display='none'; ${listing.google_photo_2 ? 'this.nextElementSibling.style.display=\'block\';' : 'this.nextElementSibling.nextElementSibling.style.display=\'flex\';'}`}
                    />
                    {listing.google_photo_2 && (
                      <img
                        src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_2)}`}
                        alt={`${displayName} - Second Photo`}
                        class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="lazy"
                        style="display: none;"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                      />
                    )}
                    <div class="w-full h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center" style="display: none;">
                      <span class="text-3xl text-white">{categoryIcon}</span>
                    </div>
                  </>
                ) : (
                  <div class="w-full h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-3xl text-white">{categoryIcon}</span>
                  </div>
                )}
              </div>

              <!-- Photo 2 with Fallback (only if photo 2 exists) -->
              {listing.google_photo_2 && (
                <div class="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                  <img
                    src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_2)}`}
                    alt={`${displayName} - Second Photo`}
                    class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                  />
                  <div class="w-full h-32 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg flex items-center justify-center" style="display: none;">
                    <span class="text-3xl text-white">{categoryIcon}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <!-- Business Features (Compact with View More) -->
          {listing.google_characteristics && Object.keys(listing.google_characteristics).length > 0 && (
            <div class="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl shadow-md border border-indigo-200/50 p-3">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-base font-bold text-slate-900 flex items-center">
                  <span class="w-5 h-5 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-md flex items-center justify-center mr-2">
                    <span class="text-white text-xs">✨</span>
                  </span>
                  Features
                </h3>
                {Object.keys(listing.google_characteristics).length > 2 && (
                  <button
                    id="features-toggle"
                    class="text-indigo-600 hover:text-indigo-700 text-xs font-medium underline transition-colors duration-200"
                    onclick="toggleFeatures()"
                  >
                    View more
                  </button>
                )}
              </div>

              <!-- Features Container -->
              <div id="features-container" class="space-y-2">
                {Object.entries(listing.google_characteristics).map(([category, features], categoryIndex) => {
                  const shouldHide = categoryIndex >= 2; // Show only first 2 categories initially

                  return (
                    <div
                      class={`bg-white/70 backdrop-blur-sm rounded-lg p-2 border border-white/50 ${shouldHide ? 'features-hidden' : ''}`}
                      style={shouldHide ? 'display: none;' : ''}
                    >
                      <div class="text-xs font-bold text-indigo-700 mb-1 capitalize flex items-center">
                        <span class="w-1.5 h-1.5 bg-indigo-500 rounded-full mr-1.5"></span>
                        {category.replace(/-/g, ' ')}
                      </div>
                      <div class="grid grid-cols-1 gap-1">
                        {Object.entries(features).slice(0, 4).map(([feature, value]) => {
                          // Clean up feature names by removing redundant words
                          const cleanFeature = feature
                            .replace(/^(has|offers|accepts|is|provides)\s+/i, '')
                            .replace(/\s+(available|accepted|friendly|accessible)$/i, '')
                            .replace(/-/g, ' ')
                            .replace(/\b\w/g, l => l.toUpperCase());

                          const isPositive = value === true || value === 'true' ||
                                           (typeof value === 'string' && !value.includes('no') && !value.includes('false'));

                          return (
                            <div class="flex items-center text-xs">
                              <span class={`mr-1.5 text-xs ${isPositive ? 'text-emerald-600' : 'text-slate-400'}`}>
                                {isPositive ? '✓' : '○'}
                              </span>
                              <span class={`${isPositive ? 'text-slate-700' : 'text-slate-500'}`}>
                                {cleanFeature}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <!-- 3. About & Description (Compact) -->
        {listing.description_long && listing.description_long !== listing.description_short && (
          <div class="bg-white rounded-xl shadow-md border border-slate-200/50 p-3">
            <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
              <span class="w-5 h-5 bg-gradient-to-r from-slate-600 to-slate-700 rounded-md flex items-center justify-center mr-2">
                <span class="text-white text-xs">ℹ️</span>
              </span>
              About {displayName}
            </h3>
            <div class="prose prose-slate max-w-none">
              <div class="description-content">
                <p
                  class="text-slate-700 leading-relaxed text-sm"
                  id="about-description"
                  data-full-text={listing.description_long}
                  data-short-text={listing.description_long.length > 200 ? listing.description_long.substring(0, 200) + '...' : listing.description_long}
                >
                  {listing.description_long.length > 200
                    ? listing.description_long.substring(0, 200) + '...'
                    : listing.description_long
                  }
                </p>
                {listing.description_long.length > 200 && (
                  <button
                    onclick="toggleAboutDescription()"
                    class="text-blue-600 hover:text-blue-700 text-sm mt-2 underline font-medium"
                    id="about-read-more"
                  >
                    Read more
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        <!-- 4. Location, Hours & Analytics Combined (Space Efficient) -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">

          <!-- Location & Hours -->
          {(listing.address_full || listing.google_working_hours || (listing.latitude && listing.longitude)) && (
            <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl shadow-md border border-purple-200/50 p-3">
              <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
                <span class="w-5 h-5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-md flex items-center justify-center mr-2">
                  <span class="text-white text-xs">📍</span>
                </span>
                Location & Hours
              </h3>

              <!-- Address -->
              {listing.address_full && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2 mb-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">📍</span>
                    <div class="flex-1">
                      <p class="text-slate-800 font-medium text-xs leading-relaxed mb-2">{listing.address_full}</p>
                      {(listing.latitude && listing.longitude) && (
                        <a
                          href={`https://www.google.com/maps?q=${listing.latitude},${listing.longitude}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          class="inline-flex items-center px-2 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                        >
                          <span class="mr-1">🗺️</span>
                          Maps
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <!-- Business Hours -->
              {listing.google_working_hours && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">🕒</span>
                    <div class="flex-1">
                      <div class="text-xs text-purple-700 font-medium mb-1">Hours</div>
                      <div class="space-y-0.5 text-xs">
                        {Object.entries(listing.google_working_hours).slice(0, 3).map(([day, hours]) => (
                          <div class="flex justify-between items-center">
                            <span class="capitalize font-medium text-slate-700">{day}:</span>
                            <span class="text-slate-600">
                              {hours && typeof hours === 'string'
                                ? hours.replace(/-/g, ' - ').replace(/am|pm/g, (match: string) => match.toUpperCase())
                                : 'Hours not available'
                              }
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <!-- Business Engagement (User-Friendly Analytics) -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-md border border-blue-200/50 p-3" data-analytics-section>
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-base font-bold text-slate-900 flex items-center">
                <span class="w-5 h-5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-md flex items-center justify-center mr-2">
                  <span class="text-white text-xs">📈</span>
                </span>
                Business Engagement
              </h3>
              <div class="group relative">
                <button class="text-slate-400 hover:text-slate-600 text-xs">
                  <span class="w-4 h-4 rounded-full bg-slate-200 flex items-center justify-center text-xs font-bold">?</span>
                </button>
                <div class="absolute right-0 top-6 w-64 bg-white rounded-lg shadow-lg border p-3 text-xs text-slate-600 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  <p class="font-medium mb-1">What do these numbers mean?</p>
                  <p><strong>Page Views:</strong> Total number of times this listing has been viewed</p>
                  <p><strong>Link Clicks:</strong> Total number of clicks to their website and social media pages</p>
                </div>
              </div>
            </div>

            <!-- User-Friendly Analytics Display -->
            <div class="space-y-2">
              {listing.view_count > 0 && (
                <div class="bg-white/70 backdrop-blur-sm rounded-lg p-2 border border-white/50" data-analytics-type="views">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <span class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                        <span class="text-blue-600 text-xs">👁️</span>
                      </span>
                      <div>
                        <p class="text-xs font-medium text-slate-800">Page Views</p>
                        <p class="text-xs text-slate-500">This business has been viewed</p>
                      </div>
                    </div>
                    <span class="text-lg font-bold text-blue-600">{listing.view_count}</span>
                  </div>
                </div>
              )}

              <!-- Website & Social Media Clicks -->
              {(listing.click_through_count_website > 0 ||
                listing.click_through_count_facebook > 0 ||
                listing.click_through_count_instagram > 0 ||
                listing.click_through_count_twitter > 0 ||
                listing.click_through_count_linkedin > 0 ||
                listing.click_through_count_youtube > 0 ||
                listing.click_through_count_tiktok > 0) && (
                <div class="bg-white/70 backdrop-blur-sm rounded-lg p-2 border border-white/50">
                  <div class="flex items-center mb-2">
                    <span class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                      <span class="text-green-600 text-xs">🔗</span>
                    </span>
                    <div>
                      <p class="text-xs font-medium text-slate-800">Link Clicks</p>
                      <p class="text-xs text-slate-500">Clicks to their website and social media</p>
                    </div>
                  </div>

                  <!-- Compact Click Grid -->
                  <div class="grid grid-cols-2 gap-1 ml-8">
                    {listing.click_through_count_website > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="website">
                        <span class="text-slate-600">🌐 Website</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_website}</span>
                      </div>
                    )}
                    {listing.click_through_count_facebook > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="facebook">
                        <span class="text-slate-600">📘 Facebook</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_facebook}</span>
                      </div>
                    )}
                    {listing.click_through_count_instagram > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="instagram">
                        <span class="text-slate-600">📷 Instagram</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_instagram}</span>
                      </div>
                    )}
                    {listing.click_through_count_twitter > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="twitter">
                        <span class="text-slate-600">🐦 Twitter</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_twitter}</span>
                      </div>
                    )}
                    {listing.click_through_count_linkedin > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="linkedin">
                        <span class="text-slate-600">💼 LinkedIn</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_linkedin}</span>
                      </div>
                    )}
                    {listing.click_through_count_youtube > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="youtube">
                        <span class="text-slate-600">📺 YouTube</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_youtube}</span>
                      </div>
                    )}
                    {listing.click_through_count_tiktok > 0 && (
                      <div class="flex items-center justify-between text-xs" data-analytics-type="tiktok">
                        <span class="text-slate-600">🎵 TikTok</span>
                        <span class="font-bold text-slate-800 text-center min-w-[20px]">{listing.click_through_count_tiktok}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {(!listing.view_count || listing.view_count === 0) &&
               (!listing.click_through_count_website || listing.click_through_count_website === 0) &&
               (!listing.click_through_count_facebook || listing.click_through_count_facebook === 0) &&
               (!listing.click_through_count_instagram || listing.click_through_count_instagram === 0) &&
               (!listing.click_through_count_twitter || listing.click_through_count_twitter === 0) &&
               (!listing.click_through_count_linkedin || listing.click_through_count_linkedin === 0) &&
               (!listing.click_through_count_youtube || listing.click_through_count_youtube === 0) &&
               (!listing.click_through_count_tiktok || listing.click_through_count_tiktok === 0) && (
                <div class="bg-white/70 backdrop-blur-sm rounded-lg p-3 border border-white/50 text-center">
                  <div class="text-slate-400 mb-1">📊</div>
                  <p class="text-xs text-slate-500 font-medium">Building Engagement</p>
                  <p class="text-xs text-slate-400">Be the first to interact with this business!</p>
                </div>
              )}
            </div>
          </div>
        </div>



        <!-- 5. Industry-Specific Information (Compact) -->
        {(businessType.isRestaurant && (listing.cuisine_type || listing.menu_highlights || listing.dietary_options)) ||
         (businessType.isRealEstate && (listing.property_types || listing.service_areas || listing.specializations)) ||
         (businessType.isHealthcare && (listing.specialties || listing.insurance_accepted || listing.languages_spoken)) && (
          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl shadow-md border border-orange-200/50 p-3">
            <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
              <span class="w-5 h-5 bg-gradient-to-r from-orange-500 to-red-500 rounded-md flex items-center justify-center mr-2">
                <span class="text-white text-xs">
                  {businessType.isRestaurant ? '🍽️' : businessType.isRealEstate ? '🏘️' : '🏥'}
                </span>
              </span>
              {businessType.isRestaurant ? 'Menu & Cuisine' : businessType.isRealEstate ? 'Property Services' : 'Medical Info'}
            </h3>
            <div class="space-y-2">
              {/* Restaurant Info */}
              {businessType.isRestaurant && listing.cuisine_type && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-center">
                    <span class="mr-2 text-sm">🌍</span>
                    <div>
                      <div class="text-xs text-orange-700 font-medium">Cuisine</div>
                      <div class="text-xs font-semibold text-slate-800">{listing.cuisine_type}</div>
                    </div>
                  </div>
                </div>
              )}
              {businessType.isRestaurant && listing.menu_highlights && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">⭐</span>
                    <div>
                      <div class="text-xs text-orange-700 font-medium">Popular Dishes</div>
                      <div class="text-xs text-slate-700">{listing.menu_highlights}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Real Estate Info */}
              {businessType.isRealEstate && listing.property_types && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">🏠</span>
                    <div>
                      <div class="text-xs text-blue-700 font-medium">Property Types</div>
                      <div class="text-xs text-slate-700">{listing.property_types}</div>
                    </div>
                  </div>
                </div>
              )}
              {businessType.isRealEstate && listing.service_areas && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">📍</span>
                    <div>
                      <div class="text-xs text-blue-700 font-medium">Service Areas</div>
                      <div class="text-xs text-slate-700">{listing.service_areas}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Healthcare Info */}
              {businessType.isHealthcare && listing.specialties && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">🩺</span>
                    <div>
                      <div class="text-xs text-green-700 font-medium">Specialties</div>
                      <div class="text-xs text-slate-700">{listing.specialties}</div>
                    </div>
                  </div>
                </div>
              )}
              {businessType.isHealthcare && listing.insurance_accepted && (
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-2">
                  <div class="flex items-start">
                    <span class="mr-2 text-sm">💳</span>
                    <div>
                      <div class="text-xs text-green-700 font-medium">Insurance</div>
                      <div class="text-xs text-slate-700">{listing.insurance_accepted}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <!-- 6. User Actions & Navigation (Compact) -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">

          <!-- User Actions -->
          <div class="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl shadow-md border border-slate-200/50 p-3">
            <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
              <span class="w-5 h-5 bg-gradient-to-r from-slate-600 to-slate-700 rounded-md flex items-center justify-center mr-2">
                <span class="text-white text-xs">⚡</span>
              </span>
              Actions
            </h3>
            <div class="grid grid-cols-2 gap-2">
              <button
                id="rate-business"
                class="group flex items-center justify-center px-3 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">👍</span>
                <span class="text-xs">Rate</span>
              </button>
              <button
                id="report-business"
                class="group flex items-center justify-center px-3 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">🚩</span>
                <span class="text-xs">Report</span>
              </button>
              <button
                id="claim-business"
                class="group flex items-center justify-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">🏢</span>
                <span class="text-xs">Claim</span>
              </button>
              <button
                id="share-business"
                class="group flex items-center justify-center px-3 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">📤</span>
                <span class="text-xs">Share</span>
              </button>
            </div>
          </div>

          <!-- Explore More -->
          <div class="bg-white rounded-xl shadow-md border border-slate-200/50 p-3">
            <h3 class="text-base font-bold text-slate-900 mb-2 flex items-center">
              <span class="w-5 h-5 bg-gradient-to-r from-teal-500 to-teal-600 rounded-md flex items-center justify-center mr-2">
                <span class="text-white text-xs">🔍</span>
              </span>
              Explore More
            </h3>
            <div class="space-y-1.5">
              <a
                href={`/${citySlug}/${categorySlug}/${subcategorySlug}`}
                class="block bg-teal-50 hover:bg-teal-100 text-teal-700 font-medium px-3 py-2 rounded-lg transition-all duration-200 hover:shadow-md text-xs"
              >
                All {subcategory.name} →
              </a>
              <a
                href={`/${citySlug}/${categorySlug}`}
                class="block bg-slate-50 hover:bg-slate-100 text-slate-700 font-medium px-3 py-2 rounded-lg transition-all duration-200 hover:shadow-md text-xs"
              >
                {mainCategory.name} →
              </a>
              <a
                href={`/${citySlug}`}
                class="block bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium px-3 py-2 rounded-lg transition-all duration-200 hover:shadow-md text-xs"
              >
                All {city.name} →
              </a>
            </div>
          </div>
        </div>

        <!-- 7. Admin Controls (Only visible to administrators) -->
        <div id="admin-controls" class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl shadow-md border border-red-200/50 p-3" style="display: none;">
          <h3 class="text-base font-bold text-red-900 mb-2 flex items-center">
            <span class="w-5 h-5 bg-gradient-to-r from-red-600 to-red-700 rounded-md flex items-center justify-center mr-2">
              <span class="text-white text-xs">🛡️</span>
            </span>
            Admin Controls
          </h3>
          <button
            id="admin-delete-business"
            class="group w-full flex items-center justify-center px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
          >
            <span class="mr-2 text-sm group-hover:scale-110 transition-transform">🗑️</span>
            <span class="text-xs">Delete Business</span>
          </button>
        </div>



      </div>
    </div>
  </main><script is:inline define:vars={{ listingId: listing.id, displayName }}>
  // Track click function - listingId is now properly interpolated

  // Add event listeners when page loads
  document.addEventListener('DOMContentLoaded', function() {
    // Track all links with data-track attribute
    const trackableLinks = document.querySelectorAll('[data-track]');
    trackableLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        const trackType = this.getAttribute('data-track');
        trackClick(trackType);
      });
    });

    // Check if user is admin and show admin controls
    checkAdminAccess();

    // Add event listeners for action buttons
    setupActionButtons();
  });

  // Check admin access and show controls
  function checkAdminAccess() {
    if (!window.authFunctions) {
      setTimeout(checkAdminAccess, 100);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();
    const userProfile = window.authFunctions.getUserProfile();

    // Check admin status with improved logic
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Business Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && userProfile && userProfile.role === 'administrator') {
        console.log('Admin detected, showing business controls');
        const adminControls = document.getElementById('admin-controls');
        if (adminControls) {
          adminControls.style.display = 'block';
        }
      } else if (currentUser && currentUser.email === '<EMAIL>') {
        // <NAME_EMAIL> even if profile isn't loaded
        console.log('Admin email detected, showing business controls');
        const adminControls = document.getElementById('admin-controls');
        if (adminControls) {
          adminControls.style.display = 'block';
        }
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);

    if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {

      // Add admin delete handler
      document.getElementById('admin-delete-business')?.addEventListener('click', async () => {
        try {
          const deleteButton = document.getElementById('admin-delete-business');

          console.log('Delete business attempt:', {
            businessId: listingId,
            adminUserId: currentUser.id,
            currentUser: currentUser
          });

          // Update button to show loading state
          deleteButton.disabled = true;
          deleteButton.innerHTML = '<span class="mr-3 text-lg animate-spin">⏳</span>Deleting Business...';
          deleteButton.classList.add('opacity-75');

          const response = await fetch('/api/admin/delete-business', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              businessId: listingId,
              adminUserId: currentUser.id
            })
          });

          console.log('Delete business response status:', response.status);

          const result = await response.json();

          if (response.ok) {
            // Show success state
            deleteButton.innerHTML = '<span class="mr-3 text-lg">✅</span>Business Deleted Successfully';
            deleteButton.classList.remove('from-red-500', 'to-red-600', 'hover:from-red-600', 'hover:to-red-700');
            deleteButton.classList.add('from-green-500', 'to-green-600');

            // Redirect after brief success feedback
            setTimeout(() => {
              window.location.href = '/';
            }, 2000);
          } else {
            // Show error state
            deleteButton.innerHTML = '<span class="mr-3 text-lg">❌</span>Delete Failed';
            deleteButton.classList.remove('from-red-500', 'to-red-600');
            deleteButton.classList.add('from-red-700', 'to-red-800');

            // Reset after 3 seconds
            setTimeout(() => {
              deleteButton.disabled = false;
              deleteButton.innerHTML = '<span class="mr-3 text-lg group-hover:scale-110 transition-transform">🗑️</span>Delete Business';
              deleteButton.classList.remove('from-red-700', 'to-red-800', 'opacity-75');
              deleteButton.classList.add('from-red-500', 'to-red-600', 'hover:from-red-600', 'hover:to-red-700');
            }, 3000);
          }
        } catch (error) {
          console.error('Error deleting business:', error);

          const deleteButton = document.getElementById('admin-delete-business');
          // Show error state
          deleteButton.innerHTML = '<span class="mr-3 text-lg">❌</span>Error Occurred';
          deleteButton.classList.remove('from-red-500', 'to-red-600');
          deleteButton.classList.add('from-red-700', 'to-red-800');

          // Reset after 3 seconds
          setTimeout(() => {
            deleteButton.disabled = false;
            deleteButton.innerHTML = '<span class="mr-3 text-lg group-hover:scale-110 transition-transform">🗑️</span>Delete Business';
            deleteButton.classList.remove('from-red-700', 'to-red-800', 'opacity-75');
            deleteButton.classList.add('from-red-500', 'to-red-600', 'hover:from-red-600', 'hover:to-red-700');
          }, 3000);
        }
      });
    }
  }

  async function trackClick(type) {
    console.log('Tracking click:', type, 'for listing:', listingId);
    try {
      const response = await fetch('/api/track-click', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          listingId: listingId,
          type: type
        })
      });

      console.log('Track click response:', response.status);
      const result = await response.json();
      console.log('Track click result:', result);

      if (response.ok) {
        console.log('✅ Click tracked successfully for:', type);
        // Optionally update the analytics display without page reload
        updateAnalyticsDisplay(type);
      } else {
        console.error('❌ Failed to track click:', result.error);
      }
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  }

  // Update analytics display in real-time for new user-friendly format
  function updateAnalyticsDisplay(type) {
    console.log('Updating analytics display for:', type);

    // Find the analytics section
    const analyticsSection = document.querySelector('[data-analytics-section]');

    if (!analyticsSection) {
      console.log('Analytics section not found');
      return;
    }

    // Handle view count updates
    if (type === 'view') {
      let viewsItem = analyticsSection.querySelector('[data-analytics-type="views"]');
      if (viewsItem) {
        const countSpan = viewsItem.querySelector('.text-lg.font-bold.text-blue-600');
        if (countSpan) {
          const currentCount = parseInt(countSpan.textContent) || 0;
          countSpan.textContent = (currentCount + 1).toString();
          console.log(`Updated views count to:`, currentCount + 1);
        }
      } else {
        // Create new views section if it doesn't exist
        const noDataSection = analyticsSection.querySelector('.text-center');
        if (noDataSection) {
          noDataSection.remove();
        }

        const newViewsItem = document.createElement('div');
        newViewsItem.className = 'bg-white/70 backdrop-blur-sm rounded-lg p-2 border border-white/50';
        newViewsItem.setAttribute('data-analytics-type', 'views');
        newViewsItem.innerHTML = `
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <span class="text-blue-600 text-xs">👁️</span>
              </span>
              <div>
                <p class="text-xs font-medium text-slate-800">Page Views</p>
                <p class="text-xs text-slate-500">People who viewed this listing</p>
              </div>
            </div>
            <span class="text-lg font-bold text-blue-600">1</span>
          </div>
        `;
        analyticsSection.querySelector('.space-y-2').appendChild(newViewsItem);
        console.log('Created new views section');
      }
      return;
    }

    // Handle click tracking updates
    const typeMap = {
      'website': '🌐 Website',
      'facebook': '📘 Facebook',
      'instagram': '📷 Instagram',
      'twitter': '🐦 Twitter',
      'linkedin': '💼 LinkedIn',
      'youtube': '📺 YouTube',
      'tiktok': '🎵 TikTok'
    };

    if (!typeMap[type]) {
      console.log('Type not found in typeMap:', type);
      return;
    }

    // Look for existing click item
    let existingItem = analyticsSection.querySelector(`[data-analytics-type="${type}"]`);

    if (existingItem) {
      // Update existing count
      const countSpan = existingItem.querySelector('.font-bold.text-slate-800');
      if (countSpan) {
        const currentCount = parseInt(countSpan.textContent) || 0;
        countSpan.textContent = (currentCount + 1).toString();
        console.log(`Updated ${type} count to:`, currentCount + 1);
      }
    } else {
      // Need to create or update the link clicks section
      let linksSection = analyticsSection.querySelector('.bg-white\\/70.backdrop-blur-sm.rounded-lg.p-2.border.border-white\\/50:not([data-analytics-type="views"])');

      if (!linksSection) {
        // Create the entire link clicks section
        const noDataSection = analyticsSection.querySelector('.text-center');
        if (noDataSection) {
          noDataSection.remove();
        }

        linksSection = document.createElement('div');
        linksSection.className = 'bg-white/70 backdrop-blur-sm rounded-lg p-2 border border-white/50';
        linksSection.innerHTML = `
          <div class="flex items-center mb-2">
            <span class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
              <span class="text-green-600 text-xs">🔗</span>
            </span>
            <div>
              <p class="text-xs font-medium text-slate-800">Link Clicks</p>
              <p class="text-xs text-slate-500">People who visited their links</p>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-1 ml-8"></div>
        `;
        analyticsSection.querySelector('.space-y-2').appendChild(linksSection);
      }

      // Add the new click item to the grid
      const clickGrid = linksSection.querySelector('.grid.grid-cols-2');
      if (clickGrid) {
        const newClickItem = document.createElement('div');
        newClickItem.className = 'flex items-center justify-between text-xs';
        newClickItem.setAttribute('data-analytics-type', type);
        newClickItem.innerHTML = `
          <span class="text-slate-600">${typeMap[type]}</span>
          <span class="font-bold text-slate-800">1</span>
        `;
        clickGrid.appendChild(newClickItem);
        console.log(`Created new click item for ${type}`);
      }
    }
  }

  // Rate Business Function
  async function rateBusiness() {
    // Check if user is signed in
    const userEmail = localStorage.getItem('userEmail');
    const userId = localStorage.getItem('userId');

    if (!userEmail || !userId) {
      alert('Please sign in to rate this business.');
      return;
    }

    try {
      const response = await fetch('/api/business/rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: listingId,
          userId: userId,
          type: 'thumbs_up'
        })
      });

      const result = await response.json();

      if (response.ok) {
        alert('Thank you for your recommendation!');
        // Refresh page to show updated count
        window.location.reload();
      } else {
        alert(result.error || 'Failed to submit rating. Please try again.');
      }
    } catch (error) {
      console.error('Error rating business:', error);
      alert('Failed to submit rating. Please try again.');
    }
  }

  // Report Business Function
  async function reportBusiness() {
    const reportTypes = [
      'Business is closed',
      'Incorrect contact information',
      'Wrong address/location',
      'Inappropriate content',
      'Duplicate listing',
      'Other issue'
    ];

    const selectedType = prompt(`Please select the type of issue:\n\n${reportTypes.map((type, index) => `${index + 1}. ${type}`).join('\n')}\n\nEnter the number (1-${reportTypes.length}):`);

    if (!selectedType || isNaN(selectedType) || selectedType < 1 || selectedType > reportTypes.length) {
      return;
    }

    const reportType = reportTypes[parseInt(selectedType) - 1];
    const details = prompt(`Please provide additional details about: ${reportType}`);

    if (!details) {
      return;
    }

    const userEmail = localStorage.getItem('userEmail') || 'anonymous';

    try {
      const response = await fetch('/api/business/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: listingId,
          reportType: reportType,
          details: details,
          userEmail: userEmail
        })
      });

      const result = await response.json();

      if (response.ok) {
        alert('Thank you for your report. We will review it and take appropriate action.');
      } else {
        alert(result.error || 'Failed to submit report. Please try again.');
      }
    } catch (error) {
      console.error('Error reporting business:', error);
      alert('Failed to submit report. Please try again.');
    }
  }

  // Claim Business Function
  async function claimBusiness() {
    const userEmail = localStorage.getItem('userEmail');
    const userId = localStorage.getItem('userId');

    if (!userEmail || !userId) {
      alert('Please sign in to claim this business.');
      return;
    }

    const confirmation = confirm(`Are you the owner or authorized representative of ${displayName}?\n\nBy claiming this business, you confirm that you have the authority to manage its information on ExpatsList.`);

    if (!confirmation) {
      return;
    }

    const businessEmail = prompt('Please provide your business email address for verification:');
    if (!businessEmail) {
      return;
    }

    const businessPhone = prompt('Please provide your business phone number for verification:');
    if (!businessPhone) {
      return;
    }

    try {
      const response = await fetch('/api/business/claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: listingId,
          userId: userId,
          userEmail: userEmail,
          businessEmail: businessEmail,
          businessPhone: businessPhone,
          businessName: displayName
        })
      });

      const result = await response.json();

      if (response.ok) {
        alert('Your claim request has been submitted successfully! We will review it and contact you within 2-3 business days.');
      } else {
        alert(result.error || 'Failed to submit claim request. Please try again.');
      }
    } catch (error) {
      console.error('Error claiming business:', error);
      alert('Failed to submit claim request. Please try again.');
    }
  }

  // Setup Action Buttons
  function setupActionButtons() {
    // Rate Business Button
    const rateButton = document.getElementById('rate-business');
    if (rateButton) {
      rateButton.addEventListener('click', function() {
        if (!window.authFunctions?.getCurrentUser()) {
          window.authFunctions?.showAuthModal();
          return;
        }
        rateBusiness();
      });
    }

    // Report Business Button
    const reportButton = document.getElementById('report-business');
    if (reportButton) {
      reportButton.addEventListener('click', function() {
        reportBusiness();
      });
    }

    // Claim Business Button
    const claimButton = document.getElementById('claim-business');
    if (claimButton) {
      claimButton.addEventListener('click', function() {
        if (!window.authFunctions?.getCurrentUser()) {
          window.authFunctions?.showAuthModal();
          return;
        }
        claimBusiness();
      });
    }

    // Share Business Button
    const shareButton = document.getElementById('share-business');
    if (shareButton) {
      shareButton.addEventListener('click', function() {
        shareProfile();
      });
    }
  }

  // Share Profile Function
  function shareProfile() {
    if (navigator.share) {
      navigator.share({
        title: displayName,
        text: `Check out ${displayName} on ExpatsList`,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('Link copied to clipboard!');
      });
    }
  }

  // Read More Functions with proper data passing - Fixed single click
  window.toggleHeroDescription = function() {
    const descElement = document.getElementById('hero-description');
    const buttonElement = document.getElementById('hero-read-more');
    const fullText = descElement.getAttribute('data-full-text');
    const shortText = descElement.getAttribute('data-short-text');

    if (buttonElement.textContent.trim() === 'Read more') {
      descElement.textContent = fullText;
      buttonElement.textContent = 'Read less';
    } else {
      descElement.textContent = shortText;
      buttonElement.textContent = 'Read more';
    }
  };

  window.toggleAboutDescription = function() {
    const descElement = document.getElementById('about-description');
    const buttonElement = document.getElementById('about-read-more');
    const fullText = descElement.getAttribute('data-full-text');
    const shortText = descElement.getAttribute('data-short-text');

    if (buttonElement.textContent.trim() === 'Read more') {
      descElement.textContent = fullText;
      buttonElement.textContent = 'Read less';
    } else {
      descElement.textContent = shortText;
      buttonElement.textContent = 'Read more';
    }
  };

  // Toggle Features Function - World-class UX/UI
  window.toggleFeatures = function() {
    const hiddenFeatures = document.querySelectorAll('.features-hidden');
    const toggleButton = document.getElementById('features-toggle');

    if (toggleButton.textContent.trim() === 'View more') {
      // Show hidden features with smooth animation
      hiddenFeatures.forEach(feature => {
        feature.style.display = 'block';
        // Add a small delay for smooth appearance
        setTimeout(() => {
          feature.style.opacity = '0';
          feature.style.transform = 'translateY(-10px)';
          feature.style.transition = 'all 0.3s ease-out';
          setTimeout(() => {
            feature.style.opacity = '1';
            feature.style.transform = 'translateY(0)';
          }, 10);
        }, 10);
      });
      toggleButton.textContent = 'View less';
      toggleButton.classList.add('text-indigo-700');
    } else {
      // Hide features with smooth animation
      hiddenFeatures.forEach(feature => {
        feature.style.transition = 'all 0.3s ease-in';
        feature.style.opacity = '0';
        feature.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          feature.style.display = 'none';
        }, 300);
      });
      toggleButton.textContent = 'View more';
      toggleButton.classList.remove('text-indigo-700');
    }
  };
</script>
</Layout>