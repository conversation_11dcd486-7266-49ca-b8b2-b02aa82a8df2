import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URLSearchParams(url.search);
    const q = searchParams.get('q')?.trim() || '';
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100); // Increased default and max

    if (!q) {
      return new Response(JSON.stringify({
        error: 'Search query is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Extract location from query
    const cities = [
      { name: 'Cancún', slug: 'cancun', aliases: ['cancun'] },
      { name: 'Playa del Carmen', slug: 'playadelcarmen', aliases: ['playa del carmen', 'playa', 'playadelcarmen'] },
      { name: '<PERSON><PERSON>', slug: 'tulum', aliases: ['tulum'] },
      { name: 'Puerto Morelos', slug: 'puerto-morelos', aliases: ['puerto morelos', 'puerto-morelos'] },
      { name: '<PERSON><PERSON>jer<PERSON>', slug: 'isla-mujeres', aliases: ['isla mujeres', 'isla-mujeres'] }
    ];

    let extractedLocation = null;
    let searchQuery = q;

    // Check if query contains a location
    for (const city of cities) {
      const allNames = [city.name.toLowerCase(), ...city.aliases];
      for (const cityName of allNames) {
        if (q.toLowerCase().includes(cityName)) {
          extractedLocation = {
            name: city.name,
            slug: city.slug
          };
          // Remove location from search query
          searchQuery = q.toLowerCase()
            .replace(new RegExp(`\\b(in\\s+|near\\s+|at\\s+)?${cityName}\\b`, 'gi'), '')
            .replace(/\s+/g, ' ')
            .trim();
          break;
        }
      }
      if (extractedLocation) break;
    }

    // Enhanced category matching - expand search terms for better results
    const categoryExpansions = {
      'restaurant': ['restaurant', 'food', 'dining', 'cafe', 'bar', 'grill', 'bistro', 'eatery'],
      'food': ['restaurant', 'food', 'dining', 'cafe', 'bar', 'grill', 'bistro', 'eatery'],
      'hotel': ['hotel', 'accommodation', 'lodging', 'resort', 'hostel', 'inn'],
      'bar': ['bar', 'pub', 'cantina', 'brewery', 'lounge', 'nightclub'],
      'shop': ['shop', 'store', 'retail', 'boutique', 'market'],
      'service': ['service', 'repair', 'maintenance', 'professional'],
      'health': ['health', 'medical', 'doctor', 'clinic', 'pharmacy', 'dentist'],
      'beauty': ['beauty', 'salon', 'spa', 'massage', 'cosmetic'],
      'fitness': ['fitness', 'gym', 'yoga', 'exercise', 'sport'],
      'education': ['education', 'school', 'language', 'tutor', 'class']
    };

    // Check if search query matches any category expansion
    let expandedSearchTerms = [searchQuery];
    const lowerSearchQuery = searchQuery.toLowerCase();

    for (const [key, expansions] of Object.entries(categoryExpansions)) {
      if (lowerSearchQuery.includes(key) || expansions.some(term => lowerSearchQuery.includes(term))) {
        expandedSearchTerms = [...new Set([...expandedSearchTerms, ...expansions])];
        break;
      }
    }

    const likePattern = `%${searchQuery}%`;
    const expandedPatterns = expandedSearchTerms.map(term => `%${term}%`);

    // Calculate limits for better result distribution
    const businessLimit = Math.ceil(limit * 0.7); // 70% for businesses
    const classifiedLimit = Math.floor(limit * 0.3); // 30% for classifieds

    // Enhanced location-aware business search with category expansion
    const businessQuery = `
      SELECT l.id, l.slug, l.business_name, l.display_name, l.description_short,
             l.contact_info, l.google_photo_1, l.is_verified_expatslist, l.is_pinned,
             c.name as category_name,
             c.icon_slug as category_icon,
             c.slug as subcategory_slug,
             ci.name as city_name,
             ci.path_slug as city_slug,
             parent_cat.slug as category_slug,
             parent_cat.name as parent_category_name,
             'business' as result_type,
             CASE
               WHEN l.business_name ILIKE $1 THEN 100
               WHEN c.name ILIKE $1 OR parent_cat.name ILIKE $1 THEN 90
               WHEN l.display_name ILIKE $1 THEN 80
               WHEN l.description_short ILIKE $1 THEN 70
               ELSE 60
             END as search_rank
      FROM listings l
      LEFT JOIN categories c ON l.category_primary_id = c.id
      LEFT JOIN categories parent_cat ON c.parent_id = parent_cat.id
      LEFT JOIN cities ci ON l.city_id = ci.id
      WHERE l.listing_status = 'active'
      AND l.deleted_at IS NULL
      ${extractedLocation ? `AND ci.path_slug = $${expandedPatterns.length + 1}` : ''}
      AND (
        l.business_name ILIKE $1
        OR l.display_name ILIKE $1
        OR l.description_short ILIKE $1
        OR c.name ILIKE $1
        OR parent_cat.name ILIKE $1
        OR ci.name ILIKE $1
        ${expandedPatterns.length > 1 ?
          'OR ' + expandedPatterns.slice(1).map((_, i) => `
            (c.name ILIKE $${i + 2} OR parent_cat.name ILIKE $${i + 2} OR l.business_name ILIKE $${i + 2})
          `).join(' OR ') : ''}
      )
      ORDER BY search_rank DESC, l.is_pinned DESC, l.is_verified_expatslist DESC, l.created_at DESC
      LIMIT ${businessLimit}
    `;

    // Enhanced classified search
    const classifiedQuery = `
      SELECT cp.*,
             ci.name as city_name,
             ci.path_slug as city_slug,
             'classified' as result_type,
             CASE
               WHEN cp.title ILIKE $1 THEN 80
               WHEN cp.category ILIKE $1 THEN 70
               WHEN cp.description ILIKE $1 THEN 60
               ELSE 50
             END as search_rank
      FROM classified_posts cp
      LEFT JOIN cities ci ON cp.city_slug = ci.path_slug
      WHERE cp.is_active = true
      ${extractedLocation ? `AND ci.path_slug = $${expandedPatterns.length + 1}` : ''}
      AND (
        cp.title ILIKE $1
        OR cp.description ILIKE $1
        OR cp.category ILIKE $1
        OR ci.name ILIKE $1
        ${expandedPatterns.length > 1 ?
          'OR ' + expandedPatterns.slice(1).map((_, i) => `
            (cp.title ILIKE $${i + 2} OR cp.category ILIKE $${i + 2})
          `).join(' OR ') : ''}
      )
      ORDER BY search_rank DESC, cp.created_at DESC
      LIMIT ${classifiedLimit}
    `;

    // Execute searches with parameter arrays
    const businessParams = [
      ...expandedPatterns,
      ...(extractedLocation ? [extractedLocation.slug] : [])
    ];

    const classifiedParams = [
      ...expandedPatterns,
      ...(extractedLocation ? [extractedLocation.slug] : [])
    ];

    const [businessResults, classifiedResults] = await Promise.all([
      query(businessQuery, businessParams),
      query(classifiedQuery, classifiedParams)
    ]);

    // Combine results
    const allResults = [
      ...(businessResults.rows || []),
      ...(classifiedResults.rows || [])
    ];

    // Sort by type and rank
    allResults.sort((a, b) => {
      if (a.result_type === 'business' && b.result_type === 'classified') return -1;
      if (a.result_type === 'classified' && b.result_type === 'business') return 1;
      return b.search_rank - a.search_rank;
    });

    return new Response(JSON.stringify({
      success: true,
      results: allResults,
      query: {
        originalQuery: q,
        searchQuery: searchQuery,
        extractedLocation: extractedLocation,
        expandedTerms: expandedSearchTerms.length > 1 ? expandedSearchTerms : null
      },
      stats: {
        businessCount: businessResults.rows?.length || 0,
        classifiedCount: classifiedResults.rows?.length || 0,
        totalResults: allResults.length,
        searchEnhanced: expandedSearchTerms.length > 1
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Simple search error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Search failed: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};