/* Custom Flag Icons CSS with correct paths */
.fi {
  position: relative;
  display: inline-block;
  width: 1.333333em;
  height: 1em;
  line-height: 1em;
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}

.fi:before {
  content: " ";
}

/* Country flags */
.fi-mx { background-image: url(/flags/4x3/mx.svg); }
.fi-cr { background-image: url(/flags/4x3/cr.svg); }
.fi-pa { background-image: url(/flags/4x3/pa.svg); }
.fi-co { background-image: url(/flags/4x3/co.svg); }
.fi-ec { background-image: url(/flags/4x3/ec.svg); }
.fi-pe { background-image: url(/flags/4x3/pe.svg); }
.fi-ar { background-image: url(/flags/4x3/ar.svg); }
.fi-cl { background-image: url(/flags/4x3/cl.svg); }
.fi-pt { background-image: url(/flags/4x3/pt.svg); }
.fi-es { background-image: url(/flags/4x3/es.svg); }
.fi-th { background-image: url(/flags/4x3/th.svg); }
.fi-ph { background-image: url(/flags/4x3/ph.svg); }
.fi-my { background-image: url(/flags/4x3/my.svg); }
.fi-cz { background-image: url(/flags/4x3/cz.svg); }
.fi-de { background-image: url(/flags/4x3/de.svg); }
.fi-hu { background-image: url(/flags/4x3/hu.svg); }
.fi-un { background-image: url(/flags/4x3/un.svg); }

/* Add more countries as needed */
.fi-br { background-image: url(/flags/4x3/br.svg); }
.fi-gt { background-image: url(/flags/4x3/gt.svg); }
.fi-ni { background-image: url(/flags/4x3/ni.svg); }
.fi-hn { background-image: url(/flags/4x3/hn.svg); }
.fi-sv { background-image: url(/flags/4x3/sv.svg); }
.fi-bz { background-image: url(/flags/4x3/bz.svg); }
