---
// Authentication modal component
---

<!-- Auth Modal -->
<div id="auth-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-2xl max-w-md w-full p-8 relative">
    <!-- Close Button -->
    <button
      id="close-auth-modal"
      class="absolute top-3 right-3 w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-700 hover:text-slate-900 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      title="Close"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>

    <!-- Sign In Form -->
    <div id="signin-form" class="space-y-6">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-slate-900 mb-2">Welcome Back</h2>
        <p class="text-slate-600">Sign in to your ExpatsLists account</p>
      </div>

      <!-- Error Message -->
      <div id="signin-error" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-3">
            <p id="signin-error-text" class="text-sm text-red-700"></p>
          </div>
          <div class="ml-auto pl-3">
            <button id="close-signin-error" class="text-red-400 hover:text-red-600 transition-colors">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <form id="signin-form-element" class="space-y-4">
        <div>
          <label for="signin-email" class="block text-sm font-medium text-slate-700 mb-2">Email</label>
          <input
            type="email"
            id="signin-email"
            required
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <label for="signin-password" class="block text-sm font-medium text-slate-700 mb-2">Password</label>
          <input
            type="password"
            id="signin-password"
            required
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="••••••••"
          />
        </div>
        <button
          type="submit"
          id="signin-submit"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
        >
          <span id="signin-text">Sign In</span>
          <div id="signin-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </button>
      </form>

      <div class="text-center space-y-2">
        <button
          id="show-forgot-password"
          class="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          Forgot your password?
        </button>
        <div class="text-slate-600 text-sm">
          Don't have an account?
          <button id="show-signup" class="text-blue-600 hover:text-blue-700 font-medium">Sign up</button>
        </div>
      </div>
    </div>

    <!-- Sign Up Form -->
    <div id="signup-form" class="space-y-6 hidden">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-slate-900 mb-2">Create Account</h2>
        <p class="text-slate-600">Join the ExpatsList community and connect with fellow expats</p>
      </div>

      <form id="signup-form-element" class="space-y-4">
        <div>
          <label for="signup-name" class="block text-sm font-medium text-slate-700 mb-2">Display Name</label>
          <input
            type="text"
            id="signup-name"
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="Your name"
          />
        </div>
        <div>
          <label for="signup-email" class="block text-sm font-medium text-slate-700 mb-2">Email</label>
          <input
            type="email"
            id="signup-email"
            required
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <label for="signup-password" class="block text-sm font-medium text-slate-700 mb-2">Password</label>
          <input
            type="password"
            id="signup-password"
            required
            minlength="6"
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="••••••••"
          />
          <div class="text-xs text-slate-500 mt-1">Minimum 6 characters</div>
        </div>

        <button
          type="submit"
          id="signup-submit"
          class="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
        >
          <span id="signup-text">Create Account</span>
          <div id="signup-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </button>
      </form>

      <div class="text-center">
        <div class="text-slate-600 text-sm">
          Already have an account?
          <button id="show-signin" class="text-blue-600 hover:text-blue-700 font-medium">Sign in</button>
        </div>
      </div>
    </div>

    <!-- Forgot Password Form -->
    <div id="forgot-password-form" class="space-y-6 hidden">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-slate-900 mb-2">Reset Password</h2>
        <p class="text-slate-600">Enter your email to receive a reset link</p>
      </div>

      <form id="forgot-password-form-element" class="space-y-4">
        <div>
          <label for="forgot-email" class="block text-sm font-medium text-slate-700 mb-2">Email</label>
          <input
            type="email"
            id="forgot-email"
            required
            class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="<EMAIL>"
          />
        </div>
        <button
          type="submit"
          id="forgot-submit"
          class="w-full bg-slate-600 hover:bg-slate-700 text-white py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
        >
          <span id="forgot-text">Send Reset Link</span>
          <div id="forgot-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </button>
      </form>

      <div class="text-center">
        <button id="back-to-signin" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
          ← Back to sign in
        </button>
      </div>
    </div>

    <!-- Success Message -->
    <div id="auth-success" class="text-center space-y-4 hidden">
      <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 class="text-xl font-bold text-slate-900">Check Your Email</h3>
      <p class="text-slate-600">We've sent you a confirmation link. Please check your email and click the link to complete your registration.</p>
      <button
        id="close-success"
        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
      >
        Got it
      </button>
    </div>
  </div>
</div>

<style>
  #auth-modal {
    backdrop-filter: blur(4px);
  }
</style>
