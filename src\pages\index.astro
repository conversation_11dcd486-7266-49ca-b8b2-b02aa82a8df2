---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import { getCities } from '../lib/database';

// Country data with flag codes and colors
const countryData = {
  'Mexico': { flagCode: 'mx', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Costa Rica': { flagCode: 'cr', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Panama': { flagCode: 'pa', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Colombia': { flagCode: 'co', color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100' },
  'Ecuador': { flagCode: 'ec', color: 'bg-purple-50 border-purple-200 hover:bg-purple-100' },
  'Peru': { flagCode: 'pe', color: 'bg-orange-50 border-orange-200 hover:bg-orange-100' },
  'Argentina': { flagCode: 'ar', color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100' },
  'Chile': { flagCode: 'cl', color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100' },
  'Portugal': { flagCode: 'pt', color: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100' },
  'Spain': { flagCode: 'es', color: 'bg-rose-50 border-rose-200 hover:bg-rose-100' },
  'Thailand': { flagCode: 'th', color: 'bg-pink-50 border-pink-200 hover:bg-pink-100' },
  'Philippines': { flagCode: 'ph', color: 'bg-teal-50 border-teal-200 hover:bg-teal-100' },
  'Malaysia': { flagCode: 'my', color: 'bg-amber-50 border-amber-200 hover:bg-amber-100' },
  'Czech Republic': { flagCode: 'cz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Germany': { flagCode: 'de', color: 'bg-gray-50 border-gray-200 hover:bg-gray-100' },
  'Hungary': { flagCode: 'hu', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Brazil': { flagCode: 'br', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Guatemala': { flagCode: 'gt', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Nicaragua': { flagCode: 'ni', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Honduras': { flagCode: 'hn', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'El Salvador': { flagCode: 'sv', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Belize': { flagCode: 'bz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' }
};

// Get all cities and categories
let allCities = null;
let mexicoCities = [];
let mainCategories = [];
let totalBusinesses = 0;
let cityWeatherData: Record<string, any> = {};

try {
  const { data, error } = await getCities();

  if (error) {
    console.error('Database error:', error);
  } else {
    allCities = data;
    // Filter for Mexico only and sort by listing count
    mexicoCities = data?.filter((city: any) => city.country === 'Mexico')
      .sort((a: any, b: any) => (b.total_listing_count || 0) - (a.total_listing_count || 0)) || [];
    console.log('Mexico cities loaded:', mexicoCities.length);
  }
} catch (e) {
  console.error('Database connection error:', e);
}

// Get main categories for quick filters
try {
  const { getCategories } = await import('../lib/database');
  const { data: categoriesData, error: categoriesError } = await getCategories();
  
  if (!categoriesError && categoriesData) {
    mainCategories = categoriesData.filter((cat: any) => !cat.parent_id);
  }
} catch (e) {
  console.error('Categories error:', e);
}

// Get total business count with caching (shorter TTL for more real-time updates)
try {
  const { getCachedData } = await import('../lib/cache');
  const { query } = await import('../lib/database');
  
  const result = await getCachedData(
    'total_business_count',
    async () => {
      const queryResult = await query("SELECT COUNT(*) as total_businesses FROM listings WHERE listing_status = 'active' AND deleted_at IS NULL");
      return queryResult.rows[0]?.total_businesses || 0;
    },
    2 * 60 * 1000 // 2 minutes cache for more real-time updates
  );
  
  totalBusinesses = result;
} catch (e) {
  console.error('Business count error:', e);
}

// Get total classified count with caching (same TTL as business count)
let totalClassifieds = 0;
try {
  const { getCachedData } = await import('../lib/cache');
  const { query } = await import('../lib/database');

  const result = await getCachedData(
    'total_classified_count',
    async () => {
      const queryResult = await query(`
        SELECT COUNT(*) as total_classifieds
        FROM classified_posts
        WHERE is_active = true
          AND (expires_at IS NULL OR expires_at > NOW())
      `);
      return queryResult.rows[0]?.total_classifieds || 0;
    },
    2 * 60 * 1000 // 2 minutes cache for more real-time updates
  );

  totalClassifieds = result;
} catch (e) {
  console.error('Classified count error:', e);
}

// Get weather data for cities
try {
  const { getWeatherForCities } = await import('../lib/weather');
  const cityNames = mexicoCities.map((city: any) => city.name);
  cityWeatherData = await getWeatherForCities(cityNames);
  console.log('Weather data loaded for', Object.keys(cityWeatherData).length, 'cities');
} catch (e) {
  console.error('Weather data error:', e);
  // Fallback to empty object - will use fallback weather in tiles
  cityWeatherData = {};
}

const pageTitle = 'ExpatsList - Business Directory & Classifieds for Expats';
const pageDescription = 'Your complete expat platform: find trusted local businesses, browse classified ads, and connect with fellow expats. Housing, jobs, services, and community - all in one place.';
---

<Layout title={pageTitle} description={pageDescription}>
  <!-- Ultra-Minimal Navigation - Only shows user menu when authenticated -->
  <Navigation currentPath="/" />

  <main class="min-h-screen bg-slate-50">
    <!-- Hero Section with Search -->
    <div class="gradient-bg relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(59,130,246,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
      </div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-indigo-600/5"></div>

      <div class="relative max-w-6xl mx-auto px-4 sm:px-6 pt-16 sm:pt-12 md:pt-16 pb-4 sm:pb-6 md:pb-8">
        <div class="text-center mb-4 sm:mb-6">
          <h1 class="text-2xl sm:text-3xl md:text-5xl font-bold text-slate-900 mb-3 sm:mb-4 leading-tight">
            Expat<button
              id="expat-info-btn"
              onclick="showDefinition()"
              class="inline-flex items-center justify-center ml-2 w-5 h-5 text-xs text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 rounded-full border border-white align-top"
              title="What's an expat?"
            >
              <span class="font-medium text-xs">?</span>
            </button> business directory and classifieds
          </h1>

          <p class="text-base sm:text-lg md:text-xl text-slate-600 mb-4 sm:mb-6 max-w-3xl mx-auto leading-relaxed">
            <span class="font-semibold text-slate-700">Verified businesses • Reviews • Housing • Buy/Sell • Local Events</span>
          </p>

          <!-- Stats Section - Inline & Compact -->
          <div class="flex flex-wrap items-center justify-center gap-x-6 gap-y-2 text-center mb-4">
            <div class="flex items-baseline space-x-1">
              <span class="text-2xl sm:text-3xl font-bold text-blue-600">{totalBusinesses.toLocaleString()}</span>
              <span class="text-sm text-slate-600 font-medium">Businesses</span>
            </div>
            <div class="hidden sm:block w-px h-6 bg-slate-300"></div>
            <div class="flex items-baseline space-x-1">
              <span class="text-2xl sm:text-3xl font-bold text-emerald-600">{totalClassifieds.toLocaleString()}</span>
              <span class="text-sm text-slate-600 font-medium">Classifieds</span>
            </div>
            <div class="hidden sm:block w-px h-6 bg-slate-300"></div>
            <div class="flex items-baseline space-x-1">
              <span class="text-2xl sm:text-3xl font-bold text-purple-600">5+</span>
              <span class="text-sm text-slate-600 font-medium">Cities</span>
            </div>
            <div class="hidden sm:block w-px h-6 bg-slate-300"></div>
            <div class="flex items-baseline space-x-1">
              <span class="text-2xl sm:text-3xl font-bold text-orange-600">Free</span>
              <span class="text-sm text-slate-600 font-medium">to Use</span>
            </div>
          </div>

          <!-- Dismissible Expat Definition Card -->
          <div id="expat-definition" class="max-w-2xl mx-auto mb-6 hidden">
            <div class="card-premium p-6 relative backdrop-blur-sm border-blue-200/50 animate-slide-up">
              <button
                onclick="dismissDefinition()"
                class="absolute top-2 right-2 w-7 h-7 bg-slate-100 hover:bg-slate-200 text-slate-700 hover:text-slate-900 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="Close definition"
                title="Close"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <div class="flex items-center justify-center mb-3">
                <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mr-2"></div>
                <span class="text-sm font-semibold text-slate-700 uppercase tracking-wider">What's an Expat?</span>
              </div>
              <p class="text-slate-600 leading-relaxed pr-6 text-base">
                An <strong class="text-slate-800">expat</strong> (expatriate) is someone living temporarily outside their home country, often for work, study, or lifestyle reasons. Expats typically prefer to live in specific areas with established expat communities.
              </p>
            </div>
          </div>
        </div>

        <script is:inline>
          // Cookie helper functions
          function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
          }

          function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
              let c = ca[i];
              while (c.charAt(0) === ' ') c = c.substring(1, c.length);
              if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
          }

          // Global functions for the definition modal
          window.dismissDefinition = function() {
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (definition && button) {
              definition.classList.add('hidden');
              button.style.display = 'inline-flex';
              // Set cookie for 365 days (1 year)
              setCookie('expatDefinitionSeen', 'true', 365);
            }
          };

          window.showDefinition = function() {
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (definition && button) {
              definition.classList.remove('hidden');
              button.style.display = 'none';
            }
          };

          // Check if user has seen the definition before
          function checkFirstVisit() {
            const hasSeenDefinition = getCookie('expatDefinitionSeen');
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (!hasSeenDefinition && definition && button) {
              // Show definition automatically for first-time users immediately
              definition.classList.remove('hidden');
              button.style.display = 'none';
            } else if (hasSeenDefinition && definition && button) {
              // Keep definition hidden for returning users
              definition.classList.add('hidden');
              button.style.display = 'inline-flex';
            }
          }

          // Run on page load
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkFirstVisit);
          } else {
            checkFirstVisit();
          }
        </script>

        <!-- Enhanced Search Section -->
        <div class="max-w-6xl mx-auto">
          <!-- Search Box -->
          <div class="relative mb-4 sm:mb-6">
            <form action="/search" method="GET" class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-0">
              <div class="flex-1 relative">
                <div class="absolute inset-y-0 left-0 pl-4 sm:pl-6 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  name="q"
                  placeholder="Search verified businesses, reviews, classifieds..."
                  class="w-full pl-12 sm:pl-16 md:pl-20 pr-4 sm:pr-6 md:pr-8 py-3 sm:py-4 md:py-6 text-base sm:text-lg md:text-xl font-semibold bg-white/95 backdrop-blur-md border-2 sm:border-3 border-blue-300 hover:border-blue-400 focus:border-blue-600 focus:outline-none focus:ring-4 sm:focus:ring-6 focus:ring-blue-200/60 placeholder-slate-500 text-slate-900 rounded-2xl sm:rounded-3xl sm:rounded-l-3xl sm:rounded-r-none shadow-lg sm:shadow-2xl hover:shadow-xl sm:hover:shadow-3xl focus:shadow-xl sm:focus:shadow-3xl transition-all duration-300 sm:duration-400 transform hover:scale-[1.01] sm:hover:scale-[1.02] focus:scale-[1.01] sm:focus:scale-[1.02]"
                />
              </div>
              <button
                type="submit"
                class="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white px-6 sm:px-8 md:px-12 py-3 sm:py-4 md:py-6 text-base sm:text-lg md:text-xl font-black rounded-2xl sm:rounded-3xl sm:rounded-l-none sm:rounded-r-3xl shadow-lg sm:shadow-2xl hover:shadow-xl sm:hover:shadow-3xl transition-all duration-300 sm:duration-400 transform hover:scale-105 active:scale-95 w-full sm:w-auto border-2 sm:border-3 border-blue-600 hover:border-blue-700"
              >
                🔍 Search
              </button>
            </form>
          </div>

          <!-- Quick Category Filters - Business & Classifieds -->
          <div class="flex flex-wrap justify-center gap-1.5 sm:gap-2 mb-4 sm:mb-6">
            <!-- Business Categories -->
            {mainCategories.slice(0, 5).map((category: any) => (
              <a
                href={`/search?q=${encodeURIComponent(category.name)}`}
                class="inline-flex items-center px-2.5 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-white/80 hover:bg-blue-50 border border-slate-200 hover:border-blue-300 rounded-full text-xs sm:text-sm font-medium text-slate-700 hover:text-blue-700 transition-all duration-200 backdrop-blur-sm hover:shadow-md"
              >
                <span class="mr-1 sm:mr-2 text-sm sm:text-base">{category.icon_slug}</span>
                <span class="hidden sm:inline">{category.name}</span>
                <span class="sm:hidden">{category.name.split(' ')[0]}</span>
              </a>
            ))}

            <!-- Classified Categories -->
            <a
              href="/search?q=housing"
              class="inline-flex items-center px-2.5 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-emerald-50/80 hover:bg-emerald-100 border border-emerald-200 hover:border-emerald-300 rounded-full text-xs sm:text-sm font-medium text-emerald-700 hover:text-emerald-800 transition-all duration-200 backdrop-blur-sm hover:shadow-md"
            >
              <span class="mr-1 sm:mr-2 text-sm sm:text-base">🏠</span>
              <span class="hidden sm:inline">Housing Ads</span>
              <span class="sm:hidden">Housing</span>
            </a>
            <a
              href="/search?q=jobs"
              class="inline-flex items-center px-2.5 sm:px-3 md:px-4 py-1.5 sm:py-2 bg-purple-50/80 hover:bg-purple-100 border border-purple-200 hover:border-purple-300 rounded-full text-xs sm:text-sm font-medium text-purple-700 hover:text-purple-800 transition-all duration-200 backdrop-blur-sm hover:shadow-md"
            >
              <span class="mr-1 sm:mr-2 text-sm sm:text-base">💼</span>
              <span class="hidden sm:inline">Job Listings</span>
              <span class="sm:hidden">Jobs</span>
            </a>
          </div>

          <!-- How It Works - Minimal Design -->
          <div class="flex flex-wrap items-center justify-center gap-x-4 gap-y-2 text-sm text-slate-600">
            <div class="flex items-center space-x-1.5">
              <span class="w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
              <span>Choose city</span>
            </div>
            <span class="hidden sm:inline text-slate-400">→</span>
            <div class="flex items-center space-x-1.5">
              <span class="w-5 h-5 bg-emerald-500 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
              <span>Browse</span>
            </div>
            <span class="hidden sm:inline text-slate-400">→</span>
            <div class="flex items-center space-x-1.5">
              <span class="w-5 h-5 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
              <span>Post</span>
            </div>
            <span class="hidden sm:inline text-slate-400">→</span>
            <div class="flex items-center space-x-1.5">
              <span class="w-5 h-5 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-bold">4</span>
              <span>Connect</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cities Section -->
    <div class="bg-gradient-to-b from-slate-50 to-white py-4 sm:py-6 md:py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">

        <!-- Featured Cities Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-8 sm:mb-10 md:mb-12">
          {mexicoCities.filter((city: any) => city.total_listing_count > 0).slice(0, 6).map((city: any, index: number) => {
            // City background images and colors - using better city-representative images
            const cityStyles = {
              'Cancún': { 
                bg: 'bg-gradient-to-br from-cyan-400 via-blue-500 to-blue-600',
                image: 'https://images.unsplash.com/photo-1512813195386-6cf811ad3542?w=400&h=300&fit=crop&crop=center' // Cancun beach resort
              },
              'Playa del Carmen': { 
                bg: 'bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-600',
                image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop&crop=center' // Tropical beach scene
              },
              'Tulum': { 
                bg: 'bg-gradient-to-br from-amber-400 via-orange-500 to-red-500',
                image: 'https://images.unsplash.com/photo-1518638150340-f706e86654de?w=400&h=300&fit=crop&crop=center' // Ancient ruins by ocean
              },
              'Puerto Morelos': { 
                bg: 'bg-gradient-to-br from-purple-400 via-pink-500 to-red-500',
                image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center' // Coastal fishing village
              },
              'Isla Mujeres': { 
                bg: 'bg-gradient-to-br from-indigo-400 via-purple-500 to-pink-500',
                image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&crop=center' // Caribbean island view
              }
            };
            
            // Get weather data for this city
            const weather = cityWeatherData[city.name];
            const temperature = weather ? `${weather.temperature}°` : '27°';
            const weatherIcon = weather ? weather.icon : '🌤️';
            const weatherDesc = weather ? weather.description : 'Perfect weather';
            const uvIndex = weather ? weather.uvIndex : 8;
            const uvLevel = weather ? weather.uvLevel : 'Very High';
            const uvColor = weather ? weather.uvColor : 'text-red-600';
            const airQuality = weather ? weather.airQuality : 25;
            const airQualityLevel = weather ? weather.airQualityLevel : 'Good';
            const airQualityColor = weather ? weather.airQualityColor : 'text-green-600';
            
            const style = cityStyles[city.name] || cityStyles['Playa del Carmen'];
            
            return (
              <a
                href={`/${city.path_slug}`}
                class="group relative overflow-hidden rounded-xl sm:rounded-2xl aspect-[3/2] sm:aspect-[4/3] transition-all duration-300 hover:shadow-2xl shadow-md sm:shadow-lg"
                style={`animation-delay: ${index * 0.1}s`}
              >
                {/* Background Image */}
                <div 
                  class="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-110"
                  style={`background-image: url('${style.image}')`}
                ></div>
                
                {/* Gradient Overlay */}
                <div class={`absolute inset-0 ${style.bg} opacity-60 group-hover:opacity-50 transition-opacity duration-300`}></div>
                
                {/* Dark Overlay for Text Readability */}
                <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                
                {/* Content */}
                <div class="relative h-full flex flex-col justify-between p-3 sm:p-4 md:p-6 text-white">


                  {/* Bottom Section */}
                  <div>
                    <div class="flex justify-between mb-2 sm:mb-3">
                      {/* Left: City Info - Fixed Height Container */}
                      <div class="flex-1 min-w-0 pr-2 sm:pr-4">
                        <div class="h-12 sm:h-14 md:h-16 flex flex-col justify-center">
                          <h3 class="text-lg sm:text-xl md:text-2xl font-bold leading-tight group-hover:text-yellow-200 transition-colors" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1), -1px -1px 0px rgba(0,0,0,0.7), 1px -1px 0px rgba(0,0,0,0.7), -1px 1px 0px rgba(0,0,0,0.7), 1px 1px 0px rgba(0,0,0,0.7);">
                            <span class="bg-black/40 px-2 sm:px-3 py-1 rounded-md sm:rounded-lg backdrop-blur-sm">{city.name}</span>
                          </h3>
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 mt-1">
                          <span class="fi fi-mx" style="font-size: 0.75rem; sm:font-size: 0.875rem;"></span>
                          <p class="text-white/90 text-xs sm:text-sm font-medium" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                            <span class="bg-black/30 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md backdrop-blur-sm">Mexico</span>
                          </p>
                        </div>
                      </div>

                      {/* Right: Weather Info - Fixed Height Container */}
                      <div class="flex-shrink-0">
                        <div class="h-12 sm:h-14 md:h-16 flex flex-col justify-center text-right">
                          <div class="flex items-center space-x-1 text-xs sm:text-sm mb-1" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                            <span class="text-sm sm:text-base bg-black/30 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md backdrop-blur-sm">{weatherIcon}</span>
                            <span class="bg-black/30 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md backdrop-blur-sm">{temperature}</span>
                          </div>
                          <div class="text-xs" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                            <span class="bg-black/25 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md backdrop-blur-sm hidden sm:inline">{weatherDesc}</span>
                            <span class="bg-black/25 px-1.5 py-0.5 rounded-md backdrop-blur-sm sm:hidden">{weatherDesc.split(' ')[0]}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Environmental Info */}
                    <div class="grid grid-cols-2 gap-1.5 sm:gap-2 mb-2 sm:mb-3">
                      <div class="bg-white/15 backdrop-blur-sm rounded-md sm:rounded-lg px-2 sm:px-3 py-1.5 sm:py-2" title={`UV Index: ${uvIndex} - ${uvLevel}. Sun protection ${uvIndex > 5 ? 'strongly recommended' : 'recommended'}.`}>
                        <div class="flex items-center space-x-1 mb-0.5 sm:mb-1">
                          <span class="text-xs">☀️</span>
                          <span class="text-xs font-medium">UV {uvIndex}</span>
                        </div>
                        <div class={`text-xs opacity-90 ${uvIndex > 7 ? 'font-semibold' : ''}`}>{uvLevel}</div>
                      </div>
                      <div class="bg-white/15 backdrop-blur-sm rounded-md sm:rounded-lg px-2 sm:px-3 py-1.5 sm:py-2" title={`Air Quality Index: ${airQuality} - ${airQualityLevel}. European AQI scale.`}>
                        <div class="flex items-center space-x-1 mb-0.5 sm:mb-1">
                          <span class="text-xs">🌬️</span>
                          <span class="text-xs font-medium">AQI {airQuality}</span>
                        </div>
                        <div class={`text-xs opacity-90 ${airQuality > 60 ? 'font-semibold' : ''}`}>{airQualityLevel}</div>
                      </div>
                    </div>

                    {/* Business Count Bar */}
                    <div class="bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-1.5 sm:py-2 flex items-center justify-between">
                      <span class="text-xs sm:text-sm font-medium">Listings</span>
                      <span class="text-xs sm:text-sm font-bold">{city.total_listing_count.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                
                {/* Hover Arrow */}
                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </a>
            );
          })}
        </div>

        <!-- More Cities Grid -->
        {mexicoCities.filter((city: any) => city.total_listing_count > 0).length > 6 && (
          <div class="mb-8 sm:mb-10 md:mb-12">
            <h3 class="text-xl sm:text-2xl font-bold text-slate-900 mb-4 sm:mb-6 md:mb-8 text-center">More Mexico Destinations</h3>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4">
              {mexicoCities.filter((city: any) => city.total_listing_count > 0).slice(6).map((city: any, index: number) => (
                <a
                  href={`/${city.path_slug}`}
                  class="relative bg-gradient-to-br from-white to-slate-50 hover:from-blue-50 hover:to-indigo-50 border border-slate-200 hover:border-blue-300 rounded-lg sm:rounded-xl p-3 sm:p-4 text-center group transition-all duration-200 hover:shadow-lg overflow-hidden"
                  style={`animation-delay: ${(index + 6) * 0.05}s`}
                >
                  <div class="relative z-10">
                    <div class="font-semibold text-slate-900 group-hover:text-blue-700 text-xs sm:text-sm mb-1 sm:mb-2 transition-colors">
                      {city.name}
                    </div>
                    <div class="text-xs text-emerald-700 bg-emerald-50 group-hover:bg-blue-50 group-hover:text-blue-700 px-2 py-1 rounded-full inline-block transition-colors">
                      {city.total_listing_count} businesses
                    </div>
                  </div>
                  <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                </a>
              ))}
            </div>
          </div>
        )}

        <!-- Coming Soon Cities -->
        {mexicoCities.filter((city: any) => city.total_listing_count === 0 || !city.total_listing_count).length > 0 && (
          <div class="mb-8 sm:mb-10 md:mb-12">
            <h3 class="text-lg sm:text-xl font-bold text-slate-700 mb-4 sm:mb-6 text-center">Coming Soon to These Cities</h3>
            <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 sm:gap-3">
              {mexicoCities.filter((city: any) => city.total_listing_count === 0 || !city.total_listing_count).slice(0, 16).map((city: any) => (
                <div class="bg-slate-50 border border-slate-200 rounded-md sm:rounded-lg p-2 sm:p-3 text-center opacity-75">
                  <div class="font-medium text-slate-600 text-xs mb-1">
                    {city.name}
                  </div>
                  <div class="text-xs text-slate-500 bg-slate-200 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full inline-block">
                    Soon
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <!-- Expansion Section -->
        <div class="mt-8 sm:mt-12 md:mt-16 text-center">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 max-w-3xl mx-auto">
            <h3 class="text-xl sm:text-2xl font-bold text-slate-900 mb-3 sm:mb-4">Expanding to More Expat Destinations</h3>
            <p class="text-slate-600 mb-4 sm:mb-6 text-sm sm:text-base md:text-lg">
              We're building comprehensive business directories for the world's top expat destinations. Next up: Costa Rica, Panama, Portugal, and beyond!
            </p>
            <div class="flex items-center justify-center space-x-3 sm:space-x-4 md:space-x-6 mb-4 sm:mb-6">
              <div class="text-center">
                <span class="fi fi-cr block mb-1 sm:mb-2" style="font-size: 1.5rem; sm:font-size: 2rem;"></span>
                <span class="text-xs sm:text-sm font-medium text-slate-700">Costa Rica</span>
              </div>
              <div class="text-center">
                <span class="fi fi-pa block mb-1 sm:mb-2" style="font-size: 1.5rem; sm:font-size: 2rem;"></span>
                <span class="text-xs sm:text-sm font-medium text-slate-700">Panama</span>
              </div>
              <div class="text-center">
                <span class="fi fi-pt block mb-1 sm:mb-2" style="font-size: 1.5rem; sm:font-size: 2rem;"></span>
                <span class="text-xs sm:text-sm font-medium text-slate-700">Portugal</span>
              </div>
              <div class="text-center">
                <span class="fi fi-es block mb-1 sm:mb-2" style="font-size: 1.5rem; sm:font-size: 2rem;"></span>
                <span class="text-xs sm:text-sm font-medium text-slate-700">Spain</span>
              </div>
              <div class="text-center">
                <span class="fi fi-th block mb-1 sm:mb-2" style="font-size: 1.5rem; sm:font-size: 2rem;"></span>
                <span class="text-xs sm:text-sm font-medium text-slate-700">Thailand</span>
              </div>
            </div>
            <div class="text-xs sm:text-sm text-slate-500">
              Know a great business in these countries? <a href="/list-business" class="text-blue-600 hover:text-blue-700 font-medium">Help us build the directory</a>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Ultra Minimal Footer -->
    <footer class="py-2 mt-2 sm:mt-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="text-center">
          <p class="text-slate-400 text-xs">
            © 2025 ExpatsList LLC. Web development by
            <a href="https://vibe8.app" target="_blank" class="text-slate-500 hover:text-slate-600 transition-colors ml-1 underline decoration-slate-300 hover:decoration-slate-400">
              Vibe8.app
            </a>
          </p>
        </div>
      </div>
    </footer>
  </main>
</Layout>
