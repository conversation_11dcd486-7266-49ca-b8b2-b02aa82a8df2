import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    console.log('Delete business request data:', data);

    // Validate required fields
    if (!data.businessId || !data.adminUserId) {
      console.log('Missing required fields:', { businessId: data.businessId, adminUserId: data.adminUserId });
      return new Response(JSON.stringify({
        error: 'Business ID and admin user ID are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // First check if user exists in auth.users (Supabase auth)
    const authUserCheck = await query(`
      SELECT email FROM auth.users WHERE id = $1
    `, [data.adminUserId]);

    console.log('Auth user check result:', authUserCheck.rows);

    // Check admin permissions - either by role or specific email
    const adminCheck = await query(`
      SELECT role, email, id FROM user_profiles
      WHERE id = $1 AND is_active = true
    `, [data.adminUserId]);

    console.log('Admin check result:', adminCheck.rows);

    // Allow if user is admin role OR if <NAME_EMAIL>
    const isAdmin = adminCheck.rows.length > 0 &&
      (adminCheck.rows[0].role === 'administrator' ||
       adminCheck.rows[0].email === '<EMAIL>' ||
       (authUserCheck.rows.length > 0 && authUserCheck.rows[0].email === '<EMAIL>'));

    if (!isAdmin) {
      console.log('Unauthorized access attempt:', {
        userId: data.adminUserId,
        profile: adminCheck.rows[0],
        authUser: authUserCheck.rows[0]
      });
      return new Response(JSON.stringify({
        error: 'Unauthorized: Admin access required'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    console.log('Attempting to delete business with ID:', data.businessId);

    // First check if business exists
    const businessCheck = await query(`
      SELECT id, business_name, listing_status FROM listings WHERE id = $1
    `, [data.businessId]);

    console.log('Business check result:', businessCheck.rows);

    if (businessCheck.rows.length === 0) {
      console.log('Business not found with ID:', data.businessId);
      return new Response(JSON.stringify({
        error: 'Business not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Soft delete the business listing
    const deleteResult = await query(`
      UPDATE listings
      SET deleted_at = NOW(),
          listing_status = 'inactive',
          last_updated_by_admin_at = NOW()
      WHERE id = $1
      RETURNING id, business_name
    `, [data.businessId]);

    console.log('Delete result:', deleteResult.rows);

    if (deleteResult.rows.length === 0) {
      console.log('Delete operation failed for business ID:', data.businessId);
      return new Response(JSON.stringify({
        error: 'Failed to delete business - update returned no rows'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const deletedBusiness = deleteResult.rows[0];

    // Invalidate cache to ensure real-time updates
    try {
      const { invalidateCache } = await import('../../../lib/cache');
      // Get business info for cache invalidation
      const businessInfo = await query(`
        SELECT city_id, category_primary_id FROM listings WHERE id = $1
      `, [data.businessId]);

      if (businessInfo.rows.length > 0) {
        const { city_id, category_primary_id } = businessInfo.rows[0];
        invalidateCache.onListingStatusChange(city_id, category_primary_id, category_primary_id);
      }
    } catch (cacheError) {
      console.error('Cache invalidation error:', cacheError);
      // Don't fail the delete operation if cache invalidation fails
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Business "${deletedBusiness.business_name}" deleted successfully`,
      deletedBusiness: deletedBusiness
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error deleting business:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete business'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
