import type { APIRoute } from 'astro';
import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URLSearchParams(url.search);
    const q = searchParams.get('q')?.trim() || '';
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20);

    if (!q || q.length < 2) {
      return new Response(JSON.stringify({
        suggestions: []
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const likePattern = `%${q}%`;
    const suggestions: any[] = [];

    // Get business name suggestions
    const businessSuggestions = await query(`
      SELECT DISTINCT l.business_name as suggestion, 'business' as type, COUNT(*) as count
      FROM listings l
      WHERE l.listing_status = 'active' 
      AND l.deleted_at IS NULL
      AND l.business_name ILIKE $1
      GROUP BY l.business_name
      ORDER BY count DESC, l.business_name ASC
      LIMIT 5
    `, [likePattern]);

    suggestions.push(...businessSuggestions.rows.map(row => ({
      text: row.suggestion,
      type: 'business',
      icon: '🏢',
      count: parseInt(row.count)
    })));

    // Get category suggestions
    const categorySuggestions = await query(`
      SELECT DISTINCT c.name as suggestion, 'category' as type, COUNT(l.id) as count
      FROM categories c
      LEFT JOIN listings l ON l.category_primary_id = c.id
      WHERE c.name ILIKE $1
      AND l.listing_status = 'active'
      AND l.deleted_at IS NULL
      GROUP BY c.name
      ORDER BY count DESC, c.name ASC
      LIMIT 5
    `, [likePattern]);

    suggestions.push(...categorySuggestions.rows.map(row => ({
      text: row.suggestion,
      type: 'category',
      icon: '📂',
      count: parseInt(row.count)
    })));

    // Get city suggestions
    const citySuggestions = await query(`
      SELECT DISTINCT ci.name as suggestion, 'city' as type, COUNT(l.id) as count
      FROM cities ci
      LEFT JOIN listings l ON l.city_id = ci.id
      WHERE ci.name ILIKE $1
      AND l.listing_status = 'active'
      AND l.deleted_at IS NULL
      GROUP BY ci.name
      ORDER BY count DESC, ci.name ASC
      LIMIT 3
    `, [likePattern]);

    suggestions.push(...citySuggestions.rows.map(row => ({
      text: row.suggestion,
      type: 'city',
      icon: '📍',
      count: parseInt(row.count)
    })));

    // Skip keyword suggestions for now due to SQL complexity

    // Sort by relevance and limit
    const sortedSuggestions = suggestions
      .sort((a, b) => {
        // Prioritize exact matches
        const aExact = a.text.toLowerCase().startsWith(q.toLowerCase()) ? 1 : 0;
        const bExact = b.text.toLowerCase().startsWith(q.toLowerCase()) ? 1 : 0;
        
        if (aExact !== bExact) return bExact - aExact;
        
        // Then by type priority (business > category > city > keyword)
        const typePriority = { business: 4, category: 3, city: 2, keyword: 1 };
        const aPriority = typePriority[a.type] || 0;
        const bPriority = typePriority[b.type] || 0;
        
        if (aPriority !== bPriority) return bPriority - aPriority;
        
        // Finally by count
        return b.count - a.count;
      })
      .slice(0, limit);

    return new Response(JSON.stringify({
      suggestions: sortedSuggestions,
      query: q
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Search suggestions error:', error);
    return new Response(JSON.stringify({
      suggestions: [],
      error: 'Failed to get suggestions'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
