---
import Layout from '../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../components/MinimalAcctButton.astro';
import { getCityBySlug } from '../../../lib/database';
import { getClassifiedCategoryCounts, getRecentClassifiedPosts, CLASSIFIED_CATEGORIES, timeAgo, formatPrice, formatDualPrice } from '../../../lib/classifieds';
import { getCurrencyForCountry } from '../../../lib/currencies';
import Breadcrumb from '../../../components/Breadcrumb.astro';

const { city: citySlug } = Astro.params;

if (!citySlug) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get local currency for the city's country
const localCurrency = getCurrencyForCountry(city.country || 'Mexico');

// Pagination for recent posts
const url = new URL(Astro.request.url);
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 20;
const offset = (currentPage - 1) * itemsPerPage;

// Get category counts and recent posts with proper pagination
const { data: categoryCounts } = await getClassifiedCategoryCounts(citySlug);
const { data: allPosts } = await getRecentClassifiedPosts(citySlug, 1000); // Get all posts for proper pagination

// Implement proper pagination
const totalPosts = allPosts?.length || 0;
const totalPages = Math.ceil(totalPosts / itemsPerPage);
const displayPosts = allPosts?.slice(offset, offset + itemsPerPage) || [];

// Create counts map for easy lookup
const countsMap = new Map();
categoryCounts?.forEach((item: any) => {
  countsMap.set(item.category, parseInt(item.count));
});

// Get total count
const totalCount = categoryCounts?.reduce((sum: number, item: any) => sum + parseInt(item.count), 0) || 0;

const pageTitle = `Classifieds - ${city.name}`;
const pageDescription = `Browse classified ads in ${city.name}. Find housing, professional services, talented expats, items for sale, and community events posted by fellow expats.`;
---

<Layout title={pageTitle} description={pageDescription}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-7xl mx-auto px-6">
        <Breadcrumb
          className="mb-4"
          items={[
            { label: 'Main', href: '/', icon: '🌎' },
            { label: city.name, href: `/${citySlug}` },
            { label: 'Classifieds', isActive: true }
          ]}
        />
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <span class="text-2xl">🏷️</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900">Expat Classifieds</h1>
              <p class="text-slate-600">Connect with fellow expats in {city.name}</p>
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-lg font-bold text-emerald-600">{totalCount}</div>
            <div class="text-xs text-slate-600">total listings</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Compact Categories Grid -->
    <div class="py-4">
      <div class="max-w-7xl mx-auto px-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {Object.entries(CLASSIFIED_CATEGORIES).map(([key, category]) => {
            const count = countsMap.get(key) || 0;
            return (
              <div class="bg-gradient-to-br from-white to-slate-50 border border-slate-200 hover:border-emerald-300 hover:shadow-xl rounded-xl p-4 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-1 cursor-pointer relative" onclick={`window.location.href='/${citySlug}/classifieds/${key}'`}>
                <!-- Header: Icon + Count -->
                <div class="flex items-center justify-between mb-3">
                  <div class="text-2xl group-hover:scale-110 transition-transform duration-300">
                    {category.icon}
                  </div>
                  <div class="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full text-xs font-bold">
                    {count}
                  </div>
                </div>

                <!-- Category Info -->
                <div class="mb-4">
                  <h3 class="font-bold text-slate-900 mb-1 text-sm group-hover:text-emerald-700 transition-colors">
                    {category.name}
                  </h3>
                  <p class="text-xs text-slate-600 line-clamp-1">
                    {category.shortDesc}
                  </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                  <div class="flex-1 bg-slate-100 text-slate-700 px-3 py-2 rounded-lg text-xs font-semibold text-center opacity-75">
                    Browse
                  </div>
                  <button
                    onclick="event.stopPropagation(); handlePostAdClick(this)"
                    data-city-slug={citySlug}
                    data-category={key}
                    class="flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 hover:scale-105 shadow-sm z-10 relative"
                  >
                    Post Ad
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        <!-- Recent Listings -->
        {displayPosts && displayPosts.length > 0 && (
          <div>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-slate-900">Recent Listings</h2>
              <div class="text-sm text-slate-500">
                Showing latest {displayPosts.length} posts
              </div>
            </div>
            <div class="bg-white border border-slate-200 rounded-lg overflow-hidden">
              {displayPosts.map((post: any, index: number) => {
                const category = CLASSIFIED_CATEGORIES[post.category as keyof typeof CLASSIFIED_CATEGORIES];
                return (
                  <div class="border-b border-slate-200 last:border-b-0 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 hover:border-emerald-200 hover:shadow-lg transition-all duration-300 group cursor-pointer relative" onclick={`window.location.href='/${citySlug}/classifieds/post/${post.id}'`}>
                    <div class="p-2 sm:p-3">
                      <!-- Enhanced Layout with Image Support -->
                      <div class="flex items-start gap-3">
                        <!-- Left: Category Icon -->
                        <div class="flex-shrink-0">
                          <div class="w-8 h-8 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-lg flex items-center justify-center">
                            <span class="text-lg">{category.icon}</span>
                          </div>
                        </div>

                        <!-- Image Thumbnail (if available) -->
                        {(post.selected_thumbnail_url || post.image_url) && (
                          <div class="flex-shrink-0 w-16 h-16">
                            <img
                              src={post.selected_thumbnail_url || post.image_url}
                              alt="Listing thumbnail"
                              class="w-full h-full object-cover rounded-lg border border-slate-200"
                              loading="lazy"
                            />
                          </div>
                        )}

                        <!-- Center: Content (Full Width) -->
                        <div class="flex-1 min-w-0">

                          <!-- Title Row -->
                          <div class="flex items-start justify-between gap-2 mb-1">
                            <a
                              href={`/${citySlug}/classifieds/post/${post.id}`}
                              class="font-bold text-slate-900 group-hover:text-emerald-700 transition-colors text-base leading-tight truncate flex-1 hover:underline"
                            >
                              {post.title}
                            </a>

                            <!-- Event Type Badge -->
                            {post.category === 'community' && post.category_specific_data?.event_type && (
                              <span class={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-bold flex-shrink-0 ${
                                post.category_specific_data.event_type === 'useful-links' ? 'bg-emerald-100 text-emerald-800' :
                                post.category_specific_data.event_type === 'meetup' ? 'bg-blue-100 text-blue-800' :
                                post.category_specific_data.event_type === 'event' ? 'bg-purple-100 text-purple-800' :
                                'bg-orange-100 text-orange-800'
                              }`}>
                                {post.category_specific_data.event_type === 'useful-links' && '🔗 Links'}
                                {post.category_specific_data.event_type === 'meetup' && '👥 Meetup'}
                                {post.category_specific_data.event_type === 'event' && '📅 Event'}
                                {post.category_specific_data.event_type === 'discussion' && '💬 Discussion'}
                              </span>
                            )}
                          </div>

                          <!-- Category-Specific Type Information -->
                          <div class="flex items-center gap-2 mb-1">
                            {/* Housing: Listing Type + Property Type */}
                            {post.category === 'housing' && (
                              <div class="flex items-center gap-2 text-xs">
                                {post.category_specific_data?.listing_type && (
                                  <span class="px-2 py-1 rounded-full bg-blue-100 text-blue-800 font-medium">
                                    {(post.category_specific_data.listing_type === 'rent' || post.category_specific_data.listing_type === 'rental') && '🏠 For Rent'}
                                    {post.category_specific_data.listing_type === 'sale' && '🏡 For Sale'}
                                    {post.category_specific_data.listing_type === 'roommate' && '👥 Roommate Wanted'}
                                  </span>
                                )}
                                {post.category_specific_data?.property_type && (
                                  <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                                    {post.category_specific_data.property_type}
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Jobs: Enhanced Listing Type + Category + Rate */}
                            {post.category === 'jobs' && (
                              <div class="flex items-center gap-2 text-xs flex-wrap">
                                {post.category_specific_data?.job_listing_type && (
                                  <span class="px-2 py-1 rounded-full bg-purple-100 text-purple-800 font-medium">
                                    {post.category_specific_data.job_listing_type === 'offering_services' && '🛠️ Offering Services'}
                                    {post.category_specific_data.job_listing_type === 'offering_services' && '🛠️ Offering Services'}
                                    {post.category_specific_data.job_listing_type === 'looking_to_hire' && '🔍 Looking to Hire'}
                                    {post.category_specific_data.job_listing_type === 'looking_for_work' && '💼 Seeking Work'}
                                    {/* Legacy support */}
                                    {post.category_specific_data.job_listing_type === 'job_offered' && '🔍 Looking to Hire'}
                                  </span>
                                )}
                                {post.category_specific_data?.job_category && (
                                  <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium text-xs">
                                    {post.category_specific_data.job_category === 'tutoring-education' && '📚 Education'}
                                    {post.category_specific_data.job_category === 'health-wellness' && '💆 Health & Wellness'}
                                    {post.category_specific_data.job_category === 'cleaning-maintenance' && '🧹 Cleaning'}
                                    {post.category_specific_data.job_category === 'tech-digital' && '💻 Tech & Digital'}
                                    {post.category_specific_data.job_category === 'creative-design' && '🎨 Creative'}
                                    {post.category_specific_data.job_category === 'business-consulting' && '💼 Business'}
                                    {post.category_specific_data.job_category === 'home-services' && '🏠 Home Services'}
                                    {post.category_specific_data.job_category === 'transportation' && '🚗 Transportation'}
                                    {post.category_specific_data.job_category === 'events-entertainment' && '🎉 Events'}
                                    {post.category_specific_data.job_category === 'other' && '🔧 Other'}
                                    {/* Fallback for any other values */}
                                    {!['tutoring-education', 'health-wellness', 'cleaning-maintenance', 'tech-digital', 'creative-design', 'business-consulting', 'home-services', 'transportation', 'events-entertainment', 'other'].includes(post.category_specific_data.job_category) && 
                                      post.category_specific_data.job_category.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
                                    }
                                  </span>
                                )}
                                {post.category_specific_data?.duration_type && (
                                  <span class="px-2 py-1 rounded bg-blue-100 text-blue-700 font-medium text-xs">
                                    {post.category_specific_data.duration_type === 'one-time' && '⚡ One-time'}
                                    {post.category_specific_data.duration_type === 'temporary' && '📅 Temporary'}
                                    {post.category_specific_data.duration_type === 'ongoing' && '🔄 Ongoing'}
                                    {post.category_specific_data.duration_type === 'flexible' && '🤝 Flexible'}
                                  </span>
                                )}
                                {post.rate_type && (
                                  <span class="px-2 py-1 rounded bg-emerald-100 text-emerald-700 font-medium">
                                    {post.rate_type === 'hourly' && '💰 Hourly'}
                                    {post.rate_type === 'daily' && '📅 Daily'}
                                    {post.rate_type === 'weekly' && '📆 Weekly'}
                                    {post.rate_type === 'monthly' && '🗓️ Monthly'}
                                    {post.rate_type === 'project' && '📋 Project'}
                                    {post.rate_type === 'negotiable' && '🤝 Negotiable'}
                                  </span>
                                )}
                                {post.category_specific_data?.cv_url && (
                                  <span class="px-2 py-1 rounded bg-blue-100 text-blue-700 font-medium">
                                    📄 CV/Portfolio
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Buy & Sell: Listing Type + Item Category */}
                            {post.category === 'buy-sell' && (
                              <div class="flex items-center gap-2 text-xs">
                                {post.category_specific_data?.listing_type && (
                                  <span class="px-2 py-1 rounded-full bg-green-100 text-green-800 font-medium">
                                    {post.category_specific_data.listing_type === 'sell' && '💰 For Sale'}
                                    {post.category_specific_data.listing_type === 'buy' && '🔍 Want to Buy'}
                                  </span>
                                )}
                                {post.category_specific_data?.item_category && (
                                  <span class="px-2 py-1 rounded bg-slate-100 text-slate-700 font-medium">
                                    {post.category_specific_data.item_category}
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Community: Social Media Indicators Only (Event type shown in header) */}
                            {post.category === 'community' && (post.whatsapp_group_link || post.telegram_link || post.facebook_link) && (
                              <div class="flex items-center gap-1 text-xs">
                                {post.whatsapp_group_link && (
                                  <span class="px-1.5 py-0.5 rounded bg-green-100 text-green-700 font-medium">💬</span>
                                )}
                                {post.telegram_link && (
                                  <span class="px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 font-medium">✈️</span>
                                )}
                                {post.facebook_link && (
                                  <span class="px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 font-medium">📘</span>
                                )}
                              </div>
                            )}
                          </div>

                          <!-- Description -->
                          <p class="text-sm text-slate-600 leading-snug mb-1 line-clamp-2">
                            {post.description}
                          </p>

                          <!-- Bottom Row: Category + Time/Views/Price -->
                          <div class="flex items-center justify-between gap-2 text-xs">
                            <div class="flex items-center gap-1 flex-wrap">
                              <!-- Category Badge -->
                              <span class="px-2 py-0.5 rounded bg-slate-100 text-slate-700 font-medium">
                                {category.name}
                              </span>


                            </div>

                            <!-- Time + Views + Price -->
                            <div class="flex items-center gap-2 text-slate-500">
                              <span>{timeAgo(post.created_at)}</span>
                              {/* View Count - Following business directory UI pattern */}
                              {post.view_count > 0 && (
                                <div class={`flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${
                                  post.view_count > 100 ? 'bg-emerald-100 text-emerald-700' :
                                  post.view_count > 50 ? 'bg-blue-100 text-blue-700' :
                                  'bg-slate-100 text-slate-600'
                                }`}>
                                  <span>👁️</span>
                                  <span>{post.view_count}</span>
                                </div>
                              )}
                              {(post.price_local || post.price_usd || post.price || post.rate_amount) && (
                                <span class="font-bold text-emerald-600">
                                  {post.rate_amount && post.rate_type ? (
                                    `${post.rate_currency === 'USD' ? '$' : localCurrency.symbol}${post.rate_amount}${
                                      post.rate_type === 'hourly' ? '/hr' :
                                      post.rate_type === 'daily' ? '/day' :
                                      post.rate_type === 'weekly' ? '/wk' :
                                      post.rate_type === 'monthly' ? '/mo' :
                                      post.rate_type === 'project' ? '/project' : ''
                                    }`
                                  ) : post.price_local || post.price_usd ? (
                                    formatDualPrice(post.price_local, post.price_usd, localCurrency.symbol, localCurrency.code)
                                  ) : (
                                    formatPrice(post.price)
                                  )}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <!-- Right: Admin Button -->
                        <div class="flex-shrink-0">
                          <button
                            class="admin-delete-btn hidden px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                            data-post-id={post.id}
                            data-post-title={post.title}
                            onclick="deleteClassifiedPost(this)"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <!-- Pagination -->
            {totalPages > 1 && (
              <div class="flex items-center justify-between mt-8 pt-6 border-t border-slate-200">
                <div class="text-sm text-slate-600">
                  Showing {offset + 1}-{Math.min(offset + itemsPerPage, totalPosts)} of {totalPosts} listings
                </div>

                <div class="flex items-center gap-2">
                  {/* Previous Page */}
                  {currentPage > 1 && (
                    <a
                      href={`/${citySlug}/classifieds?page=${currentPage - 1}`}
                      class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors"
                    >
                      ← Previous
                    </a>
                  )}

                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = i + 1;
                    return (
                      <a
                        href={`/${citySlug}/classifieds?page=${pageNum}`}
                        class={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          pageNum === currentPage
                            ? 'bg-emerald-600 text-white'
                            : 'text-slate-600 hover:text-emerald-600 hover:bg-emerald-50'
                        }`}
                      >
                        {pageNum}
                      </a>
                    );
                  })}

                  {/* Next Page */}
                  {currentPage < totalPages && (
                    <a
                      href={`/${citySlug}/classifieds?page=${currentPage + 1}`}
                      class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors"
                    >
                      Next →
                    </a>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        <!-- Empty State -->
        {(!displayPosts || displayPosts.length === 0) && (
          <div class="text-center py-16">
            <div class="text-6xl mb-6">🏷️</div>
            <h2 class="text-2xl font-bold text-slate-900 mb-4">
              No listings yet
            </h2>
            <p class="text-slate-600 mb-8 max-w-md mx-auto">
              Be the first to post a classified ad in {city.name}! Help build the expat community.
            </p>
            <button
              onclick="handlePostAdClick(this)"
              data-city-slug={citySlug}
              class="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200"
            >
              <span class="mr-2">+</span>
              Post First Listing
            </button>
          </div>
        )}
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug }}>
  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', () => {
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {
        // Show all admin delete buttons
        const deleteButtons = document.querySelectorAll('.admin-delete-btn');
        deleteButtons.forEach(btn => {
          btn.classList.remove('hidden');
        });
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);
  });

  // Global function to delete classified posts
  window.deleteClassifiedPost = async function(button) {
    const postId = button.getAttribute('data-post-id');

    const currentUser = window.authFunctions?.getCurrentUser();
    const userProfile = window.authFunctions?.getUserProfile();

    if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
      alert('Unauthorized - Admin access required');
      return;
    }

    try {
      // Update button to show loading state
      button.disabled = true;
      button.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
      button.classList.add('opacity-75');

      const response = await fetch('/api/admin/delete-post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId: postId,
          adminUserId: currentUser.id
        })
      });

      await response.json();

      if (response.ok) {
        // Show success state briefly
        button.innerHTML = '✅ Deleted';
        button.classList.remove('bg-red-600', 'hover:bg-red-700');
        button.classList.add('bg-green-600');

        // Animate removal after brief success feedback
        setTimeout(() => {
          const postElement = button.closest('div').closest('div');
          if (postElement) {
            postElement.style.transition = 'all 0.3s ease-out';
            postElement.style.transform = 'translateX(-100%)';
            postElement.style.opacity = '0';
            setTimeout(() => postElement.remove(), 300);
          }
        }, 1000);
      } else {
        // Show error state
        button.innerHTML = '❌ Failed';
        button.classList.remove('bg-red-600');
        button.classList.add('bg-red-800');

        // Reset after 2 seconds
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = 'Delete';
          button.classList.remove('bg-red-800', 'opacity-75');
          button.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 2000);
      }
    } catch (error) {
      console.error('Error deleting post:', error);

      // Show error state
      button.innerHTML = '❌ Error';
      button.classList.remove('bg-red-600');
      button.classList.add('bg-red-800');

      // Reset after 2 seconds
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800', 'opacity-75');
        button.classList.add('bg-red-600', 'hover:bg-red-700');
      }, 2000);
    }
  };

  // Handle Post Ad button clicks with smooth auth check
  window.handlePostAdClick = function(buttonElement) {
    // Handle both event object and direct button element
    const button = buttonElement?.target || buttonElement;

    if (!button || !button.getAttribute) {
      console.error('Invalid button element passed to handlePostAdClick');
      return;
    }

    const citySlug = button.getAttribute('data-city-slug');
    const category = button.getAttribute('data-category');

    // Check if auth is ready
    if (!window.authFunctions) {
      // Auth not ready, show loading and retry
      const originalText = button.textContent;
      button.textContent = 'Loading...';
      setTimeout(() => {
        button.textContent = originalText;
        window.handlePostAdClick(button);
      }, 500);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();

    if (currentUser) {
      // User is authenticated, navigate directly
      const url = category ?
        `/${citySlug}/classifieds/create?category=${category}` :
        `/${citySlug}/classifieds/create`;
      window.location.href = url;
    } else {
      // User not authenticated, show auth modal
      window.authFunctions.showAuthModal();
    }
  };

  // Track view counts for classified posts
  function trackClassifiedView(postId) {
    fetch('/api/classifieds/track-view', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ postId })
    }).catch(error => {
      console.error('Error tracking view:', error);
    });
  }

  // Track views when posts are clicked
  document.addEventListener('DOMContentLoaded', () => {
    // Track view when user clicks on a classified post
    document.querySelectorAll('a[href*="/classifieds/post/"]').forEach(link => {
      link.addEventListener('click', () => {
        const href = link.getAttribute('href');
        const postId = href?.split('/').pop();
        if (postId) {
          trackClassifiedView(postId);
        }
      });
    });
  });
</script>

<style>
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
