---
import Layout from '../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../components/MinimalAcctButton.astro';
import { getCityBySlug } from '../../../lib/database';
import { getClassifiedCategoryCounts, getRecentClassifiedPosts, CLASSIFIED_CATEGORIES, timeAgo, formatPrice, formatDualPrice } from '../../../lib/classifieds';
import { getCurrencyForCountry } from '../../../lib/currencies';
import Breadcrumb from '../../../components/Breadcrumb.astro';

const { city: citySlug } = Astro.params;

if (!citySlug) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get local currency for the city's country
const localCurrency = getCurrencyForCountry(city.country || 'Mexico');

// Pagination for recent posts
const url = new URL(Astro.request.url);
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 20;
const offset = (currentPage - 1) * itemsPerPage;

// Get category counts and recent posts with proper pagination
const { data: categoryCounts } = await getClassifiedCategoryCounts(citySlug);
const { data: allPosts } = await getRecentClassifiedPosts(citySlug, 1000); // Get all posts for proper pagination

// Implement proper pagination
const totalPosts = allPosts?.length || 0;
const totalPages = Math.ceil(totalPosts / itemsPerPage);
const displayPosts = allPosts?.slice(offset, offset + itemsPerPage) || [];

// Create counts map for easy lookup
const countsMap = new Map();
categoryCounts?.forEach((item: any) => {
  countsMap.set(item.category, parseInt(item.count));
});

// Get total count
const totalCount = categoryCounts?.reduce((sum: number, item: any) => sum + parseInt(item.count), 0) || 0;

const pageTitle = `Classifieds - ${city.name}`;
const pageDescription = `Browse classified ads in ${city.name}. Find housing, jobs, items for sale, and community events posted by fellow expats.`;
---

<Layout title={pageTitle} description={pageDescription}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-7xl mx-auto px-6">
        <Breadcrumb
          className="mb-4"
          items={[
            { label: 'Main', href: '/', icon: '🌎' },
            { label: city.name, href: `/${citySlug}` },
            { label: 'Classifieds', isActive: true }
          ]}
        />
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <span class="text-2xl">🏷️</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900">Expat Classifieds</h1>
              <p class="text-slate-600">Connect with fellow expats in {city.name}</p>
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-lg font-bold text-emerald-600">{totalCount}</div>
            <div class="text-xs text-slate-600">total listings</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Compact Categories Grid -->
    <div class="py-4">
      <div class="max-w-7xl mx-auto px-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {Object.entries(CLASSIFIED_CATEGORIES).map(([key, category]) => {
            const count = countsMap.get(key) || 0;
            return (
              <div class="bg-gradient-to-br from-white to-slate-50 border border-slate-200 hover:border-emerald-300 hover:shadow-xl rounded-xl p-4 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-1">
                <!-- Header: Icon + Count -->
                <div class="flex items-center justify-between mb-3">
                  <div class="text-2xl group-hover:scale-110 transition-transform duration-300">
                    {category.icon}
                  </div>
                  <div class="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full text-xs font-bold">
                    {count}
                  </div>
                </div>

                <!-- Category Info -->
                <div class="mb-4">
                  <h3 class="font-bold text-slate-900 mb-1 text-sm group-hover:text-emerald-700 transition-colors">
                    {category.name}
                  </h3>
                  <p class="text-xs text-slate-600 line-clamp-1">
                    {category.shortDesc}
                  </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                  <a
                    href={`/${citySlug}/classifieds/${key}`}
                    class="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 text-center hover:scale-105"
                  >
                    Browse
                  </a>
                  <button
                    onclick="handlePostAdClick(this)"
                    data-city-slug={citySlug}
                    data-category={key}
                    class="flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 hover:scale-105 shadow-sm"
                  >
                    Post Ad
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        <!-- Recent Listings -->
        {displayPosts && displayPosts.length > 0 && (
          <div>
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-slate-900">Recent Listings</h2>
              <div class="text-sm text-slate-500">
                Showing latest {displayPosts.length} posts
              </div>
            </div>
            <div class="bg-white border border-slate-200 rounded-lg overflow-hidden">
              {displayPosts.map((post: any, index: number) => {
                const category = CLASSIFIED_CATEGORIES[post.category as keyof typeof CLASSIFIED_CATEGORIES];
                return (
                  <div class={`border-b border-slate-200 last:border-b-0 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 hover:border-emerald-200 hover:shadow-lg transition-all duration-300 group cursor-pointer ${index === 0 ? '' : ''}`}>
                    <div class="p-3 sm:p-4 flex items-start gap-3">
                      <!-- Main clickable content area -->
                      <a
                        href={`/${citySlug}/classifieds/post/${post.id}`}
                        class="flex items-start gap-3 flex-1 min-w-0 hover:text-emerald-600 transition-colors"
                      >
                        <!-- Compact Category Icon + Image Layout -->
                        <div class="flex-shrink-0 flex items-start gap-2">
                          <!-- Category Icon (Compact) -->
                          <div class="w-8 h-8 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-lg">{category.icon}</span>
                          </div>

                          <!-- Image Thumbnail (Compact) -->
                          {post.image_url && (
                            <img
                              src={post.image_url}
                              alt={post.title}
                              class="w-12 h-12 object-cover rounded-lg border border-slate-200 flex-shrink-0"
                              loading="lazy"
                            />
                          )}
                        </div>

                        <!-- Optimized Content Layout -->
                        <div class="flex-1 min-w-0 space-y-1.5">
                          <!-- Line 1: Title (Prominent) + Price -->
                          <div class="flex items-start justify-between gap-3">
                            <h3 class="font-bold text-slate-900 group-hover:text-emerald-700 transition-colors text-base sm:text-lg leading-tight line-clamp-2 flex-1">
                              {post.title}
                            </h3>
                            {(post.price_local || post.price_usd || post.price) && (
                              <div class="text-sm sm:text-base font-bold text-emerald-600 group-hover:text-emerald-700 transition-colors flex-shrink-0">
                                {post.price_local || post.price_usd ?
                                  formatDualPrice(post.price_local, post.price_usd, localCurrency.symbol, localCurrency.code) :
                                  formatPrice(post.price)
                                }
                              </div>
                            )}
                          </div>

                          <!-- Line 2: Category + Type/Subtype Indicators + Time -->
                          <div class="flex items-center justify-between gap-2">
                            <div class="flex items-center gap-1.5 flex-1 flex-wrap">
                              <!-- Category Badge -->
                              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-slate-100 to-slate-50 text-slate-700 border border-slate-200 flex-shrink-0">
                                {category.name}
                              </span>

                              {post.category_specific_data && (
                                <>
                                  {/* Housing indicators */}
                                  {post.category === 'housing' && post.category_specific_data.listing_type && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex-shrink-0">
                                      {post.category_specific_data.listing_type === 'rent' && '🏠 For Rent'}
                                      {post.category_specific_data.listing_type === 'sale' && '🏡 For Sale'}
                                      {post.category_specific_data.listing_type === 'roommate' && '👥 Roommate'}
                                    </span>
                                  )}
                                  {/* Jobs indicators */}
                                  {post.category === 'jobs' && post.category_specific_data.job_listing_type && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 flex-shrink-0">
                                      {post.category_specific_data.job_listing_type === 'job_offered' && '💼 Hiring'}
                                      {post.category_specific_data.job_listing_type === 'looking_for_work' && '🔍 Seeking Work'}
                                    </span>
                                  )}
                                  {/* Buy & Sell indicators */}
                                  {post.category === 'buy-sell' && post.category_specific_data.listing_type && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0">
                                      {post.category_specific_data.listing_type === 'sell' && '💰 For Sale'}
                                      {post.category_specific_data.listing_type === 'buy' && '🔍 Want to Buy'}
                                    </span>
                                  )}
                                  {/* Property/Item type */}
                                  {post.category === 'housing' && post.category_specific_data.housing_type && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-700 flex-shrink-0">
                                      {post.category_specific_data.housing_type}
                                    </span>
                                  )}
                                  {post.category === 'buy-sell' && post.category_specific_data.item_category && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-700 flex-shrink-0">
                                      {post.category_specific_data.item_category}
                                    </span>
                                  )}
                                  {post.category === 'jobs' && post.category_specific_data.job_type && (
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-700 flex-shrink-0">
                                      {post.category_specific_data.job_type}
                                    </span>
                                  )}
                                </>
                              )}
                            </div>
                            <div class="text-xs text-slate-500 flex-shrink-0">
                              {timeAgo(post.created_at)}
                            </div>
                          </div>

                          <!-- Line 3: Enhanced Description (2-3 lines) -->
                          <div class="space-y-1">
                            <p class="text-sm text-slate-600 line-clamp-3 leading-relaxed">
                              {post.description}
                            </p>
                            <!-- Click indicator -->
                            <div class="flex items-center justify-end">
                              <div class="flex items-center text-xs text-slate-400 group-hover:text-emerald-600 group-hover:bg-emerald-50 group-hover:px-2 group-hover:py-1 group-hover:rounded-full transition-all duration-300">
                                <span class="mr-1 hidden sm:inline group-hover:font-medium">Click for details</span>
                                <span class="mr-1 sm:hidden group-hover:font-medium">Details</span>
                                <svg class="w-3 h-3 group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>
                      </a>

                      <!-- Interactive elements outside the main link -->
                      <div class="flex items-start pt-1">
                        <!-- Admin Delete Button (hidden by default) -->
                        <button
                          class="admin-delete-btn hidden px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors flex-shrink-0"
                          data-post-id={post.id}
                          data-post-title={post.title}
                          onclick="deleteClassifiedPost(this)"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <!-- View All Button -->
            <div class="text-center mt-6">
              <a
                href={`/${citySlug}/classifieds`}
                class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium text-sm"
              >
                View All Listings
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </div>
          </div>
        )}

        <!-- Empty State -->
        {(!recentPosts || recentPosts.length === 0) && (
          <div class="text-center py-16">
            <div class="text-6xl mb-6">🏷️</div>
            <h2 class="text-2xl font-bold text-slate-900 mb-4">
              No listings yet
            </h2>
            <p class="text-slate-600 mb-8 max-w-md mx-auto">
              Be the first to post a classified ad in {city.name}! Help build the expat community.
            </p>
            <button
              onclick="handlePostAdClick(this)"
              data-city-slug={citySlug}
              class="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200"
            >
              <span class="mr-2">+</span>
              Post First Listing
            </button>
          </div>
        )}
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug }}>
  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', () => {
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Classifieds Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {
        console.log('Admin detected, showing classified delete buttons');
        // Show all admin delete buttons
        const deleteButtons = document.querySelectorAll('.admin-delete-btn');
        deleteButtons.forEach(btn => {
          btn.classList.remove('hidden');
        });
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);
  });

  // Global function to delete classified posts
  window.deleteClassifiedPost = async function(button) {
    const postId = button.getAttribute('data-post-id');

    const currentUser = window.authFunctions?.getCurrentUser();
    const userProfile = window.authFunctions?.getUserProfile();

    console.log('Delete attempt - User:', currentUser?.email, 'Profile:', userProfile?.role);

    if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
      alert('Unauthorized - Admin access required');
      return;
    }

    try {
      // Update button to show loading state
      button.disabled = true;
      button.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
      button.classList.add('opacity-75');

      const response = await fetch('/api/admin/delete-post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId: postId,
          adminUserId: currentUser.id
        })
      });

      await response.json();

      if (response.ok) {
        // Show success state briefly
        button.innerHTML = '✅ Deleted';
        button.classList.remove('bg-red-600', 'hover:bg-red-700');
        button.classList.add('bg-green-600');

        // Animate removal after brief success feedback
        setTimeout(() => {
          const postElement = button.closest('div').closest('div');
          if (postElement) {
            postElement.style.transition = 'all 0.3s ease-out';
            postElement.style.transform = 'translateX(-100%)';
            postElement.style.opacity = '0';
            setTimeout(() => postElement.remove(), 300);
          }
        }, 1000);
      } else {
        // Show error state
        button.innerHTML = '❌ Failed';
        button.classList.remove('bg-red-600');
        button.classList.add('bg-red-800');

        // Reset after 2 seconds
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = 'Delete';
          button.classList.remove('bg-red-800', 'opacity-75');
          button.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 2000);
      }
    } catch (error) {
      console.error('Error deleting post:', error);

      // Show error state
      button.innerHTML = '❌ Error';
      button.classList.remove('bg-red-600');
      button.classList.add('bg-red-800');

      // Reset after 2 seconds
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800', 'opacity-75');
        button.classList.add('bg-red-600', 'hover:bg-red-700');
      }, 2000);
    }
  };

  // Handle Post Ad button clicks with smooth auth check
  window.handlePostAdClick = function(buttonElement) {
    // Handle both event object and direct button element
    const button = buttonElement?.target || buttonElement;

    if (!button || !button.getAttribute) {
      console.error('Invalid button element passed to handlePostAdClick');
      return;
    }

    const citySlug = button.getAttribute('data-city-slug');
    const category = button.getAttribute('data-category');

    // Check if auth is ready
    if (!window.authFunctions) {
      // Auth not ready, show loading and retry
      const originalText = button.textContent;
      button.textContent = 'Loading...';
      setTimeout(() => {
        button.textContent = originalText;
        window.handlePostAdClick(button);
      }, 500);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();

    if (currentUser) {
      // User is authenticated, navigate directly
      const url = category ?
        `/${citySlug}/classifieds/create?category=${category}` :
        `/${citySlug}/classifieds/create`;
      window.location.href = url;
    } else {
      // User not authenticated, show auth modal
      window.authFunctions.showAuthModal();
    }
  };
</script>

<style>
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
