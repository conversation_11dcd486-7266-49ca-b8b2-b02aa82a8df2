---
// Cities directory page: /cities
import Layout from '../../layouts/Layout.astro';
import { getCities } from '../../lib/database';

// Country data with flag codes and colors
const countryData = {
  'Mexico': { flagCode: 'mx', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Costa Rica': { flagCode: 'cr', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Panama': { flagCode: 'pa', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Colombia': { flagCode: 'co', color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100' },
  'Ecuador': { flagCode: 'ec', color: 'bg-purple-50 border-purple-200 hover:bg-purple-100' },
  'Peru': { flagCode: 'pe', color: 'bg-orange-50 border-orange-200 hover:bg-orange-100' },
  'Argentina': { flagCode: 'ar', color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100' },
  'Chile': { flagCode: 'cl', color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100' },
  'Portugal': { flagCode: 'pt', color: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100' },
  'Spain': { flagCode: 'es', color: 'bg-rose-50 border-rose-200 hover:bg-rose-100' },
  'Thailand': { flagCode: 'th', color: 'bg-pink-50 border-pink-200 hover:bg-pink-100' },
  'Philippines': { flagCode: 'ph', color: 'bg-teal-50 border-teal-200 hover:bg-teal-100' },
  'Malaysia': { flagCode: 'my', color: 'bg-amber-50 border-amber-200 hover:bg-amber-100' },
  'Czech Republic': { flagCode: 'cz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Germany': { flagCode: 'de', color: 'bg-gray-50 border-gray-200 hover:bg-gray-100' },
  'Hungary': { flagCode: 'hu', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Brazil': { flagCode: 'br', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Guatemala': { flagCode: 'gt', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Nicaragua': { flagCode: 'ni', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Honduras': { flagCode: 'hn', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'El Salvador': { flagCode: 'sv', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Belize': { flagCode: 'bz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' }
};

// Get all cities
const { data: allCities, error } = await getCities();

if (error) {
  console.error('Database error:', error);
}

// Organize cities by country with Mexico first
const citiesByCountry: Record<string, any[]> = {};
if (allCities) {
  allCities.forEach((city: any) => {
    if (!citiesByCountry[city.country]) {
      citiesByCountry[city.country] = [];
    }
    citiesByCountry[city.country].push(city);
  });
}

// Sort countries with Mexico first
const sortedCountries = Object.keys(citiesByCountry).sort((a, b) => {
  if (a === 'Mexico') return -1;
  if (b === 'Mexico') return 1;
  return a.localeCompare(b);
});

const pageTitle = 'All Cities - ExpatsList';
const pageDescription = 'Browse all expat-friendly cities on ExpatsList. Find local businesses and services recommended by fellow expats worldwide.';
---

<Layout title={pageTitle} description={pageDescription}>
  <main class="min-h-screen bg-slate-50">
    <!-- Enhanced Breadcrumb Navigation -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-40 shadow-sm">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
          <a href="/" class="hover:text-blue-600 transition-colors font-medium px-2 py-1 rounded-md hover:bg-blue-50">
            🌎 Main
          </a>
          <span class="text-gray-400 text-lg">›</span>
          <span class="text-blue-600 font-semibold bg-blue-50 px-3 py-1 rounded-md">
            🏙️ Cities
          </span>
        </nav>
      </div>
    </div>

    <!-- Hero Section -->
    <div class="gradient-bg relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(59,130,246,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
      </div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-indigo-600/5"></div>

      <div class="relative max-w-7xl mx-auto px-4 py-20">
        <div class="text-center">
          <h1 class="text-5xl md:text-6xl font-bold text-slate-900 mb-8 animate-fade-in">
            Explore <span class="text-gradient">Expat-Friendly</span> Cities
          </h1>
          <p class="text-xl md:text-2xl text-slate-600 mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
            Discover trusted local businesses and services in popular expat destinations around the world
          </p>

          <!-- Premium Search Cities -->
          <div class="max-w-2xl mx-auto animate-slide-up">
            <div class="card-premium p-3 backdrop-blur-sm border-white/50">
              <div class="relative">
                <input
                  type="text"
                  id="city-search"
                  placeholder="Search cities..."
                  class="w-full px-6 py-5 pl-14 bg-transparent border-0 focus:outline-none placeholder-slate-400 text-slate-700 font-medium text-lg rounded-xl"
                />
                <div class="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                  <svg class="h-6 w-6 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cities by Country -->
    <div class="max-w-7xl mx-auto px-4 py-20">
      {sortedCountries.map((country, countryIndex) => (
        <div class="mb-20" data-country={country.toLowerCase()} style={`animation-delay: ${countryIndex * 0.1}s`}>
          <!-- Country Header -->
          <div class="flex items-center justify-center mb-12">
            <span class={`fi fi-${(countryData as any)[country]?.flagCode || 'un'} mr-6 animate-float`} style="font-size: 3rem; line-height: 1;"></span>
            <h2 class="text-3xl md:text-4xl font-bold text-slate-900">{country}</h2>
            <span class="ml-6 bg-gradient-to-r from-blue-50 to-indigo-50 text-slate-700 px-4 py-2 rounded-full text-sm font-semibold border border-blue-100">
              {citiesByCountry[country].length} {citiesByCountry[country].length === 1 ? 'city' : 'cities'}
            </span>
          </div>

          <!-- Cities Grid -->
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {citiesByCountry[country].map((city: any, cityIndex: number) => (
              <a
                href={`/${city.path_slug}`}
                class="card-premium p-8 text-center group hover:shadow-lg transition-all duration-300 animate-slide-up relative overflow-hidden"
                data-city={city.name.toLowerCase()}
                style={`animation-delay: ${(countryIndex * 0.1) + (cityIndex * 0.05)}s`}
              >
                <!-- Premium Background Gradient -->
                <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <!-- City Name -->
                <div class="relative z-10 font-bold text-slate-900 mb-4 group-hover:text-blue-700 text-xl transition-colors">
                  {city.name}
                </div>

                <!-- Stats -->
                <div class="relative z-10 space-y-3">
                  {city.total_listing_count > 0 ? (
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/60 rounded-xl p-4 shadow-sm">
                      <div class="text-2xl font-bold text-green-700 mb-1">
                        {city.total_listing_count.toLocaleString()}
                      </div>
                      <div class="text-sm text-green-600 font-semibold uppercase tracking-wide">
                        {city.total_listing_count === 1 ? 'Business' : 'Businesses'}
                      </div>
                      <div class="mt-2 flex items-center justify-center">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Active
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200/60 rounded-xl p-4 shadow-sm">
                      <div class="text-xl font-bold text-amber-700 mb-1">
                        🚀 Coming Soon
                      </div>
                      <div class="text-sm text-amber-600">
                        Be the first to add businesses
                      </div>
                      <div class="mt-2 flex items-center justify-center">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                          ⏳ Launching
                        </span>
                      </div>
                    </div>
                  )}

                  {city.verified_listing_count > 0 && (
                    <div class="text-xs text-emerald-700 bg-emerald-50 px-3 py-1 rounded-full font-semibold border border-emerald-100">
                      {city.verified_listing_count} verified
                    </div>
                  )}
                </div>

                <!-- View Button -->
                <div class="mt-6 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span class="text-blue-600 text-sm font-semibold">
                    Explore {city.name} →
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      ))}
    </div>

    <!-- Empty State -->
    {(!allCities || allCities.length === 0) && (
      <div class="max-w-4xl mx-auto px-4 py-16 text-center">
        <div class="text-8xl mb-6">🌍</div>
        <h2 class="text-3xl font-bold text-gray-900 mb-4">No cities available</h2>
        <p class="text-xl text-gray-600 mb-8">
          We're working on adding more cities. Check back soon!
        </p>
        <a
          href="/"
          class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
        >
          Back to Main
        </a>
      </div>
    )}

    <!-- Call to Action -->
    <div class="bg-blue-600">
      <div class="max-w-7xl mx-auto px-4 py-16 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">
          Don't see your city?
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
          Help us expand ExpatsList to your city. Be the first to add businesses and build the expat community.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/add-your-business"
            class="bg-white text-blue-600 px-8 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold"
          >
            Add Your Business
          </a>
          <a
            href="/contact"
            class="border border-white text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
          >
            Request New City
          </a>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  // City search functionality
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('city-search') as HTMLInputElement;
    const countryContainers = document.querySelectorAll('[data-country]');
    const cityCards = document.querySelectorAll('[data-city]');

    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = (e.target as HTMLInputElement).value.toLowerCase().trim();

        if (searchTerm === '') {
          // Show all cities and countries
          countryContainers.forEach(container => {
            (container as HTMLElement).style.display = 'block';
          });
          cityCards.forEach(card => {
            (card as HTMLElement).style.display = 'block';
          });
        } else {
          // Filter cities
          let hasVisibleCities: Record<string, boolean> = {};

          cityCards.forEach(card => {
            const cityName = card.getAttribute('data-city') || '';
            const countryName = card.closest('[data-country]')?.getAttribute('data-country') || '';

            if (cityName.includes(searchTerm) || countryName.includes(searchTerm)) {
              (card as HTMLElement).style.display = 'block';
              hasVisibleCities[countryName] = true;
            } else {
              (card as HTMLElement).style.display = 'none';
            }
          });

          // Show/hide country containers based on visible cities
          countryContainers.forEach(container => {
            const countryName = container.getAttribute('data-country') || '';
            if (hasVisibleCities[countryName]) {
              (container as HTMLElement).style.display = 'block';
            } else {
              (container as HTMLElement).style.display = 'none';
            }
          });
        }
      });
    }
  });
</script>
