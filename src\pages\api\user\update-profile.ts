import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.id || !data.email) {
      return new Response(JSON.stringify({
        error: 'User ID and email are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate bio length if provided
    if (data.bio && data.bio.length > 500) {
      return new Response(JSON.stringify({
        error: 'Bio must be 500 characters or less'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if profile exists
    const existingProfile = await query(`
      SELECT id FROM user_profiles WHERE id = $1
    `, [data.id]);

    let result;

    if (existingProfile.rows.length > 0) {
      // Update existing profile
      result = await query(`
        UPDATE user_profiles 
        SET 
          display_name = $2,
          bio = $3,
          location = $4,
          updated_at = NOW()
        WHERE id = $1
        RETURNING id, email, display_name, bio, location, updated_at
      `, [
        data.id,
        data.display_name || null,
        data.bio || null,
        data.location || null
      ]);
    } else {
      // Create new profile
      result = await query(`
        INSERT INTO user_profiles (
          id, email, display_name, bio, location, 
          role, is_verified, is_active, total_posts,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, 'classified_user', false, true, 0, NOW(), NOW()
        )
        RETURNING id, email, display_name, bio, location, updated_at
      `, [
        data.id,
        data.email,
        data.display_name || null,
        data.bio || null,
        data.location || null
      ]);
    }

    if (result.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Failed to update profile'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updatedProfile = result.rows[0];

    return new Response(JSON.stringify({
      success: true,
      profile: {
        id: updatedProfile.id,
        email: updatedProfile.email,
        display_name: updatedProfile.display_name,
        bio: updatedProfile.bio,
        location: updatedProfile.location,
        updated_at: updatedProfile.updated_at
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
