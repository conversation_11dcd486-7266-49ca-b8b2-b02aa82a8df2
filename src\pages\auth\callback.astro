---
// Auth callback page to handle email confirmations and redirects
import Layout from '../../layouts/Layout.astro';
---

<Layout title="Authentication" description="Completing authentication...">
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl max-w-md w-full p-8 text-center">
      <div id="loading-state" class="space-y-4">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
          <div class="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
        <h2 class="text-2xl font-bold text-slate-900">Completing Sign In</h2>
        <p class="text-slate-600">Please wait while we verify your account...</p>
      </div>

      <div id="success-state" class="space-y-4 hidden">
        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
          <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-slate-900">Welcome!</h2>
        <p class="text-slate-600">Your account has been verified successfully. Redirecting you now...</p>
      </div>

      <div id="error-state" class="space-y-4 hidden">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-slate-900">Authentication Failed</h2>
        <p class="text-slate-600" id="error-message">There was an issue verifying your account. Please try again.</p>
        <a 
          href="/" 
          class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
        >
          Return Home
        </a>
      </div>
    </div>
  </div>

  <script type="module">
    // Import Supabase client
    import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

    // Initialize Supabase client
    const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co'
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM'

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    })

    // Get DOM elements
    const loadingState = document.getElementById('loading-state')
    const successState = document.getElementById('success-state')
    const errorState = document.getElementById('error-state')
    const errorMessage = document.getElementById('error-message')

    function showState(state) {
      loadingState.classList.add('hidden')
      successState.classList.add('hidden')
      errorState.classList.add('hidden')
      
      if (state === 'loading') loadingState.classList.remove('hidden')
      else if (state === 'success') successState.classList.remove('hidden')
      else if (state === 'error') errorState.classList.remove('hidden')
    }

    async function handleAuthCallback() {
      try {
        // Check if there are auth parameters in the URL
        const urlParams = new URLSearchParams(window.location.search)
        const accessToken = urlParams.get('access_token')
        const refreshToken = urlParams.get('refresh_token')
        const error = urlParams.get('error')
        const errorDescription = urlParams.get('error_description')

        if (error) {
          console.error('Auth callback error:', error, errorDescription)
          errorMessage.textContent = errorDescription || 'Authentication failed. Please try again.'
          showState('error')
          return
        }

        // Let Supabase handle the session from URL
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (sessionError) {
          console.error('Session error:', sessionError)
          errorMessage.textContent = 'Failed to establish session. Please try signing in again.'
          showState('error')
          return
        }

        if (session?.user) {
          // User is authenticated - profile should exist due to database trigger
          console.log('Email confirmation successful for:', session.user.email)

          // Show success and redirect
          showState('success')

          // Redirect after a short delay
          setTimeout(() => {
            window.location.href = '/'
          }, 2000)
        } else {
          // No session found
          errorMessage.textContent = 'No valid session found. Please try signing in again.'
          showState('error')
        }

      } catch (error) {
        console.error('Auth callback error:', error)
        errorMessage.textContent = 'An unexpected error occurred. Please try again.'
        showState('error')
      }
    }

    // Handle auth callback on page load
    handleAuthCallback()
  </script>
</Layout>
