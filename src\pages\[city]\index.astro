---
// Modern city page with real business counts and background images
import Layout from '../../layouts/Layout.astro';
import StructuredData from '../../components/StructuredData.astro';
import ClassifiedsSection from '../../components/ClassifiedsSection.astro';
import MinimalAcctButton from '../../components/MinimalAcctButton.astro';
import { getCityBySlug, getCategories, query } from '../../lib/database';
import Breadcrumb from '../../components/Breadcrumb.astro';

// Set cache headers for better performance
Astro.response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=600'); // 5 min browser, 10 min CDN

// Get categories and subcategories from database
const { data: allCategories } = await getCategories();

// Icon mapping function
function getIconForCategory(slug: string): string {
  const iconMap: Record<string, string> = {
    'housing-relocation': '🏠',
    'health-wellness': '🧑‍⚕️',
    'food-dining': '🍽️',
    'professional-legal-financial': '💼',
    'everyday-services-repairs': '🛠️',
    'shopping-leisure-community': '🛍️',
    'education-childcare': '🎓'
  };
  return iconMap[slug] || '📁';
}

// Subcategory icon mapping function
function getIconForSubcategory(slug: string): string {
  const subcategoryIconMap: Record<string, string> = {
    // Housing & Relocation
    'rental-agencies': '🏢',
    'real-estate-agents': '🏘️',
    'property-management': '🏠',
    'moving-shipping-companies': '📦',
    'car-rental': '🚗',
    'furniture-stores': '🛋️',

    // Health & Wellness
    'medical-services': '🏥',
    'emergency-medical-services': '🚑',
    'pharmacies': '💊',
    'mental-health-therapy': '🧠',
    'alternative-complementary-medicine': '🌿',
    'fitness-gyms-personal-trainers': '💪',
    'massage-spas-beauty-salons': '💆',
    'yoga-meditation-studios': '🧘',

    // Food & Dining
    'restaurants': '🍽️',
    'cafes-coffee-shops': '☕',
    'grocery-stores-specialty-foods': '🛒',
    'local-food-markets-street-food': '🥬',

    // Professional, Legal & Financial
    'legal-immigration-services': '⚖️',
    'financial-services': '💰',
    'insurance-brokers': '🛡️',
    'business-support': '💼',
    'coworking-spaces': '💻',
    'currency-exchange': '💱',

    // Everyday Services & Repairs
    'connectivity': '📶',
    'home-maintenance-repair': '🔧',
    'appliance-repair': '🔌',
    'clothing-shoe-repair-alterations': '👕',
    'cleaning-services': '🧽',
    'gardeners-pool-maintenance': '🌱',
    'pet-services': '🐕',
    'vehicle-services': '🚗',
    'locksmiths': '🔑',

    // Shopping, Leisure & Community
    'retail-shopping': '🛍️',
    'tour-agencies': '🗺️',
    'bars-nightclubs': '🍻',
    'home-improvement': '🔨',
    'extra-curricular-activities': '🎨',
    'community-social': '👥',

    // Private Education & Childcare
    'schools': '🏫',
    'preschools-daycares': '👶',
    'language-schools': '📚',
    'after-school-programs-tutoring-workshops': '🎓'
  };
  return subcategoryIconMap[slug] || '📋';
}

// Background image mapping for subcategories
function getBackgroundImageForSubcategory(slug: string): string {
  const imageMap: Record<string, string> = {
    // Housing & Relocation
    'rental-agencies': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop&crop=center', // Modern apartment building
    'real-estate-agents': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop&crop=center', // Luxury home exterior
    'property-management': 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop&crop=center', // Professional building
    'moving-shipping-companies': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center', // Moving boxes
    'car-rental': 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop&crop=center', // Car lineup
    'furniture-stores': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center', // Modern furniture showroom

    // Health & Wellness
    'medical-services': 'https://images.unsplash.com/photo-**********-a9333d879b1f?w=400&h=300&fit=crop&crop=center', // Medical clinic
    'emergency-medical-services': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center', // Emergency medical
    'pharmacies': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&crop=center', // Pharmacy interior
    'mental-health-therapy': 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=300&fit=crop&crop=center', // Therapy office
    'alternative-complementary-medicine': 'https://images.unsplash.com/photo-**********-4ab6ce6db874?w=400&h=300&fit=crop&crop=center', // Natural medicine
    'fitness-gyms-personal-trainers': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center', // Modern gym
    'massage-spas-beauty-salons': 'https://images.unsplash.com/photo-**********-73207b1ef5b8?w=400&h=300&fit=crop&crop=center', // Spa treatment
    'yoga-meditation-studios': 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&crop=center', // Yoga studio

    // Food & Dining
    'restaurants': 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop&crop=center', // Restaurant interior
    'cafes-coffee-shops': 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=400&h=300&fit=crop&crop=center', // Coffee shop
    'grocery-stores-specialty-foods': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&crop=center', // Grocery store
    'local-food-markets-street-food': 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop&crop=center', // Food market

    // Professional, Legal & Financial
    'legal-immigration-services': 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=300&fit=crop&crop=center', // Legal office
    'financial-services': 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop&crop=center', // Financial office
    'insurance-brokers': 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=400&h=300&fit=crop&crop=center', // Insurance office
    'business-support': 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop&crop=center', // Business office
    'coworking-spaces': 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=400&h=300&fit=crop&crop=center', // Coworking space
    'currency-exchange': 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=300&fit=crop&crop=center', // Currency exchange

    // Everyday Services & Repairs
    'connectivity': 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop&crop=center', // Internet/tech
    'home-maintenance-repair': 'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=300&fit=crop&crop=center', // Home repair
    'appliance-repair': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center', // Appliance repair
    'clothing-shoe-repair-alterations': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop&crop=center', // Tailor shop
    'cleaning-services': 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=300&fit=crop&crop=center', // Cleaning supplies
    'gardeners-pool-maintenance': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop&crop=center', // Garden maintenance
    'pet-services': 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center', // Pet services
    'vehicle-services': 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=400&h=300&fit=crop&crop=center', // Auto repair
    'locksmiths': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center', // Locksmith tools

    // Shopping, Leisure & Community
    'retail-shopping': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop&crop=center', // Shopping center
    'tour-agencies': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=300&fit=crop&crop=center', // Travel agency
    'bars-nightclubs': 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400&h=300&fit=crop&crop=center', // Bar interior
    'home-improvement': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop&crop=center', // Home improvement
    'extra-curricular-activities': 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&crop=center', // Activity center
    'community-social': 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=400&h=300&fit=crop&crop=center', // Community center

    // Private Education & Childcare
    'schools': 'https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=400&h=300&fit=crop&crop=center', // School classroom
    'preschools-daycares': 'https://images.unsplash.com/photo-1587654780291-39c9404d746b?w=400&h=300&fit=crop&crop=center', // Daycare center
    'language-schools': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=300&fit=crop&crop=center', // Language classroom
    'after-school-programs-tutoring-workshops': 'https://images.unsplash.com/photo-1509062522246-3755977927d7?w=400&h=300&fit=crop&crop=center' // Tutoring session
  };
  return imageMap[slug] || 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop&crop=center';
}

const { city: citySlug } = Astro.params;

if (!citySlug) {
  return Astro.redirect('/cities');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/cities');
}

// Get weather data for the city
let weatherData = null;
try {
  // Use city name-based weather function instead of coordinates to avoid undefined errors
  const { getWeatherForCity } = await import('../../lib/weather');
  weatherData = await getWeatherForCity(city.name);
} catch (error) {
  console.error('Weather data error:', error);
}

// Get real business counts for each category
const categoryCountsQuery = `
  SELECT
    parent_cat.id as category_id,
    parent_cat.slug as category_slug,
    parent_cat.name as category_name,
    COUNT(l.id) as business_count
  FROM categories parent_cat
  LEFT JOIN categories c ON c.parent_id = parent_cat.id
  LEFT JOIN listings l ON l.category_primary_id = c.id
    AND l.city_id = $1
    AND l.listing_status = 'active'
    AND l.deleted_at IS NULL
  WHERE parent_cat.parent_id IS NULL
  GROUP BY parent_cat.id, parent_cat.slug, parent_cat.name
  ORDER BY parent_cat.sort_order
`;

const { rows: categoryCounts } = await query(categoryCountsQuery, [city.id]);

// Create a map for quick lookup
const categoryCountMap = {};
categoryCounts.forEach(row => {
  categoryCountMap[row.category_slug] = row.business_count;
});

// Get business counts for each subcategory
const subcategoryCountsQuery = `
  SELECT
    c.id as subcategory_id,
    c.slug as subcategory_slug,
    c.name as subcategory_name,
    c.parent_id as parent_category_id,
    parent_cat.slug as parent_category_slug,
    COUNT(l.id) as business_count
  FROM categories c
  LEFT JOIN categories parent_cat ON c.parent_id = parent_cat.id
  LEFT JOIN listings l ON l.category_primary_id = c.id
    AND l.city_id = $1
    AND l.listing_status = 'active'
    AND l.deleted_at IS NULL
  WHERE c.parent_id IS NOT NULL
  GROUP BY c.id, c.slug, c.name, c.parent_id, parent_cat.slug
  ORDER BY c.sort_order
`;

const { rows: subcategoryCounts } = await query(subcategoryCountsQuery, [city.id]);

// Create a map for subcategory counts
const subcategoryCountMap = {};
subcategoryCounts.forEach(row => {
  subcategoryCountMap[row.subcategory_slug] = row.business_count;
});

// Organize categories into main categories with their subcategories
const premiumCategories = allCategories
  ?.filter(cat => cat.parent_id === null) // Main categories only
  ?.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
  ?.map(mainCategory => {
    const subcategories = allCategories
      ?.filter(cat => cat.parent_id === mainCategory.id)
      ?.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
      ?.map(sub => ({
        name: sub.name,
        slug: sub.slug,
        businessCount: subcategoryCountMap[sub.slug] || 0
      })) || [];

    return {
      id: mainCategory.slug,
      name: mainCategory.name,
      icon: getIconForCategory(mainCategory.slug),
      subcategories,
      totalBusinessCount: categoryCountMap[mainCategory.slug] || 0
    };
  }) || [];

const pageTitle = `${city.name} - Local Businesses for Expats`;
const pageDescription = `Discover trusted local businesses in ${city.name}. Find restaurants, services, and more recommended by fellow expats.`;
const canonicalUrl = `https://expatslist.org/${citySlug}`;
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <StructuredData type="city" data={{ city_name: city.name, country: city.country }} />
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Mobile-Optimized City Header -->
    <div class="bg-white border-b border-slate-200 py-3 sm:py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Mobile: Single line with breadcrumb that includes city name -->
        <div class="block sm:hidden">
          <Breadcrumb
            items={[
              { label: 'Main', href: '/', icon: '🌎' },
              { label: city.name, isActive: true, icon: '📍' }
            ]}
          />
        </div>

        <!-- Desktop: Traditional layout with separate title -->
        <div class="hidden sm:flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <Breadcrumb
              items={[
                { label: 'Main', href: '/' },
                { label: city.name, isActive: true, icon: '📍' }
              ]}
            />
            <div class="flex items-center space-x-3">
              <span class="fi fi-mx text-sm"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Expat Classifieds Section -->
    <div class="bg-slate-50 py-4 sm:py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <ClassifiedsSection citySlug={citySlug} />
      </div>
    </div>

    <!-- Modern Categories Grid -->
    <div class="bg-white py-6 sm:py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Business Directory Header -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-slate-900 mb-2">🏢 Business Directory</h2>
          <p class="text-slate-600">Local businesses and services in {city.name}</p>
        </div>
        <!-- Category Tiles Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="categories-grid">
          {premiumCategories.map((category, index) => {
            const businessCount = categoryCountMap[category.id] || 0;

            // Category background images with light overlays and no faces
            const categoryStyles = {
              'housing-relocation': {
                bg: 'linear-gradient(135deg, rgba(59, 130, 246, 0.7), rgba(37, 99, 235, 0.8)), url("https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop")',
                icon: '🏠'
              },
              'health-wellness': {
                bg: 'linear-gradient(135deg, rgba(16, 185, 129, 0.7), rgba(5, 150, 105, 0.8)), url("https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop")',
                icon: '🧑‍⚕️'
              },
              'food-dining': {
                bg: 'linear-gradient(135deg, rgba(245, 158, 11, 0.7), rgba(217, 119, 6, 0.8)), url("https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop")',
                icon: '🍽️'
              },
              'professional-legal-financial': {
                bg: 'linear-gradient(135deg, rgba(99, 102, 241, 0.7), rgba(79, 70, 229, 0.8)), url("https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop")',
                icon: '💼'
              },
              'everyday-services-repairs': {
                bg: 'linear-gradient(135deg, rgba(239, 68, 68, 0.7), rgba(220, 38, 38, 0.8)), url("https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=300&fit=crop")',
                icon: '🛠️'
              },
              'shopping-leisure-community': {
                bg: 'linear-gradient(135deg, rgba(168, 85, 247, 0.7), rgba(147, 51, 234, 0.8)), url("https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop")',
                icon: '🛍️'
              },
              'education-childcare': {
                bg: 'linear-gradient(135deg, rgba(34, 197, 94, 0.7), rgba(21, 128, 61, 0.8)), url("https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop")',
                icon: '🎓'
              }
            };

            const style = categoryStyles[category.id] || categoryStyles['housing-relocation'];

            return (
              <div class="category-wrapper" data-category-id={category.id}>
                <!-- Modern Category Tile with Background -->
                <button
                  class="category-toggle w-full h-48 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group relative"
                  data-category={category.id}
                  data-index={index}
                  style={`background: ${style.bg}; background-size: cover; background-position: center;`}
                >
                  <div class="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent group-hover:from-black/20 transition-all duration-300"></div>

                  <div class="relative h-full flex flex-col justify-end p-6 text-white">
                    <!-- Bottom Section -->
                    <div class="text-left">
                      <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mb-4">
                        <span class="text-2xl">{style.icon}</span>
                      </div>
                      <h3 class="text-lg font-bold leading-tight mb-2" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1), -1px -1px 0px rgba(0,0,0,0.7), 1px -1px 0px rgba(0,0,0,0.7), -1px 1px 0px rgba(0,0,0,0.7), 1px 1px 0px rgba(0,0,0,0.7);">
                        <span class="bg-black/40 px-2 py-1 rounded-md backdrop-blur-sm">{category.name}</span>
                      </h3>
                      <div class="text-sm" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                        <span class="bg-black/30 px-2 py-1 rounded-md backdrop-blur-sm">{businessCount > 0 ? `${businessCount} businesses` : 'Coming soon'}</span>
                      </div>
                    </div>
                  </div>
                </button>
              </div>
            );
          })}
        </div>

      <!-- Smooth Subcategories Panel - Fixed Position -->
      <div id="subcategories-panel" class="fixed inset-0 z-50 opacity-0 pointer-events-none transition-all duration-500 ease-out">
        <!-- Backdrop -->
        <div class="absolute inset-0 bg-black/20 backdrop-blur-sm transition-opacity duration-500"></div>

        <!-- Panel Container -->
        <div class="relative h-full flex items-end sm:items-center justify-center p-4">
          <div class="bg-white rounded-t-3xl sm:rounded-3xl shadow-2xl border border-slate-200/50 overflow-hidden w-full max-w-6xl max-h-[85vh] transform translate-y-full sm:translate-y-8 sm:scale-95 transition-all duration-500 ease-out">

            <!-- Header -->
            <div class="p-6 sm:p-8 border-b border-slate-200 flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 sticky top-0 z-10">
              <div class="flex items-center space-x-4 sm:space-x-6">
                <!-- Premium Icon Background -->
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span id="active-category-icon" class="text-xl sm:text-2xl text-white"></span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 id="active-category-name" class="text-xl sm:text-2xl font-bold text-slate-900 mb-1"></h3>
                      <p class="text-sm text-slate-600 font-medium">Choose a specific service</p>
                    </div>
                    <div class="text-right ml-6">
                      <div id="active-category-count" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-slate-600">businesses</div>
                    </div>
                  </div>
                </div>
              </div>
              <button id="close-panel" class="w-10 h-10 bg-white hover:bg-slate-50 text-slate-700 hover:text-slate-900 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" title="Close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- Content -->
            <div class="p-6 sm:p-8 overflow-y-auto max-h-[calc(85vh-120px)]">
              <div id="subcategories-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                <!-- Subcategories will be populated here -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    </div>
  </main>
</Layout>

<script define:vars={{ premiumCategories }}>
  // Dynamic category data from database - passed from server
  const categoryData = {};

  // Subcategory icon mapping function (client-side)
  function getIconForSubcategory(slug) {
    const subcategoryIconMap = {
      // Housing & Relocation
      'rental-agencies': '🏢',
      'real-estate-agents': '🏘️',
      'property-management': '🏠',
      'moving-shipping-companies': '📦',
      'car-rental': '🚗',
      'furniture-stores': '🛋️',

      // Health & Wellness
      'medical-services': '🏥',
      'emergency-medical-services': '🚑',
      'pharmacies': '💊',
      'mental-health-therapy': '🧠',
      'alternative-complementary-medicine': '🌿',
      'fitness-gyms-personal-trainers': '💪',
      'massage-spas-beauty-salons': '💆',
      'yoga-meditation-studios': '🧘',

      // Food & Dining
      'restaurants': '🍽️',
      'cafes-coffee-shops': '☕',
      'grocery-stores-specialty-foods': '🛒',
      'local-food-markets-street-food': '🥬',

      // Professional, Legal & Financial
      'legal-immigration-services': '⚖️',
      'financial-services': '💰',
      'insurance-brokers': '🛡️',
      'business-support': '💼',
      'coworking-spaces': '💻',
      'currency-exchange': '💱',

      // Everyday Services & Repairs
      'connectivity': '📶',
      'home-maintenance-repair': '🔧',
      'appliance-repair': '🔌',
      'clothing-shoe-repair-alterations': '👕',
      'cleaning-services': '🧽',
      'gardeners-pool-maintenance': '🌱',
      'pet-services': '🐕',
      'vehicle-services': '🚗',
      'locksmiths': '🔑',

      // Shopping, Leisure & Community
      'retail-shopping': '🛍️',
      'tour-agencies': '🗺️',
      'bars-nightclubs': '🍻',
      'home-improvement': '🔨',
      'extra-curricular-activities': '🎨',
      'community-social': '👥',

      // Private Education & Childcare
      'schools': '🏫',
      'preschools-daycares': '👶',
      'language-schools': '📚',
      'after-school-programs-tutoring-workshops': '🎓'
    };
    return subcategoryIconMap[slug] || '📋';
  }

  // Background image mapping for subcategories (client-side)
  function getBackgroundImageForSubcategory(slug) {
    const imageMap = {
      // Housing & Relocation
      'rental-agencies': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop&crop=center',
      'real-estate-agents': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop&crop=center',
      'property-management': 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop&crop=center',
      'moving-shipping-companies': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center',
      'car-rental': 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop&crop=center',
      'furniture-stores': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center',

      // Health & Wellness
      'medical-services': 'https://images.unsplash.com/photo-**********-a9333d879b1f?w=400&h=300&fit=crop&crop=center',
      'emergency-medical-services': 'https://images.unsplash.com/photo-1587351021759-3e566b6af7cc?w=400&h=300&fit=crop&crop=center', // Ambulance/emergency
      'pharmacies': 'https://images.unsplash.com/photo-1631549916768-4119b2e5f926?w=400&h=300&fit=crop&crop=center', // Modern pharmacy shelves
      'mental-health-therapy': 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop&crop=center', // Peaceful therapy room
      'alternative-complementary-medicine': 'https://images.unsplash.com/photo-**********-4ab6ce6db874?w=400&h=300&fit=crop&crop=center',
      'fitness-gyms-personal-trainers': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center',
      'massage-spas-beauty-salons': 'https://images.unsplash.com/photo-**********-73207b1ef5b8?w=400&h=300&fit=crop&crop=center',
      'yoga-meditation-studios': 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&crop=center',

      // Food & Dining
      'restaurants': 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop&crop=center',
      'cafes-coffee-shops': 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=400&h=300&fit=crop&crop=center',
      'grocery-stores-specialty-foods': 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop&crop=center', // Clean grocery store aisles
      'local-food-markets-street-food': 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop&crop=center',

      // Professional, Legal & Financial
      'legal-immigration-services': 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=300&fit=crop&crop=center',
      'financial-services': 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop&crop=center',
      'insurance-brokers': 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=400&h=300&fit=crop&crop=center',
      'business-support': 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop&crop=center',
      'coworking-spaces': 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=400&h=300&fit=crop&crop=center',
      'currency-exchange': 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=300&fit=crop&crop=center',

      // Everyday Services & Repairs
      'connectivity': 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop&crop=center',
      'home-maintenance-repair': 'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=300&fit=crop&crop=center',
      'appliance-repair': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center',
      'clothing-shoe-repair-alterations': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop&crop=center',
      'cleaning-services': 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=300&fit=crop&crop=center',
      'gardeners-pool-maintenance': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop&crop=center',
      'pet-services': 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center',
      'vehicle-services': 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=400&h=300&fit=crop&crop=center',
      'locksmiths': 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop&crop=center',

      // Shopping, Leisure & Community
      'retail-shopping': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop&crop=center',
      'tour-agencies': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=300&fit=crop&crop=center',
      'bars-nightclubs': 'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400&h=300&fit=crop&crop=center',
      'home-improvement': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop&crop=center',
      'extra-curricular-activities': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center', // Sports/activities equipment
      'community-social': 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=400&h=300&fit=crop&crop=center',

      // Private Education & Childcare
      'schools': 'https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=400&h=300&fit=crop&crop=center',
      'preschools-daycares': 'https://images.unsplash.com/photo-1587654780291-39c9404d746b?w=400&h=300&fit=crop&crop=center',
      'language-schools': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=300&fit=crop&crop=center',
      'after-school-programs-tutoring-workshops': 'https://images.unsplash.com/photo-1509062522246-3755977927d7?w=400&h=300&fit=crop&crop=center'
    };
    return imageMap[slug] || 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop&crop=center';
  }

  // Build categoryData from server-side data
  premiumCategories.forEach(cat => {
    categoryData[cat.id] = {
      name: cat.name,
      icon: cat.icon,
      subcategories: cat.subcategories,
      totalBusinessCount: cat.totalBusinessCount
    };
  });

  document.addEventListener('DOMContentLoaded', () => {
    const categoryToggles = document.querySelectorAll('.category-toggle');
    const subcategoriesPanel = document.getElementById('subcategories-panel');
    const activeCategoryIcon = document.getElementById('active-category-icon');
    const activeCategoryName = document.getElementById('active-category-name');
    const activeCategoryCount = document.getElementById('active-category-count');
    const subcategoriesGrid = document.getElementById('subcategories-grid');
    const closePanel = document.getElementById('close-panel');
    const categoriesGrid = document.getElementById('categories-grid');
    const citySlug = window.location.pathname.split('/')[1];

    // Track currently active category
    let activeCategory = null;

    // Handle category clicks with smooth modal
    categoryToggles.forEach((toggle, index) => {
      toggle.addEventListener('click', () => {
        const categoryId = toggle.getAttribute('data-category');
        const category = categoryData[categoryId];

        if (category) {
          // Set this category as active
          activeCategory = categoryId;

          // Update panel content
          activeCategoryIcon.textContent = category.icon;
          activeCategoryName.textContent = category.name;
          activeCategoryCount.textContent = category.totalBusinessCount.toLocaleString();

          // Clear and populate subcategories
          subcategoriesGrid.innerHTML = '';
          category.subcategories.forEach(subcategory => {
            const subcategoryLink = document.createElement('a');
            subcategoryLink.href = `/${citySlug}/${categoryId}/${subcategory.slug}`;
            subcategoryLink.className = 'block relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 group h-48';

            // Get specific icon and background image for this subcategory
            const subcategoryIcon = getIconForSubcategory(subcategory.slug);
            const backgroundImage = getBackgroundImageForSubcategory(subcategory.slug);
            const businessCount = subcategory.businessCount || 0;

            subcategoryLink.style.backgroundImage = `url('${backgroundImage}')`;
            subcategoryLink.style.backgroundSize = 'cover';
            subcategoryLink.style.backgroundPosition = 'center';

            subcategoryLink.innerHTML = `
              <!-- Lighter overlay layers for better background visibility -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent group-hover:from-black/60 group-hover:via-black/25 group-hover:to-black/5 transition-all duration-300"></div>
              <div class="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/15 group-hover:to-black/20 transition-all duration-300"></div>

              <!-- Content positioned at bottom -->
              <div class="absolute inset-0 p-6 flex flex-col justify-end">
                <div class="text-left">
                  <!-- Icon with enhanced background -->
                  <div class="w-12 h-12 bg-white/30 backdrop-blur-sm rounded-xl flex items-center justify-center mb-4 shadow-lg border border-white/20">
                    <span class="text-2xl">${subcategoryIcon}</span>
                  </div>

                  <!-- Title with enhanced readability -->
                  <h3 class="text-lg font-bold leading-tight mb-2 text-white" style="text-shadow: 2px 2px 6px rgba(0,0,0,0.8), 1px 1px 3px rgba(0,0,0,0.9), 0px 0px 8px rgba(0,0,0,0.6);">
                    <span class="bg-black/50 px-3 py-1.5 rounded-lg backdrop-blur-sm border border-white/10">${subcategory.name}</span>
                  </h3>

                  <!-- Business count with enhanced visibility -->
                  <div class="text-sm text-white" style="text-shadow: 2px 2px 6px rgba(0,0,0,0.8), 1px 1px 3px rgba(0,0,0,0.9);">
                    <span class="bg-black/40 px-2.5 py-1 rounded-lg backdrop-blur-sm border border-white/10">${businessCount > 0 ? `${businessCount} businesses` : 'Coming soon'}</span>
                  </div>
                </div>
              </div>
            `;
            subcategoriesGrid.appendChild(subcategoryLink);
          });

          // Show modal with smooth animation
          showModal();
        }
      });
    });

    // Smooth modal show function
    function showModal() {
      const panel = subcategoriesPanel;
      const content = panel.querySelector('.bg-white');

      // Enable pointer events and start animation
      panel.classList.remove('pointer-events-none');

      // Force reflow
      panel.offsetHeight;

      // Animate in
      panel.classList.remove('opacity-0');
      content.classList.remove('translate-y-full', 'sm:translate-y-8', 'sm:scale-95');
      content.classList.add('translate-y-0', 'sm:translate-y-0', 'sm:scale-100');

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }

    // Smooth modal hide function
    function hideModal() {
      const panel = subcategoriesPanel;
      const content = panel.querySelector('.bg-white');

      // Animate out
      panel.classList.add('opacity-0');
      content.classList.add('translate-y-full', 'sm:translate-y-8', 'sm:scale-95');
      content.classList.remove('translate-y-0', 'sm:translate-y-0', 'sm:scale-100');

      // Disable pointer events after animation
      setTimeout(() => {
        panel.classList.add('pointer-events-none');
        document.body.style.overflow = '';
        activeCategory = null;
      }, 500);
    }

    // Handle close panel
    closePanel.addEventListener('click', () => {
      hideModal();
    });

    // Handle keyboard navigation
    categoryToggles.forEach(toggle => {
      toggle.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggle.click();
        }
        if (e.key === 'Escape') {
          hideModal();
        }
      });
    });

    // Close panel when clicking backdrop
    subcategoriesPanel.addEventListener('click', (e) => {
      if (e.target === subcategoriesPanel || e.target.classList.contains('bg-black/20')) {
        hideModal();
      }
    });

    // Handle escape key globally
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && activeCategory) {
        hideModal();
      }
    });
  });
</script>

<style>
  /* Premium mobile-first responsive design */
  .category-toggle {
    aspect-ratio: 1.2 / 1;
    min-height: 180px;
  }

  @media (max-width: 640px) {
    .category-toggle {
      min-height: 160px;
      aspect-ratio: 1.1 / 1;
    }

    .category-toggle h3 {
      font-size: 0.875rem;
      line-height: 1.2;
    }

    .category-toggle .w-20 {
      width: 4rem;
      height: 4rem;
    }

    .category-toggle .text-3xl {
      font-size: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .category-toggle {
      min-height: 200px;
      aspect-ratio: 1.25 / 1;
    }
  }

  @media (min-width: 1024px) {
    .category-toggle {
      min-height: 220px;
      aspect-ratio: 1.3 / 1;
    }

    .category-toggle h3 {
      font-size: 1.125rem;
    }

    .category-toggle .w-20 {
      width: 5.5rem;
      height: 5.5rem;
    }

    .category-toggle .text-3xl {
      font-size: 2rem;
    }
  }

  /* Smooth modal animations */
  #subcategories-panel {
    backdrop-filter: blur(8px);
  }

  #subcategories-panel .bg-white {
    will-change: transform, opacity;
  }

  /* Premium focus states for accessibility */
  .category-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* Subtle hover effects for category tiles */
  .category-toggle:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.06);
  }

  /* Smooth transitions for all interactive elements */
  .category-toggle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .category-toggle * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Mobile-specific modal adjustments */
  @media (max-width: 640px) {
    #subcategories-panel .bg-white {
      border-radius: 1.5rem 1.5rem 0 0;
    }
  }

  /* Smooth backdrop blur */
  #subcategories-panel .bg-black\/20 {
    backdrop-filter: blur(4px);
  }

  /* Prevent scroll bounce on iOS */
  #subcategories-panel {
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced subcategory cards */
  #subcategories-grid a {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  #subcategories-grid a:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Smooth scrolling for modal content */
  #subcategories-panel .overflow-y-auto {
    scroll-behavior: smooth;
  }
</style>
