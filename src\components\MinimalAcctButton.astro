---
// World-class minimal account navigation - Best UX/UI practices
export interface Props {
  currentPath?: string;
}

const { currentPath = '' } = Astro.props;
---

<!-- World-Class Mobile-First Account Navigation -->
<div id="minimal-account-nav" class="fixed top-4 right-4 z-50">
  <!-- Signed Out State -->
  <button
    id="signin-btn"
    class="hidden bg-white/95 backdrop-blur-sm border border-slate-200 hover:border-slate-300 active:border-slate-400 text-slate-600 hover:text-slate-900 active:text-slate-900 px-3 py-2.5 sm:py-2 text-sm font-medium transition-all duration-200 rounded-lg shadow-sm hover:shadow-md active:shadow-lg min-h-[44px] touch-manipulation"
    title="Sign in"
  >
    sign in
  </button>

  <!-- Signed In State - Mobile-Optimized Mini Menu -->
  <div id="signed-in-menu" class="hidden relative">
    <button
      id="account-menu-btn"
      class="bg-white/95 backdrop-blur-sm border border-slate-200 hover:border-slate-300 active:border-slate-400 text-slate-600 hover:text-slate-900 active:text-slate-900 px-3 py-2.5 sm:py-2 text-sm font-medium transition-all duration-200 rounded-lg shadow-sm hover:shadow-md active:shadow-lg flex items-center space-x-2 min-h-[44px] touch-manipulation"
      title="Account menu"
    >
      <span id="user-initial" class="w-6 h-6 sm:w-5 sm:h-5 bg-blue-500 text-white text-xs font-bold rounded-full flex items-center justify-center">U</span>
      <svg class="w-4 h-4 sm:w-3 sm:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- Mobile-Optimized Dropdown Menu -->
    <div
      id="account-dropdown"
      class="hidden absolute right-0 mt-2 w-52 sm:w-48 bg-white/95 backdrop-blur-sm border border-slate-200 rounded-xl sm:rounded-lg shadow-xl sm:shadow-lg py-2 sm:py-1 z-50"
    >
      <!-- Navigation Links - Mobile Optimized -->
      <a
        href="/"
        class="flex items-center px-4 py-3 sm:px-3 sm:py-2 text-base sm:text-sm text-slate-700 hover:bg-slate-50 active:bg-slate-100 transition-colors min-h-[44px] sm:min-h-auto touch-manipulation"
      >
        <span class="mr-3 sm:mr-2 text-lg sm:text-base">🏠</span>
        Home
      </a>
      <a
        href="/account"
        class="flex items-center px-4 py-3 sm:px-3 sm:py-2 text-base sm:text-sm text-slate-700 hover:bg-slate-50 active:bg-slate-100 transition-colors min-h-[44px] sm:min-h-auto touch-manipulation"
      >
        <span class="mr-3 sm:mr-2 text-lg sm:text-base">⚙️</span>
        Account
      </a>
      <div class="border-t border-slate-100 my-2 sm:my-1"></div>
      <button
        id="signout-btn"
        class="w-full flex items-center px-4 py-3 sm:px-3 sm:py-2 text-base sm:text-sm text-slate-600 hover:bg-red-50 hover:text-red-600 active:bg-red-100 active:text-red-700 transition-colors text-left min-h-[44px] sm:min-h-auto touch-manipulation"
      >
        <span class="mr-3 sm:mr-2 text-lg sm:text-base">👋</span>
        Sign out
      </button>
    </div>
  </div>
</div>

<!-- Minimal styles for perfect positioning -->
<style>
  #minimal-account-nav {
    position: fixed !important;
    top: 16px !important;
    right: 16px !important;
    z-index: 50 !important;
  }

  /* Mobile-specific positioning to avoid overlap */
  @media (max-width: 640px) {
    #minimal-account-nav {
      top: 20px !important;
      right: 20px !important;
    }
  }

  /* Ensure dropdown appears above everything */
  #account-dropdown {
    z-index: 60 !important;
  }
</style>

<!-- World-Class Account Navigation JavaScript -->
<script>
  // Wait for auth functions to be available
  function initAccountNav() {
    if (!window.authFunctions) {
      setTimeout(initAccountNav, 100);
      return;
    }

    const signinBtn = document.getElementById('signin-btn');
    const signedInMenu = document.getElementById('signed-in-menu');
    const accountMenuBtn = document.getElementById('account-menu-btn');
    const accountDropdown = document.getElementById('account-dropdown');
    const signoutBtn = document.getElementById('signout-btn');
    const userInitial = document.getElementById('user-initial');

    // Update navigation based on auth state
    function updateAccountNav() {
      const currentUser = window.authFunctions.getCurrentUser();

      if (currentUser) {
        // User is signed in - show menu with user initial
        signinBtn?.classList.add('hidden');
        signedInMenu?.classList.remove('hidden');

        // Update user initial
        const displayName = currentUser.user_metadata?.display_name || currentUser.email?.split('@')[0] || 'User';
        const initial = displayName.charAt(0).toUpperCase();
        if (userInitial) {
          userInitial.textContent = initial;
        }
      } else {
        // User is not signed in - show signin button
        signinBtn?.classList.remove('hidden');
        signedInMenu?.classList.add('hidden');
        accountDropdown?.classList.add('hidden');
      }
    }

    // Sign in handler
    signinBtn?.addEventListener('click', () => {
      if (window.authFunctions) {
        window.authFunctions.showAuthModal();
      }
    });

    // Account menu toggle
    accountMenuBtn?.addEventListener('click', (e) => {
      e.stopPropagation();
      accountDropdown?.classList.toggle('hidden');
    });

    // Sign out handler - stay on current page
    signoutBtn?.addEventListener('click', async () => {
      try {
        await window.authFunctions.signOut();
        accountDropdown?.classList.add('hidden');
        updateAccountNav();

        // Stay on current page after logout - much better UX!
        // No redirect needed - user stays where they were
      } catch (error) {
        console.error('Sign out error:', error);
      }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      const target = e.target as Node;
      if (!accountMenuBtn?.contains(target) && !accountDropdown?.contains(target)) {
        accountDropdown?.classList.add('hidden');
      }
    });

    // Listen for auth state changes
    document.addEventListener('authStateChanged', updateAccountNav);

    // Initial update
    updateAccountNav();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAccountNav);
  } else {
    initAccountNav();
  }
</script>

 
 