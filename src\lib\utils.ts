import { clsx, type ClassValue } from 'clsx';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Convert city name to URL slug
 * Example: "Playa del Carmen" → "playa-del-carmen"
 */
export function cityToSlug(cityName: string): string {
  return cityName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * Convert business name to URL slug that meets database constraint
 * Example: "Casa Banana Restaurant" → "casa-banana-restaurant"
 */
export function businessToSlug(businessName: string): string {
  return generateSlug(businessName); // Use the constraint-compliant function
}

/**
 * Generate slug from text that meets database constraint requirements
 */
export function generateSlug(text: string): string {
  let slug = text
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Only allow lowercase letters, numbers, spaces, hyphens
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

  // Ensure minimum length of 2 characters
  if (slug.length < 2) {
    slug = `business-${slug}` || 'business-listing';
  }

  // Final validation - ensure it matches the constraint pattern
  if (!slug.match(/^[a-z0-9]+(-[a-z0-9]+)*$/)) {
    // Create a safe fallback
    const safeChars = text.replace(/[^a-z0-9]/gi, '').toLowerCase();
    slug = safeChars.length >= 2 ? safeChars : 'business-listing';
  }

  return slug;
}

/**
 * Format price range display
 */
export function formatPriceRange(priceRange: string | null): string {
  if (!priceRange) return '';

  const ranges: Record<string, string> = {
    '$': 'Budget-friendly',
    '$$': 'Moderate',
    '$$$': 'Upscale',
    '$$$$': 'Luxury'
  };

  return ranges[priceRange] || priceRange;
}

/**
 * Format opening hours for display
 */
export function formatOpeningHours(openingHours: Record<string, any> | null): string {
  if (!openingHours) return 'Hours not available';

  // This is a simplified version - you might want to implement more complex logic
  if (typeof openingHours === 'string') {
    return openingHours;
  }

  // Handle structured opening hours object
  const today = new Date().toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase(); // 'mon', 'tue', etc.
  const todayHours = openingHours[today];

  if (todayHours) {
    return `Today: ${todayHours}`;
  }

  return 'See full hours';
}

/**
 * Check if business is currently open
 */
export function isBusinessOpen(openingHours: Record<string, any> | null): boolean | null {
  if (!openingHours) return null;

  // Simplified implementation - you'd want more robust time parsing
  const now = new Date();
  const today = now.toLocaleDateString('en', { weekday: 'short' }).toLowerCase();
  // const currentTime = now.getHours() * 100 + now.getMinutes(); // HHMM format

  const todayHours = openingHours[today];
  if (!todayHours || todayHours === 'Closed') {
    return false;
  }

  // This would need more sophisticated parsing for real implementation
  return null; // Return null when we can't determine
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Get display name for business
 */
export function getBusinessDisplayName(listing: any): string {
  // Use display_name only if it's meaningful (not just first word of business_name)
  return (listing.display_name && listing.display_name.length > 3 && !listing.business_name.startsWith(listing.display_name + ' '))
    ? listing.display_name
    : listing.business_name;
}

/**
 * Get translated content
 */
export function getTranslatedContent(
  content: string | null,
  translations: Record<string, any> | null,
  language: string = 'en'
): string {
  if (translations && translations[language]) {
    return translations[language];
  }
  return content || '';
}

/**
 * Extract location identifier from address for slug generation
 */
function extractLocationFromAddress(address: string): string | null {
  if (!address) return null;

  const locationKeywords = [
    'downtown', 'centro', 'center', 'centre',
    'mall', 'plaza', 'shopping', 'market',
    'airport', 'aeropuerto', 'terminal',
    'station', 'estacion', 'central',
    'beach', 'playa', 'costa', 'marina',
    'old town', 'zona rosa', 'historic',
    'north', 'south', 'east', 'west',
    'norte', 'sur', 'este', 'oeste'
  ];

  const addressLower = address.toLowerCase();

  // Look for location keywords
  for (const keyword of locationKeywords) {
    if (addressLower.includes(keyword)) {
      return keyword.replace(/\s+/g, '-');
    }
  }

  // Extract street name as fallback (first part before comma or number)
  const streetMatch = address.match(/^([a-zA-Z\s]+)/);
  if (streetMatch) {
    const streetName = streetMatch[1].trim().toLowerCase();
    // Only use if it's not too long and contains meaningful words
    if (streetName.length <= 15 && streetName.split(' ').length <= 2) {
      return streetName.replace(/\s+/g, '-');
    }
  }

  return null;
}

/**
 * Generate unique slug for business listings with location awareness
 * Handles chain stores by incorporating location information
 */
export async function generateUniqueSlug(
  businessName: string,
  cityId: string,
  categoryId: string,
  address?: string
): Promise<string> {
  const baseSlug = generateSlug(businessName);
  const locationId = address ? extractLocationFromAddress(address) : null;

  // Import here to avoid circular dependency
  const { query } = await import('./database');

  // Try different slug variations in order of preference
  const slugCandidates = [
    baseSlug, // First try clean slug
    ...(locationId ? [`${baseSlug}-${locationId}`] : []), // Then try with location
  ];

  // Check each candidate
  for (const candidateSlug of slugCandidates) {
    const result = await query(
      'SELECT id FROM listings WHERE city_id = $1 AND category_primary_id = $2 AND slug = $3 AND deleted_at IS NULL',
      [cityId, categoryId, candidateSlug]
    );

    if (result.rows.length === 0) {
      return candidateSlug;
    }
  }

  // If all candidates are taken, fall back to numbered suffix
  const finalBaseSlug = locationId ? `${baseSlug}-${locationId}` : baseSlug;
  let counter = 2;

  while (true) {
    const numberedSlug = `${finalBaseSlug}-${counter}`;
    const result = await query(
      'SELECT id FROM listings WHERE city_id = $1 AND category_primary_id = $2 AND slug = $3 AND deleted_at IS NULL',
      [cityId, categoryId, numberedSlug]
    );

    if (result.rows.length === 0) {
      return numberedSlug;
    }

    counter++;
  }
}

/**
 * Normalize business name for duplicate detection
 */
function normalizeBusinessName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    // Remove common business suffixes
    .replace(/\b(llc|inc|corp|ltd|restaurant|cafe|bar|shop|store|company|co\.?)\b/g, '')
    // Remove punctuation and extra spaces
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Calculate simple string similarity (0-1 scale)
 */
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Check for potential duplicates of a business listing
 */
export async function checkForDuplicates(businessData: {
  business_name: string;
  city_id: string;
  category_primary_id: string;
  address_full?: string;
}): Promise<{
  isDuplicate: boolean;
  type?: 'exact' | 'similar_name' | 'similar_address';
  matches?: any[];
  confidence?: number;
}> {
  // Import here to avoid circular dependency
  const { query } = await import('./database');

  // Layer 1: Exact match check
  const exactMatchQuery = `
    SELECT id, business_name, address_full
    FROM listings
    WHERE city_id = $1
    AND category_primary_id = $2
    AND LOWER(business_name) = LOWER($3)
    AND LOWER(COALESCE(address_full, '')) = LOWER(COALESCE($4, ''))
    AND deleted_at IS NULL
  `;

  const exactResult = await query(exactMatchQuery, [
    businessData.city_id,
    businessData.category_primary_id,
    businessData.business_name,
    businessData.address_full || ''
  ]);

  if (exactResult.rows.length > 0) {
    return {
      isDuplicate: true,
      type: 'exact',
      matches: exactResult.rows,
      confidence: 1.0
    };
  }

  // Layer 2: Fuzzy business name matching
  const normalizedInputName = normalizeBusinessName(businessData.business_name);

  const fuzzyMatchQuery = `
    SELECT id, business_name, address_full
    FROM listings
    WHERE city_id = $1
    AND category_primary_id = $2
    AND deleted_at IS NULL
  `;

  const fuzzyResult = await query(fuzzyMatchQuery, [
    businessData.city_id,
    businessData.category_primary_id
  ]);

  const similarNames = fuzzyResult.rows.filter(row => {
    const normalizedExistingName = normalizeBusinessName(row.business_name);
    const similarity = calculateSimilarity(normalizedInputName, normalizedExistingName);
    return similarity >= 0.8; // 80% similarity threshold
  });

  if (similarNames.length > 0) {
    const maxSimilarity = Math.max(...similarNames.map(row =>
      calculateSimilarity(normalizedInputName, normalizeBusinessName(row.business_name))
    ));

    return {
      isDuplicate: true,
      type: 'similar_name',
      matches: similarNames,
      confidence: maxSimilarity
    };
  }

  // Layer 3: Address similarity (same business name)
  if (businessData.address_full) {
    const addressMatchQuery = `
      SELECT id, business_name, address_full
      FROM listings
      WHERE city_id = $1
      AND category_primary_id = $2
      AND LOWER(business_name) = LOWER($3)
      AND address_full IS NOT NULL
      AND deleted_at IS NULL
    `;

    const addressResult = await query(addressMatchQuery, [
      businessData.city_id,
      businessData.category_primary_id,
      businessData.business_name
    ]);

    const similarAddresses = addressResult.rows.filter(row => {
      const similarity = calculateSimilarity(
        businessData.address_full!.toLowerCase(),
        row.address_full.toLowerCase()
      );
      return similarity >= 0.7; // 70% similarity threshold for addresses
    });

    if (similarAddresses.length > 0) {
      const maxSimilarity = Math.max(...similarAddresses.map(row =>
        calculateSimilarity(businessData.address_full!.toLowerCase(), row.address_full.toLowerCase())
      ));

      return {
        isDuplicate: true,
        type: 'similar_address',
        matches: similarAddresses,
        confidence: maxSimilarity
      };
    }
  }

  return { isDuplicate: false };
}


