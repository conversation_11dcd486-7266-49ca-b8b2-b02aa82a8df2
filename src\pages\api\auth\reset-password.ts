import type { APIRoute } from 'astro';
import bcrypt from 'bcryptjs';
import { updateUserPasswordHash } from '../../../lib/database.js';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL;
const supabaseServiceKey = import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export const POST: APIRoute = async ({ request }) => {
  try {
    const { newPassword } = await request.json();

    if (!newPassword) {
      return new Response(JSON.stringify({ 
        error: 'New password is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (newPassword.length < 8) {
      return new Response(JSON.stringify({ 
        error: 'Password must be at least 8 characters long' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get the authorization header or cookie
    let token = null;
    const authHeader = request.headers.get('authorization');

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    } else {
      // Try to get token from cookie
      const cookies = request.headers.get('cookie');
      if (cookies) {
        const tokenMatch = cookies.match(/sb-[^=]+-auth-token=([^;]+)/);
        if (tokenMatch) {
          try {
            const tokenData = JSON.parse(decodeURIComponent(tokenMatch[1]));
            token = tokenData.access_token;
          } catch (e) {
            console.error('Error parsing token from cookie:', e);
          }
        }
      }
    }

    if (!token) {
      return new Response(JSON.stringify({
        error: 'No valid session found. Please use the reset link from your email.'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the user's session using the admin client
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user) {
      console.error('Error verifying user session:', userError);
      return new Response(JSON.stringify({ 
        error: 'Invalid or expired reset session. Please request a new password reset.' 
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Hash the new password using bcrypt with cost factor 10 (same as Supabase)
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update password in auth.users table using admin client
    const { error: updateAuthError } = await supabaseAdmin.auth.admin.updateUserById(
      user.id,
      { password: newPassword }
    );

    if (updateAuthError) {
      console.error('Error updating auth password:', updateAuthError);
      return new Response(JSON.stringify({ 
        error: 'Failed to update password in authentication system' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Also update password hash in our database for consistency
    const { error: updateDbError } = await updateUserPasswordHash(user.id, hashedNewPassword);

    if (updateDbError) {
      console.error('Error updating database password:', updateDbError);
      // Don't fail the request if database update fails, auth update is primary
      console.warn('Password updated in auth but failed to sync to database');
    }

    // Sign out the user to force them to sign in with new password
    const { error: signOutError } = await supabaseAdmin.auth.admin.signOut(token);
    if (signOutError) {
      console.warn('Failed to sign out user after password reset:', signOutError);
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Password reset successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Password reset error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
