---
import '../styles/global.css';
import AuthModal from '../components/AuthModal.astro';
import Navigation from '../components/Navigation.astro';

export interface Props {
	title: string;
	description?: string;
	city?: string;
	canonical?: string;
}

const { title, description = "Find trusted local businesses and services for expats worldwide", city, canonical } = Astro.props;

const fullTitle = city ? `${title} - ${city} | ExpatsList` : `${title} | ExpatsList`;
const canonicalUrl = canonical || Astro.url.href;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/vibe8-logo.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{fullTitle}</title>

		<!-- Canonical URL -->
		<link rel="canonical" href={canonicalUrl} />

		<!-- Open Graph -->
		<meta property="og:title" content={fullTitle} />
		<meta property="og:description" content={description} />
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonicalUrl} />
		<meta property="og:site_name" content="ExpatsList" />

		<!-- Twitter Card -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={fullTitle} />
		<meta name="twitter:description" content={description} />

		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

		<!-- Flag Icons -->
		<link rel="stylesheet" href="/custom-flags.css" />

		<!-- SEO Enhancements -->
		<meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
		<meta name="googlebot" content="index, follow" />
		<meta name="bingbot" content="index, follow" />

		<!-- Language and Region -->
		<meta name="language" content="English" />
		<meta name="geo.region" content="MX" />
		<meta name="geo.placename" content={city || "Mexico"} />

		<!-- Performance Hints -->
		<link rel="dns-prefetch" href="//expatslist.org" />
		<meta http-equiv="x-dns-prefetch-control" content="on" />

	</head>
	<body class="min-h-screen bg-gray-50 font-sans antialiased">
		<slot />

		<!-- World-Class User Navigation -->
		<Navigation />

		<!-- Auth Modal -->
		<AuthModal />

		<!-- User Feedback Notifications -->
		<div id="user-notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>

		<!-- Auth JavaScript -->
		<script type="module">
			// Import Supabase with consistent configuration
			import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

			// Initialize Supabase client with EXACT same configuration as auth.ts
			const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co'
			const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM'

			const supabase = createClient(supabaseUrl, supabaseAnonKey, {
				auth: {
					autoRefreshToken: true,
					persistSession: true,
					detectSessionInUrl: true,
					flowType: 'pkce',
					storage: window.localStorage,
					storageKey: 'expatslist-auth'
				}
			})

			let currentUser = null;
			let userProfile = null;

			// DOM elements
			const authModal = document.getElementById('auth-modal');
			const closeAuthModalBtn = document.getElementById('close-auth-modal');
			const notificationsContainer = document.getElementById('user-notifications');

			// Notification functions
			function showNotification(message, type = 'success') {
				if (!notificationsContainer) return;

				const notification = document.createElement('div');
				notification.className = `
					px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out
					${type === 'success' ? 'bg-emerald-500 text-white' : 'bg-red-500 text-white'}
					translate-x-full opacity-0
				`;
				notification.innerHTML = `
					<div class="flex items-center space-x-3">
						<div class="flex-shrink-0">
							${type === 'success' ?
								'<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
								'<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>'
							}
						</div>
						<div class="font-medium">${message}</div>
					</div>
				`;

				notificationsContainer.appendChild(notification);

				// Animate in
				setTimeout(() => {
					notification.classList.remove('translate-x-full', 'opacity-0');
					notification.classList.add('translate-x-0', 'opacity-100');
				}, 100);

				// Auto remove after 4 seconds
				setTimeout(() => {
					notification.classList.add('translate-x-full', 'opacity-0');
					setTimeout(() => {
						if (notification.parentNode) {
							notification.parentNode.removeChild(notification);
						}
					}, 300);
				}, 4000);
			}

			// Form elements
			const signinForm = document.getElementById('signin-form');
			const signupForm = document.getElementById('signup-form');
			const forgotPasswordForm = document.getElementById('forgot-password-form');
			const authSuccess = document.getElementById('auth-success');

			// Form switching buttons
			const showSignupBtn = document.getElementById('show-signup');
			const showSigninBtn = document.getElementById('show-signin');
			const showForgotPasswordBtn = document.getElementById('show-forgot-password');
			const backToSigninBtn = document.getElementById('back-to-signin');
			const closeSuccessBtn = document.getElementById('close-success');

			// Initialize auth state using official Supabase methods
			async function initAuth() {
				try {
					const { data: { user }, error } = await supabase.auth.getUser()
					if (error && error.message !== 'Auth session missing!') {
						console.error('Auth initialization error:', error)
						return
					}

					currentUser = user
					if (currentUser) {
						// Skip profile loading during initialization to avoid 406 errors
						console.log('User found on init, skipping profile loading for now')
						userProfile = null
					}
				} catch (error) {
					console.error('Auth initialization error:', error)
				}
			}

			// Modal functions
			function showModal() {
				if (authModal) {
					authModal.classList.remove('hidden');
					document.body.style.overflow = 'hidden';
				}
			}

			function hideModal() {
				console.log('hideModal called')
				if (authModal) {
					authModal.classList.add('hidden');
					document.body.style.overflow = '';
					console.log('Modal hidden successfully')
				} else {
					console.error('authModal element not found')
				}
			}

			function showForm(formToShow) {
				const forms = [signinForm, signupForm, forgotPasswordForm, authSuccess];
				forms.forEach(form => {
					if (form) {
						form.classList.add('hidden');
					}
				});
				if (formToShow) {
					formToShow.classList.remove('hidden');
				}

				// Hide any error messages when switching forms
				hideSignInError();
			}

			// Error message functions
			function showSignInError(message) {
				const errorDiv = document.getElementById('signin-error');
				const errorText = document.getElementById('signin-error-text');

				if (errorDiv && errorText) {
					errorText.textContent = message;
					errorDiv.classList.remove('hidden');

					// Auto-hide after 5 seconds
					setTimeout(() => {
						hideSignInError();
					}, 5000);
				}
			}

			function hideSignInError() {
				const errorDiv = document.getElementById('signin-error');
				if (errorDiv) {
					errorDiv.classList.add('hidden');
				}
			}

			// For now, reuse the signin error display for signup errors
			function showSignUpError(message) {
				// Switch to signin form to show the error
				showForm(signinForm);
				showSignInError(message);
			}

			// Event listeners
			closeAuthModalBtn?.addEventListener('click', hideModal);
			closeSuccessBtn?.addEventListener('click', hideModal);

			// Close error message when user clicks the X
			document.getElementById('close-signin-error')?.addEventListener('click', hideSignInError);

			// Hide error when user starts typing (better UX)
			document.getElementById('signin-email')?.addEventListener('input', hideSignInError);
			document.getElementById('signin-password')?.addEventListener('input', hideSignInError);

			// Form switching
			showSignupBtn?.addEventListener('click', () => showForm(signupForm));
			showSigninBtn?.addEventListener('click', () => showForm(signinForm));
			showForgotPasswordBtn?.addEventListener('click', () => showForm(forgotPasswordForm));
			backToSigninBtn?.addEventListener('click', () => showForm(signinForm));

			// Close modal on escape key
			document.addEventListener('keydown', (e) => {
				if (e.key === 'Escape') {
					hideModal();
				}
			});

			// Close modal when clicking outside
			authModal?.addEventListener('click', (e) => {
				if (e.target === authModal) {
					hideModal();
				}
			});

			// Listen for auth state changes using official Supabase methods
			let lastProcessedEvent = null
			function setupAuthStateListener() {
				console.log('Setting up auth state listener')
				supabase.auth.onAuthStateChange(async (event, session) => {
					const eventKey = `${event}-${session?.user?.id}`
					if (lastProcessedEvent === eventKey) {
						console.log('Skipping duplicate event:', event)
						return
					}
					lastProcessedEvent = eventKey

					console.log('Auth state change:', event, session?.user?.email)

					if (event === 'SIGNED_IN' && session?.user) {
						console.log('Processing SIGNED_IN event')
						currentUser = session.user

						// Skip profile loading for now and just close modal
						console.log('Skipping profile loading, closing modal immediately')
						userProfile = null

						// Always complete sign-in and reset button state
						hideModal()

						// Reset sign-in button state with more debugging
						const submitBtn = document.getElementById('signin-submit')
						const submitText = document.getElementById('signin-text')
						const submitSpinner = document.getElementById('signin-spinner')

						console.log('Button elements found:', {
							submitBtn: !!submitBtn,
							submitText: !!submitText,
							submitSpinner: !!submitSpinner
						})

						if (submitBtn && submitText && submitSpinner) {
							submitBtn.disabled = false
							submitText.textContent = 'Sign In'
							submitSpinner.classList.add('hidden')
							console.log('Button state reset successfully')

							// Force a visual refresh
							submitBtn.style.display = 'none'
							setTimeout(() => {
								submitBtn.style.display = 'block'
							}, 10)

							// Reset the signing in flag
							isSigningIn = false
						} else {
							console.error('Could not find button elements to reset')

							// Try to find any sign-in buttons and reset them
							const allSigninBtns = document.querySelectorAll('[id*="signin"]')
							console.log('Found signin elements:', allSigninBtns.length)
							allSigninBtns.forEach((btn, index) => {
								console.log(`Element ${index}:`, btn.id, btn.textContent)
							})
						}

						// Dispatch auth state change event for other pages
						const authEvent = new CustomEvent('authStateChanged', {
							detail: {
								user: currentUser,
								profile: userProfile,
								isAuthenticated: !!currentUser
							}
						})
						document.dispatchEvent(authEvent)

						console.log('Sign-in completed for:', currentUser.email)
					} else if (event === 'SIGNED_OUT') {
						console.log('User signed out')
						currentUser = null
						userProfile = null

						// Dispatch auth state change event for other pages
						const authEvent = new CustomEvent('authStateChanged', {
							detail: {
								user: null,
								profile: null,
								isAuthenticated: false
							}
						})
						document.dispatchEvent(authEvent)
					}
				})
			}

			// Form submission handlers
			const signinFormElement = document.getElementById('signin-form-element');
			const signupFormElement = document.getElementById('signup-form-element');
			const forgotPasswordFormElement = document.getElementById('forgot-password-form-element');

			// Sign in form using official Supabase methods
			let isSigningIn = false
			signinFormElement?.addEventListener('submit', async (e) => {
				e.preventDefault()

				// Prevent multiple submissions
				if (isSigningIn) {
					console.log('Sign-in already in progress, ignoring')
					return
				}
				isSigningIn = true

				const email = document.getElementById('signin-email').value
				const password = document.getElementById('signin-password').value
				const submitBtn = document.getElementById('signin-submit')
				const submitText = document.getElementById('signin-text')
				const submitSpinner = document.getElementById('signin-spinner')

				console.log('Starting sign-in for:', email)

				// Show loading state
				submitBtn.disabled = true
				submitText.textContent = 'Signing in...'
				submitSpinner.classList.remove('hidden')

				try {
					const { data, error } = await supabase.auth.signInWithPassword({
						email,
						password
					})

					if (error) {
						console.error('Sign in error:', error)

						// Show elegant error message instead of popup
						showSignInError(error.message || 'Sign in failed. Please check your credentials.')

						// Reset button state immediately on error
						submitBtn.disabled = false
						submitText.textContent = 'Sign In'
						submitSpinner.classList.add('hidden')
						isSigningIn = false
					} else if (data.user) {
						// Sign in successful - the auth state change listener will handle the rest
						console.log('Sign in successful for user:', data.user.email)
						showNotification('Successfully signed in! Welcome back.', 'success');

						// Force close modal after 2 seconds as backup
						setTimeout(() => {
							console.log('Force closing modal as backup')
							hideModal()
							// Reset button state
							submitBtn.disabled = false
							submitText.textContent = 'Sign In'
							submitSpinner.classList.add('hidden')
							isSigningIn = false
						}, 2000)
					}
				} catch (error) {
					console.error('Sign in error:', error)

					// Show elegant error message instead of popup
					showSignInError(error.message || 'Sign in failed. Please check your credentials.')

					// Reset button state immediately on error
					submitBtn.disabled = false
					submitText.textContent = 'Sign In'
					submitSpinner.classList.add('hidden')
					isSigningIn = false
				}
			})

			// Sign up form using official Supabase methods
			signupFormElement?.addEventListener('submit', async (e) => {
				e.preventDefault()

				const name = document.getElementById('signup-name').value
				const email = document.getElementById('signup-email').value
				const password = document.getElementById('signup-password').value
				const role = 'classified_user' // Default role for all new users
				const submitBtn = document.getElementById('signup-submit')
				const submitText = document.getElementById('signup-text')
				const submitSpinner = document.getElementById('signup-spinner')

				// Show loading state
				submitBtn.disabled = true
				submitText.textContent = 'Creating account...'
				submitSpinner.classList.remove('hidden')

				try {
					const { data, error } = await supabase.auth.signUp({
						email,
						password,
						options: {
							data: {
								display_name: name
							},
							emailRedirectTo: `${window.location.origin}/auth/callback`
						}
					})

					if (error) {
						console.error('Sign up error:', error)
						// Show user-friendly error message instead of popup
						showSignUpError(error.message || 'Sign up failed. Please try again.')
					} else if (data.user) {
						// Profile will be created automatically by database trigger
						console.log('User signup successful, profile will be created automatically')
						showNotification('Account created! Please check your email to verify your account.', 'success');
						showForm(authSuccess)
					}
				} catch (error) {
					console.error('Sign up error:', error)
					// Show user-friendly error message instead of popup
					showSignUpError(error.message || 'Sign up failed. Please try again.')
				} finally {
					// Reset button state
					submitBtn.disabled = false
					submitText.textContent = 'Create Account'
					submitSpinner.classList.add('hidden')
				}
			})

			// Forgot password form using official Supabase methods
			forgotPasswordFormElement?.addEventListener('submit', async (e) => {
				e.preventDefault()

				const email = document.getElementById('forgot-email').value
				const submitBtn = document.getElementById('forgot-submit')
				const submitText = document.getElementById('forgot-text')
				const submitSpinner = document.getElementById('forgot-spinner')

				// Show loading state
				submitBtn.disabled = true
				submitText.textContent = 'Sending...'
				submitSpinner.classList.remove('hidden')

				try {
					const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
						redirectTo: `${window.location.origin}/auth/reset-password`
					})

					if (error) {
						console.error('Password reset error:', error)
						alert(error.message || 'Password reset failed. Please try again.')
					} else {
						showForm(authSuccess)
					}
				} catch (error) {
					console.error('Password reset error:', error)
					alert(error.message || 'Password reset failed. Please try again.')
				} finally {
					// Reset button state
					submitBtn.disabled = false
					submitText.textContent = 'Send Reset Link'
					submitSpinner.classList.add('hidden')
				}
			})

			// Initialize on page load
			initAuth();
			setupAuthStateListener();

			// Make auth functions globally available using official Supabase methods
			window.authFunctions = {
				showAuthModal: showModal,
				getCurrentUser: () => currentUser,
				getUserProfile: () => userProfile,
				signOut: async () => {
					try {
						const { error } = await supabase.auth.signOut()
						if (error) {
							console.error('Sign out error:', error)
							throw error
						}
						currentUser = null
						userProfile = null
						showNotification('Successfully signed out. See you next time!', 'success');
					} catch (error) {
						console.error('Sign out error:', error)
						throw error
					}
				},
				deleteUser: async () => {
					try {
						// Note: User deletion from Supabase auth is handled by the backend API
						// This function is mainly for cleanup after successful deletion
						currentUser = null
						userProfile = null
					} catch (error) {
						console.error('Delete user error:', error)
						throw error
					}
				},
				hasPermission: async (permission) => {
					if (!currentUser || !userProfile) return false

					switch (permission) {
						case 'post_classified':
							return userProfile.is_active
						case 'manage_business':
							return userProfile.role === 'business_owner' || userProfile.role === 'administrator'
						case 'admin_access':
							return userProfile.role === 'administrator'
						case 'moderate_content':
							return userProfile.role === 'administrator'
						default:
							return false
					}
				}
			}
		</script>
	</body>
</html>

<style is:global>
	html {
		font-family: 'Inter', system-ui, sans-serif;
		scroll-behavior: smooth;
	}

	body {
		line-height: 1.6;
	}

	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		background: #f1f5f9;
	}

	::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
</style>
