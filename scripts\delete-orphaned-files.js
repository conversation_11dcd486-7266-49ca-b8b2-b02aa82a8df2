#!/usr/bin/env node

/**
 * Delete Orphaned Files Script
 * 
 * This script attempts to delete common orphaned files from Supabase storage
 * by testing for files that follow common naming patterns but are not referenced.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Currently referenced files (from our database query)
const referencedFiles = new Set([
  '1749528819782-4qjp0aif9x6.jpg',
  '1749523095203-jzng0wi4w9d.webp',
  '1749523629013-ceu4j0q0sj.webp',
  '1749523647739-wjquwdhi6o.webp',
  '1749524207989-lweohkpj329.jpg',
  '1749524246454-v3bwouxddl.webp',
  '1749524258398-ptakvog5pjj.webp',
  'cv_1749529633790_ujpi6qga40b.pdf'
]);

console.log('🧹 Deleting Orphaned Files');
console.log('===========================');
console.log(`📊 Protected files: ${referencedFiles.size}`);
console.log('');

async function main() {
  try {
    // List all files in the storage bucket
    console.log('📁 Fetching files from storage bucket...');
    const { data: files, error: listError } = await supabase.storage
      .from('classified-images')
      .list('', { limit: 1000 });

    if (listError) {
      throw new Error(`Failed to list files: ${listError.message}`);
    }

    console.log(`📊 Found ${files.length} files in storage`);

    // Find orphaned files
    const orphanedFiles = files.filter(file => !referencedFiles.has(file.name));
    
    console.log(`🗑️  Found ${orphanedFiles.length} orphaned files`);

    if (orphanedFiles.length === 0) {
      console.log('✨ No orphaned files found! Storage is clean.');
      return;
    }

    // Calculate total size
    const totalSize = orphanedFiles.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

    console.log(`💾 Total size to be freed: ${totalSizeMB} MB`);
    console.log('');

    // List orphaned files
    console.log('🗑️  ORPHANED FILES TO DELETE:');
    orphanedFiles.forEach((file, index) => {
      const sizeMB = file.metadata?.size ? (file.metadata.size / 1024 / 1024).toFixed(2) : 'unknown';
      console.log(`${index + 1}. ${file.name} (${sizeMB} MB)`);
    });

    console.log('');
    console.log('🗑️  Deleting orphaned files...');

    // Delete orphaned files
    const filesToDelete = orphanedFiles.map(file => file.name);
    const { error: deleteError } = await supabase.storage
      .from('classified-images')
      .remove(filesToDelete);

    if (deleteError) {
      throw new Error(`Failed to delete files: ${deleteError.message}`);
    }

    console.log(`✅ Successfully deleted ${orphanedFiles.length} orphaned files`);
    console.log(`💾 Freed up ${totalSizeMB} MB of storage space`);
    console.log('');
    console.log('🎉 Storage cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

main();