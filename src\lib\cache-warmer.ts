// Cache warming utility for optimal performance
// Pre-loads frequently accessed data into memory cache

import { getCities, getCategories, getListingsForCity } from './database';
import { cache, cacheKeys, cacheTTL } from './cache';

export class CacheWarmer {
  private isWarming = false;
  private lastWarmTime = 0;
  private readonly warmInterval = 10 * 60 * 1000; // 10 minutes

  async warmCache(): Promise<void> {
    if (this.isWarming) {
      console.log('Cache warming already in progress...');
      return;
    }

    const now = Date.now();
    if (now - this.lastWarmTime < this.warmInterval) {
      console.log('Cache warming skipped - too soon since last warm');
      return;
    }

    this.isWarming = true;
    this.lastWarmTime = now;

    try {
      console.log('🔥 Starting cache warming...');
      const startTime = Date.now();

      // Warm essential data
      await this.warmEssentialData();
      
      // Warm city-specific data
      await this.warmCityData();

      const duration = Date.now() - startTime;
      console.log(`✅ Cache warming completed in ${duration}ms`);
      
      // Log cache stats
      const stats = cache.getStats();
      console.log(`📊 Cache stats: ${stats.size} entries cached`);

    } catch (error) {
      console.error('❌ Cache warming failed:', error);
    } finally {
      this.isWarming = false;
    }
  }

  private async warmEssentialData(): Promise<void> {
    console.log('🏗️ Warming essential data...');

    // Warm cities (most frequently accessed)
    const citiesKey = cacheKeys.cities();
    if (!cache.get(citiesKey)) {
      const cities = await getCities();
      cache.set(citiesKey, cities, cacheTTL.cities);
      console.log('  ✓ Cities cached');
    }

    // Warm categories (very frequently accessed)
    const categoriesKey = cacheKeys.categories();
    if (!cache.get(categoriesKey)) {
      const categories = await getCategories();
      cache.set(categoriesKey, categories, cacheTTL.categories);
      console.log('  ✓ Categories cached');
    }
  }

  private async warmCityData(): Promise<void> {
    console.log('🏙️ Warming city-specific data...');

    // Get cities to warm their data
    const { data: cities } = await getCities();
    if (!cities) return;

    // Focus on active cities (those with path_slug)
    const activeCities = cities.filter(city => city.path_slug);
    
    for (const city of activeCities.slice(0, 5)) { // Limit to top 5 cities
      try {
        // Warm city by slug lookup
        const citySlugKey = cacheKeys.cityBySlug(city.path_slug);
        if (!cache.get(citySlugKey)) {
          cache.set(citySlugKey, { data: city, error: null }, cacheTTL.cities);
        }

        // Warm city listings
        const cityListingsKey = cacheKeys.listingsForCity(city.id);
        if (!cache.get(cityListingsKey)) {
          const listings = await getListingsForCity(city.id);
          cache.set(cityListingsKey, listings, cacheTTL.listings);
          console.log(`  ✓ ${city.name} listings cached`);
        }

      } catch (error) {
        console.error(`  ❌ Failed to warm ${city.name}:`, error);
      }
    }
  }

  // Selective cache invalidation for specific data types
  async invalidateCity(cityId: string): Promise<void> {
    cache.delete(cacheKeys.listingsForCity(cityId));
    console.log(`🗑️ Invalidated cache for city ${cityId}`);
  }

  async invalidateCategories(): Promise<void> {
    cache.delete(cacheKeys.categories());
    console.log('🗑️ Invalidated categories cache');
  }

  // Get cache performance metrics
  getCacheMetrics() {
    const stats = cache.getStats();
    return {
      size: stats.size,
      keys: stats.keys,
      isWarming: this.isWarming,
      lastWarmTime: this.lastWarmTime,
      nextWarmTime: this.lastWarmTime + this.warmInterval
    };
  }

  // Force immediate cache warming (for admin use)
  async forceWarm(): Promise<void> {
    this.lastWarmTime = 0; // Reset timer
    await this.warmCache();
  }
}

// Global cache warmer instance
export const cacheWarmer = new CacheWarmer();

// Auto-warm cache on server start
if (typeof process !== 'undefined') {
  // Warm cache after a short delay to allow server to fully start
  setTimeout(() => {
    cacheWarmer.warmCache().catch(console.error);
  }, 5000);

  // Set up periodic cache warming
  setInterval(() => {
    cacheWarmer.warmCache().catch(console.error);
  }, 10 * 60 * 1000); // Every 10 minutes
}

// Export utility functions for manual cache management
export const cacheUtils = {
  // Warm cache for a specific city
  warmCity: async (citySlug: string) => {
    try {
      const { getCityBySlug, getListingsForCity } = await import('./database');
      const { data: city } = await getCityBySlug(citySlug);
      if (city) {
        const listings = await getListingsForCity(city.id);
        cache.set(cacheKeys.listingsForCity(city.id), listings, cacheTTL.listings);
        console.log(`✅ Warmed cache for ${city.name}`);
      }
    } catch (error) {
      console.error(`❌ Failed to warm cache for ${citySlug}:`, error);
    }
  },

  // Clear all cache
  clearAll: () => {
    cache.clear();
    console.log('🗑️ All cache cleared');
  },

  // Get cache status
  getStatus: () => cacheWarmer.getCacheMetrics()
};
