import type { APIRoute } from 'astro';
import bcrypt from 'bcryptjs';
import { getUserPasswordHash, updateUserPasswordHash } from '../../../lib/database.js';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { currentPassword, newPassword, userId } = await request.json();

    if (!currentPassword || !newPassword || !userId) {
      return new Response(JSON.stringify({ error: 'Current password, new password, and user ID are required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (newPassword.length < 8) {
      return new Response(JSON.stringify({ error: 'New password must be at least 8 characters long' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's current password using direct database connection
    const { data: user, error: getUserError } = await getUserPasswordHash(userId);

    if (getUserError) {
      console.error('Error fetching user:', getUserError);
      return new Response(JSON.stringify({ error: 'Failed to fetch user data' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!user) {
      return new Response(JSON.stringify({ error: 'User not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify current password using bcrypt
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.encrypted_password);
    if (!isCurrentPasswordValid) {
      return new Response(JSON.stringify({ error: 'Current password is incorrect' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Hash the new password using bcrypt with cost factor 10 (same as Supabase)
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update password in database using direct connection
    const { error: updateError } = await updateUserPasswordHash(userId, hashedNewPassword);

    if (updateError) {
      console.error('Error updating password:', updateError);
      return new Response(JSON.stringify({ error: 'Failed to update password' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Password updated successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in change password API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
