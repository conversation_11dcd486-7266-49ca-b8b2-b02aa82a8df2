---
export interface Props {
  type: 'website' | 'business' | 'category' | 'city';
  data: any;
}

const { type, data } = Astro.props;

function generateStructuredData() {
  const baseData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ExpatsList",
    "url": "https://expatslist.org",
    "description": "Find trusted local businesses and services for expats worldwide",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://expatslist.org/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  switch (type) {
    case 'business':
      return {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": data.business_name,
        "description": data.description_short || data.description_long,
        "address": {
          "@type": "PostalAddress",
          "streetAddress": data.address_full,
          "addressLocality": data.city_name,
          "addressCountry": "MX"
        },
        "telephone": data.contact_info?.phone,
        "email": data.contact_info?.email,
        "url": data.contact_info?.website,
        "sameAs": [
          data.contact_info?.facebook,
          data.contact_info?.instagram,
          data.contact_info?.twitter
        ].filter(Boolean),
        "aggregateRating": data.thumbs_up_count > 0 ? {
          "@type": "AggregateRating",
          "ratingValue": Math.min(5, Math.max(1, (data.thumbs_up_count / (data.thumbs_up_count + (data.thumbs_down_count || 0))) * 5)),
          "reviewCount": data.thumbs_up_count + (data.thumbs_down_count || 0)
        } : undefined
      };

    case 'category':
      return {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": `${data.category_name} in ${data.city_name}`,
        "description": `Find the best ${data.category_name.toLowerCase()} in ${data.city_name}`,
        "url": Astro.url.href,
        "mainEntity": {
          "@type": "ItemList",
          "numberOfItems": data.listings?.length || 0,
          "itemListElement": data.listings?.slice(0, 10).map((listing: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "item": {
              "@type": "LocalBusiness",
              "name": listing.business_name,
              "description": listing.description_short
            }
          })) || []
        }
      };

    case 'city':
      return {
        "@context": "https://schema.org",
        "@type": "City",
        "name": data.city_name,
        "description": `Expat services and businesses in ${data.city_name}`,
        "url": Astro.url.href,
        "containedInPlace": {
          "@type": "Country",
          "name": data.country || "Mexico"
        }
      };

    default:
      return baseData;
  }
}

const structuredData = generateStructuredData();
---

<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
