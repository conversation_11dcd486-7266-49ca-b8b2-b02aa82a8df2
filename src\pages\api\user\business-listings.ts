import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's business listings (claimed or owned)
    const result = await query(`
      SELECT
        l.id,
        l.business_name,
        l.category_primary_id,
        l.city_id,
        l.is_verified_expatslist,
        l.claimed_by_user_id,
        l.created_at,
        c.name as category_name,
        ci.name as city_name
      FROM listings l
      LEFT JOIN categories c ON l.category_primary_id = c.id
      LEFT JOIN cities ci ON l.city_id = ci.id
      WHERE l.claimed_by_user_id = $1
      ORDER BY l.created_at DESC
    `, [userId]);

    return new Response(JSON.stringify({
      listings: result.rows || []
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in user business listings API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
