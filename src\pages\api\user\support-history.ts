import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    const userId = url.searchParams.get('userId');
    
    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's support requests with admin responses
    const result = await query(`
      SELECT 
        id,
        type,
        subject,
        message,
        status,
        admin_response,
        created_at,
        updated_at,
        resolved_at
      FROM support_requests
      WHERE user_id = $1
      ORDER BY created_at DESC
    `, [userId]);

    return new Response(JSON.stringify({ 
      requests: result.rows || []
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in user support history API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
