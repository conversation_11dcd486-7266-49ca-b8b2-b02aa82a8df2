---
import Layout from '../../layouts/Layout.astro';

const pageTitle = 'Reset Your Password';
---

<Layout title={pageTitle}>
  <!-- Beautiful UX Animations -->
  <style>
    @keyframes bounce-in {
      0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.3);
      }
      50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
      }
      70% {
        transform: translate(-50%, -50%) scale(0.9);
      }
      100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }

    .animate-bounce-in {
      animation: bounce-in 0.6s ease-out;
    }

    @keyframes fade-in {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .animate-fade-in {
      animation: fade-in 0.8s ease-out;
    }
  </style>

  <main class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md animate-fade-in">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
          <span class="text-2xl">🔐</span>
        </div>
        <h1 class="text-2xl font-bold text-slate-900 mb-2">Reset Your Password</h1>
        <p class="text-slate-600">Enter your new password below</p>
      </div>

      <!-- Reset Password Form -->
      <div class="bg-white rounded-2xl shadow-xl border border-slate-200 p-8">
        <form id="reset-password-form" class="space-y-6">
          <!-- New Password -->
          <div>
            <label for="new-password" class="block text-sm font-medium text-slate-700 mb-2">
              New Password *
            </label>
            <input
              type="password"
              id="new-password"
              required
              minlength="8"
              class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Enter your new password"
            />
            
            <!-- Password Strength Indicator -->
            <div id="password-strength" class="mt-2 hidden">
              <div class="flex items-center space-x-2 mb-1">
                <div class="flex-1 bg-slate-200 rounded-full h-2">
                  <div id="strength-bar" class="h-2 rounded-full transition-all duration-300"></div>
                </div>
                <span id="strength-text" class="text-xs font-medium"></span>
              </div>
              <div id="strength-requirements" class="text-xs text-slate-500 space-y-1">
                <div id="req-length" class="flex items-center space-x-1">
                  <span class="req-icon">❌</span>
                  <span>At least 8 characters</span>
                </div>
                <div id="req-uppercase" class="flex items-center space-x-1">
                  <span class="req-icon">❌</span>
                  <span>One uppercase letter</span>
                </div>
                <div id="req-lowercase" class="flex items-center space-x-1">
                  <span class="req-icon">❌</span>
                  <span>One lowercase letter</span>
                </div>
                <div id="req-number" class="flex items-center space-x-1">
                  <span class="req-icon">❌</span>
                  <span>One number</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Confirm Password -->
          <div>
            <label for="confirm-password" class="block text-sm font-medium text-slate-700 mb-2">
              Confirm New Password *
            </label>
            <input
              type="password"
              id="confirm-password"
              required
              minlength="8"
              class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Confirm your new password"
            />
            
            <!-- Password Match Indicator -->
            <div id="password-match" class="mt-1 text-xs hidden">
              <span id="match-text"></span>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            id="reset-submit-btn"
            class="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 disabled:from-slate-400 disabled:to-slate-400 text-white py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center"
          >
            <span id="reset-submit-text">Reset Password</span>
            <div id="reset-submit-spinner" class="hidden ml-2 w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </button>
        </form>

        <!-- Back to Sign In -->
        <div class="mt-6 text-center">
          <a href="/" class="text-sm text-slate-600 hover:text-blue-600 transition-colors">
            ← Back to Home
          </a>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div id="reset-feedback" class="mt-4 hidden">
        <!-- Dynamic content will be inserted here -->
      </div>
    </div>
  </main>

  <script type="module">
    // Import Supabase client
    import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

    // Initialize Supabase client
    const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co';
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM';

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        storage: window.localStorage,
        storageKey: 'expatslist-auth'
      }
    });

    // Make supabase available globally for this page
    window.supabase = supabase;

    // Wait for page to load
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('DOM loaded, initializing reset password page...');

      // Let Supabase automatically detect and handle the session from URL
      // This will process the URL hash parameters automatically
      try {
        // Wait a moment for Supabase to process the URL
        await new Promise(resolve => setTimeout(resolve, 100));

        const { data: { session }, error: sessionError } = await window.supabase.auth.getSession();

        console.log('Session check:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          sessionError: sessionError?.message,
          fullURL: window.location.href
        });

        if (sessionError) {
          console.error('Session error:', sessionError);
          showError('Invalid or expired reset link. Please request a new password reset.');
          return;
        }

        if (!session || !session.user) {
          // Check for error in URL
          const urlParams = new URLSearchParams(window.location.search);
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const error = urlParams.get('error') || hashParams.get('error');
          const errorDescription = urlParams.get('error_description') || hashParams.get('error_description');

          if (error) {
            const errorMsg = errorDescription ? decodeURIComponent(errorDescription.replace(/\+/g, ' ')) : error;
            showError(`Reset link error: ${errorMsg}. Please request a new password reset.`);
          } else {
            showError('Invalid or expired reset link. Please request a new password reset.');
          }
          return;
        }

        console.log('Valid reset session found for user:', session.user.email);

        // Initialize password strength checking
        setupPasswordValidation();

        // Handle form submission
        const form = document.getElementById('reset-password-form');
        if (form) {
          console.log('Form found, attaching event listener');
          form.addEventListener('submit', handlePasswordReset);
        } else {
          console.error('Reset password form not found!');
          showError('Form initialization error. Please refresh the page.');
        }

      } catch (error) {
        console.error('Error initializing reset password page:', error);
        showError('Failed to initialize password reset. Please try again.');
      }
    });

    function setupPasswordValidation() {
      const newPasswordInput = document.getElementById('new-password');
      const confirmPasswordInput = document.getElementById('confirm-password');

      newPasswordInput.addEventListener('input', (e) => {
        const password = e.target.value;
        updatePasswordStrength(password);
        checkPasswordMatch();
      });

      confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    }

    function updatePasswordStrength(password) {
      const strengthDiv = document.getElementById('password-strength');
      const strengthBar = document.getElementById('strength-bar');
      const strengthText = document.getElementById('strength-text');

      if (password.length === 0) {
        strengthDiv.classList.add('hidden');
        return;
      }

      strengthDiv.classList.remove('hidden');

      const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password)
      };

      // Update requirement indicators
      Object.keys(requirements).forEach(req => {
        const element = document.getElementById(`req-${req}`);
        const icon = element.querySelector('.req-icon');
        if (requirements[req]) {
          icon.textContent = '✅';
          element.classList.add('text-green-600');
          element.classList.remove('text-slate-500');
        } else {
          icon.textContent = '❌';
          element.classList.add('text-slate-500');
          element.classList.remove('text-green-600');
        }
      });

      // Calculate strength
      const score = Object.values(requirements).filter(Boolean).length;
      const strength = score / 4;

      // Update strength bar
      strengthBar.style.width = `${strength * 100}%`;
      
      if (strength < 0.5) {
        strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-red-500';
        strengthText.textContent = 'Weak';
        strengthText.className = 'text-xs font-medium text-red-600';
      } else if (strength < 0.75) {
        strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-yellow-500';
        strengthText.textContent = 'Fair';
        strengthText.className = 'text-xs font-medium text-yellow-600';
      } else if (strength < 1) {
        strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-blue-500';
        strengthText.textContent = 'Good';
        strengthText.className = 'text-xs font-medium text-blue-600';
      } else {
        strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-green-500';
        strengthText.textContent = 'Strong';
        strengthText.className = 'text-xs font-medium text-green-600';
      }

      return { strength, score };
    }

    function checkPasswordMatch() {
      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const matchDiv = document.getElementById('password-match');
      const matchText = document.getElementById('match-text');

      if (confirmPassword.length === 0) {
        matchDiv.classList.add('hidden');
        return;
      }

      matchDiv.classList.remove('hidden');

      if (newPassword === confirmPassword) {
        matchText.textContent = '✅ Passwords match';
        matchText.className = 'text-green-600';
      } else {
        matchText.textContent = '❌ Passwords do not match';
        matchText.className = 'text-red-600';
      }
    }

    async function handlePasswordReset(e) {
      console.log('Form submitted, handling password reset...');
      e.preventDefault();

      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;

      console.log('Password values:', {
        newPasswordLength: newPassword.length,
        confirmPasswordLength: confirmPassword.length,
        passwordsMatch: newPassword === confirmPassword
      });

      // Validation
      if (newPassword !== confirmPassword) {
        showError('Passwords do not match');
        return;
      }

      const { strength } = updatePasswordStrength(newPassword);
      console.log('Password strength:', strength);

      if (strength < 0.5) {
        showError('Password is too weak. Please choose a stronger password.');
        return;
      }

      const submitBtn = document.getElementById('reset-submit-btn');
      const submitText = document.getElementById('reset-submit-text');
      const submitSpinner = document.getElementById('reset-submit-spinner');

      if (!submitBtn || !submitText || !submitSpinner) {
        console.error('Submit button elements not found');
        showError('Form elements not found. Please refresh the page.');
        return;
      }

      // Show loading state with better UX
      submitBtn.disabled = true;
      submitText.textContent = 'Resetting Password...';
      submitSpinner.classList.remove('hidden');

      // Add visual feedback to the form
      const form = document.getElementById('reset-password-form');
      form.style.opacity = '0.7';
      form.style.pointerEvents = 'none';

      try {
        // Get the current session to send with request
        const { data: { session } } = await window.supabase.auth.getSession();
        console.log('Current session:', { hasSession: !!session, hasAccessToken: !!session?.access_token });

        const headers = { 'Content-Type': 'application/json' };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        }

        console.log('Making API request to /api/auth/reset-password');
        const response = await fetch('/api/auth/reset-password', {
          method: 'POST',
          headers,
          body: JSON.stringify({ newPassword })
        });

        console.log('API response status:', response.status);
        const result = await response.json();
        console.log('API response:', result);

        if (result.success) {
          // Hide the form and show success state
          document.getElementById('reset-password-form').style.display = 'none';
          showSuccessState();
        } else {
          showError(result.error || 'Failed to reset password. Please try again.');
        }
      } catch (error) {
        console.error('Reset error:', error);
        showError('Unable to reset password right now. Please check your connection and try again.');
      } finally {
        // Only restore form state if we're not showing success
        if (!document.querySelector('.bg-gradient-to-br.from-green-500')) {
          submitBtn.disabled = false;
          submitText.textContent = 'Reset Password';
          submitSpinner.classList.add('hidden');

          // Restore form interactivity
          const form = document.getElementById('reset-password-form');
          form.style.opacity = '1';
          form.style.pointerEvents = 'auto';
        }
      }
    }

    function showError(message) {
      showFeedback(message, 'error');
    }

    function showSuccess(message) {
      showFeedback(message, 'success');
    }

    function showSuccessState() {
      // Hide the form container
      const formContainer = document.querySelector('.bg-white.rounded-2xl');
      if (formContainer) {
        formContainer.style.display = 'none';
      }

      // Create and show success state
      const successHTML = `
        <div class="bg-white rounded-2xl shadow-xl border border-slate-200 p-8 text-center animate-fade-in">
          <!-- Success Icon -->
          <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-3xl">✅</span>
          </div>

          <!-- Success Message -->
          <h2 class="text-2xl font-bold text-slate-900 mb-4">Password Reset Successful!</h2>
          <p class="text-slate-600 mb-6 leading-relaxed">
            Your password has been successfully updated. You have been signed out for security reasons.
          </p>

          <!-- Next Steps -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="font-semibold text-blue-900 mb-2">What's Next?</h3>
            <p class="text-blue-800 text-sm">
              Sign in with your new password to continue using your account.
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="space-y-3">
            <button
              onclick="window.location.href = '/'"
              class="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center"
            >
              <span class="mr-2">🏠</span>
              Go to Home & Sign In
            </button>

            <button
              onclick="showSignInModal()"
              class="w-full bg-slate-100 hover:bg-slate-200 text-slate-700 py-3 rounded-lg font-medium transition-colors duration-200"
            >
              Sign In Now
            </button>
          </div>

          <!-- Security Note -->
          <div class="mt-6 text-xs text-slate-500 bg-slate-50 rounded-lg p-3">
            <span class="font-medium">🔒 Security Note:</span> For your protection, you've been automatically signed out. This ensures your account remains secure.
          </div>
        </div>
      `;

      // Insert success state
      const container = document.querySelector('.w-full.max-w-md');
      if (container) {
        container.insertAdjacentHTML('beforeend', successHTML);
      }
    }

    // Function to show sign in modal (if available)
    function showSignInModal() {
      // Try to trigger the sign in modal from the main page
      if (window.parent && window.parent.showAuthModal) {
        window.parent.showAuthModal();
      } else {
        // Fallback: redirect to home page
        window.location.href = '/';
      }
    }

    function showFeedback(message, type) {
      const feedbackDiv = document.getElementById('reset-feedback');
      if (!feedbackDiv) return;

      const isError = type === 'error';

      feedbackDiv.className = `mt-4 p-4 rounded-lg border animate-bounce-in ${
        isError
          ? 'bg-red-50 border-red-200 text-red-800'
          : 'bg-green-50 border-green-200 text-green-800'
      }`;

      // Enhanced error messages with helpful actions
      let actionButton = '';
      if (isError) {
        if (message.includes('Invalid or expired')) {
          actionButton = `
            <button
              onclick="window.location.href = '/'"
              class="mt-3 px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
            >
              Request New Reset Link
            </button>
          `;
        } else if (message.includes('connection')) {
          actionButton = `
            <button
              onclick="location.reload()"
              class="mt-3 px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
            >
              Try Again
            </button>
          `;
        }
      }

      feedbackDiv.innerHTML = `
        <div class="flex items-start space-x-3">
          <span class="text-xl flex-shrink-0 mt-0.5">${isError ? '⚠️' : '🎉'}</span>
          <div class="flex-1">
            <p class="font-semibold text-base">${isError ? 'Password Reset Failed' : 'Success!'}</p>
            <p class="text-sm mt-1 leading-relaxed">${message}</p>
            ${actionButton}
          </div>
        </div>
      `;

      feedbackDiv.classList.remove('hidden');

      // Auto-hide error messages after 10 seconds (longer for better UX)
      if (isError) {
        setTimeout(() => {
          feedbackDiv?.classList.add('hidden');
        }, 10000);
      }
    }
  </script>
</Layout>
