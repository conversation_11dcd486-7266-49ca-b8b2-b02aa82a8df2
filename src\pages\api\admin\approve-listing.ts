import type { APIRoute } from 'astro';
import { updateListingStatus } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId } = await request.json();
    console.log('Approve listing request - Listing ID:', listingId);

    if (!listingId) {
      return new Response(JSON.stringify({ error: 'Missing listing ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update listing status to active
    const { error } = await updateListingStatus(listingId, 'active');

    if (error) {
      console.error('Error approving listing:', error);
      return new Response(JSON.stringify({ error: 'Failed to approve listing' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
