---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "ExpatsList Admin Panel" } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title} | ExpatsList Admin</title>
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Supabase Auth -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  </head>
  <body class="min-h-screen bg-gray-100 font-sans antialiased">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <a href="/admin" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">A</span>
              </div>
              <div class="flex flex-col">
                <span class="text-xl font-bold text-gray-900">ExpatsList</span>
                <span class="text-sm text-red-600 -mt-1">Admin Panel</span>
              </div>
            </a>
          </div>

          <!-- Navigation -->
          <nav class="hidden md:flex items-center space-x-8">
            <a href="/admin" class="text-gray-700 hover:text-red-600 font-medium">Dashboard</a>
            <a href="/admin/listings" class="text-gray-700 hover:text-red-600 font-medium">Listings</a>
            <a href="/admin/users" class="text-gray-700 hover:text-red-600 font-medium">Users</a>
            <a href="/admin/cities" class="text-gray-700 hover:text-red-600 font-medium">Cities</a>
            <a href="/admin/categories" class="text-gray-700 hover:text-red-600 font-medium">Categories</a>
            <a href="/admin/support" class="text-gray-700 hover:text-red-600 font-medium">Support</a>
          </nav>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <button class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-red-600">
              Admin User
            </button>
            <a href="/" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors">
              View Site
            </a>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <slot />
    </main>

    <!-- Auth Functions -->
    <script is:inline>
      const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co';
      const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM';

      function initAuth() {
        if (window.supabase) {
          const supabaseClient = window.supabase.createClient(supabaseUrl, supabaseAnonKey, {
            auth: {
              autoRefreshToken: true,
              persistSession: true,
              detectSessionInUrl: true,
              flowType: 'pkce',
              storage: window.localStorage,
              storageKey: 'expatslist-auth'
            }
          });

          window.authFunctions = {
            getCurrentUser: async function() {
              const { data: { user } } = await supabaseClient.auth.getUser();
              return user;
            }
          };
        } else {
          setTimeout(initAuth, 100);
        }
      }

      initAuth();
    </script>
  </body>
</html>

<style is:global>
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    line-height: 1.6;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
</style>
