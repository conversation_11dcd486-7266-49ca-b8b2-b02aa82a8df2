---
interface Props {
  email?: string;
  phone?: string;
  whatsapp?: string;
  preferred_contact_method?: 'email' | 'phone' | 'whatsapp';
  type?: 'business' | 'classified';
  size?: 'small' | 'medium' | 'large';
  style?: 'pill' | 'button' | 'text';
}

const {
  email,
  phone,
  whatsapp,
  preferred_contact_method,
  type = 'business',
  size = 'medium',
  style = 'button'
} = Astro.props;

// Size classes - Mobile optimized
const sizeClasses = {
  small: 'text-xs px-2 py-1.5 min-h-[32px]',
  medium: 'text-sm px-3 py-2 min-h-[36px]',
  large: 'text-base px-4 py-3 min-h-[44px]'
};

// Style classes
const styleClasses = {
  pill: 'rounded-full font-medium',
  button: 'rounded-lg font-semibold',
  text: 'font-medium'
};

const baseClasses = `${sizeClasses[size]} ${styleClasses[style]} transition-all duration-200`;

// Create contact methods array with preference ordering
const contactMethods = [];
if (email) contactMethods.push({ type: 'email', value: email });
if (phone) contactMethods.push({ type: 'phone', value: phone });
if (whatsapp) contactMethods.push({ type: 'whatsapp', value: whatsapp });

// Sort by preferred method first
if (preferred_contact_method) {
  contactMethods.sort((a, b) => {
    if (a.type === preferred_contact_method) return -1;
    if (b.type === preferred_contact_method) return 1;
    return 0;
  });
}
---

<!-- Dynamic Contact Methods in Preference Order -->
{contactMethods.map((contact, index) => {
  const isPreferred = contact.type === preferred_contact_method;
  const preferredBadge = isPreferred ? '⭐ ' : '';

  // Contact type specific configurations
  const configs = {
    email: {
      icon: '📧',
      color: 'purple',
      action: `mailto:${contact.value}`,
      label: 'Send Email',
      loginText: 'Login to see email'
    },
    phone: {
      icon: '📞',
      color: 'blue',
      action: `tel:${contact.value}`,
      label: 'Call Now',
      loginText: 'Login to see phone'
    },
    whatsapp: {
      icon: '💬',
      color: 'green',
      action: `https://wa.me/${contact.value.replace(/[^0-9]/g, '')}`,
      label: 'WhatsApp',
      loginText: 'Login for WhatsApp'
    }
  };

  const config = configs[contact.type as keyof typeof configs];

  return (
    <div class="protected-contact-item" data-contact-type={contact.type} data-contact-value={contact.value}>
      <!-- Logged in users see actual contact -->
      <div class="contact-authenticated hidden">
        {style === 'pill' ? (
          <span class={`${baseClasses} bg-${config.color}-50 text-${config.color}-700 border border-${config.color}-200`}>
            {preferredBadge}{config.icon} {contact.value}
          </span>
        ) : style === 'button' ? (
          <a
            href={config.action}
            target={contact.type === 'whatsapp' ? '_blank' : undefined}
            rel={contact.type === 'whatsapp' ? 'noopener noreferrer' : undefined}
            class={`${baseClasses} bg-${config.color}-600 hover:bg-${config.color}-700 text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 inline-flex items-center`}
          >
            <span class="mr-2">{preferredBadge}{config.icon}</span>
            {config.label}
          </a>
        ) : (
          <a
            href={config.action}
            target={contact.type === 'whatsapp' ? '_blank' : undefined}
            rel={contact.type === 'whatsapp' ? 'noopener noreferrer' : undefined}
            class={`${baseClasses} text-${config.color}-600 hover:text-${config.color}-700`}
          >
            {preferredBadge}{config.icon} {contact.value}
          </a>
        )}
      </div>

      <!-- Non-logged in users see login prompt -->
      <div class="contact-login-required">
        {style === 'pill' ? (
          <button
            onclick="window.authFunctions?.showAuthModal()"
            class={`${baseClasses} bg-slate-100 hover:bg-slate-200 text-slate-600 border border-slate-300 cursor-pointer`}
          >
            {preferredBadge}{config.icon} {config.loginText}
          </button>
        ) : style === 'button' ? (
          <button
            onclick="window.authFunctions?.showAuthModal()"
            class={`${baseClasses} bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 inline-flex items-center cursor-pointer group`}
          >
            <span class="mr-1.5 text-sm group-hover:scale-110 transition-transform">{preferredBadge}{config.icon}</span>
            <span class="text-xs">{config.loginText}</span>
          </button>
        ) : (
          <button
            onclick="window.authFunctions?.showAuthModal()"
            class={`${baseClasses} text-slate-600 hover:text-slate-700 cursor-pointer`}
          >
            {preferredBadge}{config.icon} {config.loginText}
          </button>
        )}
      </div>
    </div>
  );
})}



<script>
  // Initialize contact protection when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    updateContactVisibility();
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', updateContactVisibility);
  });

  function updateContactVisibility() {
    // Wait for auth functions to be available
    if (!window.authFunctions) {
      setTimeout(updateContactVisibility, 100);
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();
    const isLoggedIn = !!currentUser;

    // Update all protected contact items
    document.querySelectorAll('.protected-contact-item').forEach(item => {
      const authenticatedDiv = item.querySelector('.contact-authenticated');
      const loginRequiredDiv = item.querySelector('.contact-login-required');

      if (isLoggedIn) {
        authenticatedDiv?.classList.remove('hidden');
        loginRequiredDiv?.classList.add('hidden');
      } else {
        authenticatedDiv?.classList.add('hidden');
        loginRequiredDiv?.classList.remove('hidden');
      }
    });
  }
</script>
