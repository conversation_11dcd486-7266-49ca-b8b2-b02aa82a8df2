// In-memory cache for fast data access
// This provides Craigslist-like performance by avoiding database hits

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes default

  set<T>(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Get cache stats for monitoring
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
export const cache = new MemoryCache();

// Cache key generators for consistent naming
export const cacheKeys = {
  cities: () => 'cities:all',
  categories: () => 'categories:all',
  totalBusinessCount: () => 'total_business_count',
  totalClassifiedCount: () => 'total_classified_count',
  cityBySlug: (slug: string) => `city:slug:${slug}`,
  listingsForCity: (cityId: string) => `listings:city:${cityId}`,
  listingsForCategory: (cityId: string, categoryId: string) => `listings:category:${cityId}:${categoryId}`,
  listingsForSubcategory: (cityId: string, subcategoryId: string) => `listings:subcategory:${cityId}:${subcategoryId}`,
  businessListing: (cityId: string, subcategoryId: string, slug: string) => `business:${cityId}:${subcategoryId}:${slug}`,
  categoryCounts: (cityId: string) => `counts:categories:${cityId}`,
  subcategoryCounts: (cityId: string, categoryId: string) => `counts:subcategories:${cityId}:${categoryId}`
};

// Cache TTL configurations (in milliseconds)
export const cacheTTL = {
  cities: 5 * 60 * 1000,         // 5 minutes - cities change when listings are added
  categories: 30 * 60 * 1000,    // 30 minutes - categories rarely change
  listings: 3 * 60 * 1000,       // 3 minutes - listings change more frequently
  business: 10 * 60 * 1000,      // 10 minutes - individual business pages
  counts: 2 * 60 * 1000,         // 2 minutes - category counts for real-time updates
  search: 2 * 60 * 1000,         // 2 minutes - search results
  totalBusinessCount: 2 * 60 * 1000,  // 2 minutes - total business count for real-time updates
  totalClassifiedCount: 2 * 60 * 1000,  // 2 minutes - total classified count for real-time updates
  weather: 30 * 60 * 1000        // 30 minutes - weather data (changes slowly)
};

// Cached wrapper functions for database operations
export async function getCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Try cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch from database
  const data = await fetcher();
  
  // Store in cache
  cache.set(key, data, ttl);
  
  return data;
}

// Cache invalidation helpers
export const invalidateCache = {
  // Invalidate when new listing is added
  onNewListing: (cityId: string, categoryId: string, subcategoryId: string) => {
    cache.delete(cacheKeys.listingsForCity(cityId));
    cache.delete(cacheKeys.listingsForCategory(cityId, categoryId));
    cache.delete(cacheKeys.listingsForSubcategory(cityId, subcategoryId));
    cache.delete(cacheKeys.categoryCounts(cityId));
    cache.delete(cacheKeys.subcategoryCounts(cityId, categoryId));
    cache.delete(cacheKeys.totalBusinessCount());
    cache.delete(cacheKeys.cities()); // Invalidate cities to update counts
  },

  // Invalidate when listing is updated
  onListingUpdate: (cityId: string, categoryId: string, subcategoryId: string, slug: string) => {
    cache.delete(cacheKeys.businessListing(cityId, subcategoryId, slug));
    cache.delete(cacheKeys.listingsForCity(cityId));
    cache.delete(cacheKeys.listingsForCategory(cityId, categoryId));
    cache.delete(cacheKeys.listingsForSubcategory(cityId, subcategoryId));
  },

  // Invalidate when listing status changes
  onListingStatusChange: (cityId: string, categoryId: string, subcategoryId: string) => {
    cache.delete(cacheKeys.listingsForCity(cityId));
    cache.delete(cacheKeys.listingsForCategory(cityId, categoryId));
    cache.delete(cacheKeys.listingsForSubcategory(cityId, subcategoryId));
    cache.delete(cacheKeys.categoryCounts(cityId));
    cache.delete(cacheKeys.subcategoryCounts(cityId, categoryId));
    cache.delete(cacheKeys.totalBusinessCount());
    cache.delete(cacheKeys.cities()); // Invalidate cities to update counts
  },

  // Invalidate when classified post is created/updated/deleted
  onClassifiedChange: () => {
    cache.delete(cacheKeys.totalClassifiedCount());
    cache.delete(cacheKeys.cities()); // Invalidate cities to update classified counts
  },

  // Clear all cache
  all: () => {
    cache.clear();
  }
};

// Periodic cleanup (run every 10 minutes)
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    cache.cleanup();
  }, 10 * 60 * 1000);
}
