import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client for storage operations
const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    console.log('Delete post request - Post ID:', data.postId);

    // Validate required fields
    if (!data.postId) {
      return new Response(JSON.stringify({
        error: 'Post ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // First, get the post data to extract file URLs for cleanup
    const postData = await query(`
      SELECT image_url, additional_images, category_specific_data, title, category
      FROM classified_posts
      WHERE id = $1
    `, [data.postId]);

    if (postData.rows.length === 0) {
      return new Response(JSON.stringify({ error: 'Post not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const post = postData.rows[0];
    const filesToDelete = [];

    // Collect image URLs for deletion
    if (post.image_url) {
      const fileName = post.image_url.split('/').pop();
      if (fileName) filesToDelete.push(fileName);
    }

    if (post.additional_images) {
      try {
        const additionalImages = JSON.parse(post.additional_images);
        additionalImages.forEach((url: string) => {
          const fileName = url.split('/').pop();
          if (fileName) filesToDelete.push(fileName);
        });
      } catch (e) {
        // Ignore JSON parse errors
      }
    }

    // Collect CV/document URLs for deletion
    if (post.category_specific_data) {
      try {
        const categoryData = JSON.parse(post.category_specific_data);
        if (categoryData.cv_url) {
          const fileName = categoryData.cv_url.split('/').pop();
          if (fileName) filesToDelete.push(fileName);
        }
      } catch (e) {
        // Ignore JSON parse errors
      }
    }

    // Delete the classified post from database
    const deleteResult = await query(`
      DELETE FROM classified_posts 
      WHERE id = $1 
      RETURNING id, title, category
    `, [data.postId]);

    if (deleteResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Post not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const deletedPost = deleteResult.rows[0];

    // Clean up storage files (don't fail the operation if file deletion fails)
    if (filesToDelete.length > 0) {
      try {
        const { error: deleteError } = await supabase.storage
          .from('classified-images')
          .remove(filesToDelete);
        
        if (deleteError) {
          console.warn('Some files could not be deleted from storage:', deleteError);
        }
      } catch (storageError) {
        console.warn('Storage cleanup failed:', storageError);
      }
    }

    // Invalidate cache to ensure real-time updates
    const { invalidateCache } = await import('../../../lib/cache');
    invalidateCache.onClassifiedChange();

    return new Response(JSON.stringify({
      success: true,
      message: `Post "${deletedPost.title}" deleted successfully`,
      deletedPost: deletedPost,
      filesDeleted: filesToDelete.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error deleting post:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete post'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
