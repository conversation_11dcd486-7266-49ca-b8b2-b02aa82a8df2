import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    console.log('Delete post request - Post ID:', data.postId);

    // Validate required fields
    if (!data.postId) {
      return new Response(JSON.stringify({
        error: 'Post ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Delete the classified post
    const deleteResult = await query(`
      DELETE FROM classified_posts 
      WHERE id = $1 
      RETURNING id, title, category
    `, [data.postId]);

    if (deleteResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Post not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const deletedPost = deleteResult.rows[0];

    // Invalidate cache to ensure real-time updates
    const { invalidateCache } = await import('../../../lib/cache');
    invalidateCache.onClassifiedChange();

    return new Response(JSON.stringify({
      success: true,
      message: `Post "${deletedPost.title}" deleted successfully`,
      deletedPost: deletedPost
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error deleting post:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete post'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
