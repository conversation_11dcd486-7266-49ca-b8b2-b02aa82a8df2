---
import { getClassifiedCategoryCounts, CLASSIFIED_CATEGORIES } from '../lib/classifieds';

interface Props {
  citySlug: string;
}

const { citySlug } = Astro.props;

// Get category counts
const { data: categoryCounts } = await getClassifiedCategoryCounts(citySlug);

// Create counts map for easy lookup
const countsMap = new Map();
categoryCounts?.forEach((item: any) => {
  countsMap.set(item.category, parseInt(item.count));
});

// Get total count
const totalCount = categoryCounts?.reduce((sum: number, item: any) => sum + parseInt(item.count), 0) || 0;
---

<!-- Clean Expat Classifieds Section -->
<div class="bg-white rounded-xl border border-slate-200 overflow-hidden shadow-sm">
  <!-- Compact Header -->
  <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 px-6 py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
          <span class="text-lg">🏷️</span>
        </div>
        <div>
          <h2 class="text-lg font-bold text-white">Expat Classifieds</h2>
          <p class="text-emerald-100 text-xs">Connect with the community</p>
        </div>
      </div>
      <div class="text-right">
        <div class="text-lg font-bold text-white">{totalCount}</div>
        <div class="text-xs text-emerald-100">listings</div>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="p-4">
    <!-- Clean Category Grid -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
      {Object.entries(CLASSIFIED_CATEGORIES).map(([key, category]) => {
        const count = countsMap.get(key) || 0;
        return (
          <a
            href={`/${citySlug}/classifieds/${key}`}
            class="group bg-slate-50 hover:bg-emerald-50 border border-slate-200 hover:border-emerald-300 rounded-lg p-3 transition-all duration-200 hover:shadow-sm"
          >
            <div class="text-center">
              <div class="text-2xl mb-2 group-hover:scale-105 transition-transform duration-200">
                {category.icon}
              </div>
              <h3 class="font-semibold text-slate-900 mb-1 text-sm">
                {category.name}
              </h3>
              <p class="text-xs text-slate-600 mb-2 line-clamp-2">
                {category.shortDesc}
              </p>
              <div class="flex items-center justify-center space-x-1">
                <span class="text-xs font-medium text-emerald-600">
                  {count > 0 ? `${count} listing${count === 1 ? '' : 's'}` : '0 listings'}
                </span>
                <svg class="w-3 h-3 text-slate-400 group-hover:text-emerald-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </a>
        );
      })}
    </div>

    <!-- Single Combined Action Button -->
    <div class="flex items-center justify-center">
      <a
        href={`/${citySlug}/classifieds`}
        class="w-full bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center flex items-center justify-center space-x-2"
      >
        <span class="text-lg">🏷️</span>
        <span>Browse & Post Classifieds</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </a>
    </div>
  </div>
</div>

<style>
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>


