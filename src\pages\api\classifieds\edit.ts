import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.id || !data.title || !data.description) {
      return new Response(JSON.stringify({
        error: 'Missing required fields'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Handle dual currency pricing
    const priceLocal = data.price_local ? parseFloat(data.price_local) : null;
    const priceUsd = data.price_usd ? parseFloat(data.price_usd) : null;
    const mainPrice = priceUsd || priceLocal || data.price || null;

    // Handle multi-image updates intelligently
    let imageUrl = null;
    let additionalImages = null;

    if (data.image_urls && Array.isArray(data.image_urls)) {
      // New multi-image format
      const allImages = data.image_urls.filter(url => url && url.trim() !== '');
      if (allImages.length > 0) {
        imageUrl = allImages[0]; // Primary image
        additionalImages = allImages.slice(1); // Additional images
      }
      console.log('Multi-image update:', { primary: imageUrl, additional: additionalImages });
    } else {
      // Legacy single image format
      if (data.remove_current_image) {
        // User explicitly removed the current image
        imageUrl = null;
        additionalImages = [];
      } else if (data.new_image_url) {
        // User uploaded a new image
        imageUrl = data.new_image_url;
        // Keep existing additional images
        const currentPost = await query('SELECT additional_images FROM classified_posts WHERE id = $1', [data.id]);
        additionalImages = currentPost.rows[0]?.additional_images || [];
      } else {
        // Keep existing images - get current images from database
        const currentPost = await query('SELECT image_url, additional_images FROM classified_posts WHERE id = $1', [data.id]);
        imageUrl = currentPost.rows[0]?.image_url || null;
        additionalImages = currentPost.rows[0]?.additional_images || [];
      }
    }

    // Update classified post with all fields including new job fields and multi-image handling
    const result = await query(`
      UPDATE classified_posts
      SET title = $1, description = $2, price = $3, price_local = $4, price_usd = $5,
          contact_email = $6, contact_phone = $7, contact_whatsapp = $8,
          preferred_contact_method = $9, category_specific_data = $10,
          image_url = $11, additional_images = $12, rate_type = $13, rate_amount = $14, 
          rate_currency = $15, selected_thumbnail_url = $16, updated_at = NOW()
      WHERE id = $17
      RETURNING id, title, image_url, additional_images, rate_type, rate_amount, rate_currency
    `, [
      data.title,
      data.description,
      mainPrice,
      priceLocal,
      priceUsd,
      data.contact_email || null,
      data.contact_phone || null,
      data.contact_whatsapp || null,
      data.preferred_contact_method || 'email',
      data.category_specific_data ? JSON.stringify(data.category_specific_data) : null,
      imageUrl,
      additionalImages ? JSON.stringify(additionalImages) : null,
      data.rate_type || null,
      data.rate_amount || null,
      data.rate_currency ? data.rate_currency.substring(0, 10) : null,
      data.selected_thumbnail_url || null,
      data.id
    ]);

    if (result.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Post not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({
      success: true,
      post: result.rows[0]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error updating classified post:', error);

    // Check if it's a column doesn't exist error
    if (error.message && error.message.includes('additional_images')) {

      // Fallback to single image update
      try {
        const fallbackResult = await query(`
          UPDATE classified_posts
          SET title = $1, description = $2, price = $3, price_local = $4, price_usd = $5,
              contact_email = $6, contact_phone = $7, contact_whatsapp = $8,
              preferred_contact_method = $9, category_specific_data = $10,
              image_url = $11, updated_at = NOW()
          WHERE id = $12
          RETURNING id, title, image_url
        `, [
          data.title,
          data.description,
          mainPrice,
          priceLocal,
          priceUsd,
          data.contact_email || null,
          data.contact_phone || null,
          data.contact_whatsapp || null,
          data.preferred_contact_method || 'email',
          data.category_specific_data ? JSON.stringify(data.category_specific_data) : null,
          imageUrl,
          data.id
        ]);

        return new Response(JSON.stringify({
          success: true,
          post: fallbackResult.rows[0],
          message: 'Updated with single image support'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      } catch (fallbackError) {
        console.error('Fallback update also failed:', fallbackError);
        return new Response(JSON.stringify({
          error: 'Failed to update classified post'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    return new Response(JSON.stringify({
      error: 'Internal server error: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};