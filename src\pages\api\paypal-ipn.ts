import type { APIRoute } from 'astro';
import { query } from '../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const formData = await request.formData();
    const ipnData: Record<string, string> = {};
    
    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
      ipnData[key] = value.toString();
    }

    console.log('PayPal IPN received:', ipnData);

    // Verify the IPN with PayPal (in production, you should verify this)
    // For now, we'll process if payment_status is 'Completed'
    
    if (ipnData.payment_status === 'Completed' && 
        ipnData.receiver_email === '<EMAIL>' &&
        parseFloat(ipnData.mc_gross) === 49.00 &&
        ipnData.mc_currency === 'USD') {
      
      const userId = ipnData.custom; // User ID passed in custom field
      const transactionId = ipnData.txn_id;
      const payerEmail = ipnData.payer_email;
      const amount = parseFloat(ipnData.mc_gross);

      // Insert verification payment record
      const insertPaymentQuery = `
        INSERT INTO verification_payments (
          user_id,
          transaction_id,
          payer_email,
          amount,
          currency,
          payment_status,
          payment_date,
          ipn_data,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
        ON CONFLICT (transaction_id) DO NOTHING
      `;

      await query(insertPaymentQuery, [
        userId,
        transactionId,
        payerEmail,
        amount,
        'USD',
        'completed',
        new Date(ipnData.payment_date),
        JSON.stringify(ipnData)
      ]);

      // Create admin notification for verification request
      const adminNotificationQuery = `
        INSERT INTO admin_notifications (
          type,
          title,
          message,
          data,
          created_at
        ) VALUES ($1, $2, $3, $4, NOW())
      `;

      const notificationData = {
        userId,
        transactionId,
        payerEmail,
        amount,
        type: 'verification_payment'
      };

      await query(adminNotificationQuery, [
        'verification_payment',
        'New Business Verification Payment',
        `User ${payerEmail} has paid $${amount} USD for business verification. Transaction ID: ${transactionId}`,
        JSON.stringify(notificationData)
      ]);

      console.log('Verification payment processed successfully:', transactionId);
    }

    // Always return 200 to acknowledge receipt
    return new Response('OK', {
      status: 200,
      headers: { 'Content-Type': 'text/plain' }
    });

  } catch (error) {
    console.error('PayPal IPN processing error:', error);
    
    // Still return 200 to avoid PayPal retries
    return new Response('Error logged', {
      status: 200,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
};
