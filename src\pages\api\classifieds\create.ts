import type { APIRoute } from 'astro';
import { query, updateCityClassifiedCounts } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Validate required fields
    const requiredFields = ['city_slug', 'category', 'title', 'description', 'user_id'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(JSON.stringify({
          error: `Missing required field: ${field}`
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Validate at least one contact method
    if (!data.contact_email && !data.contact_phone && !data.contact_whatsapp) {
      return new Response(JSON.stringify({
        error: 'At least one contact method is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Handle dual currency pricing
    const priceLocal = data.price_local ? parseFloat(data.price_local) : null;
    const priceUsd = data.price_usd ? parseFloat(data.price_usd) : null;
    const mainPrice = priceUsd || priceLocal || data.price || null;

    // Insert classified post using direct database connection (no expiry date)
    const insertResult = await query(`
      INSERT INTO classified_posts (
        city_slug, category, title, description, price, price_local, price_usd,
        contact_email, contact_phone, contact_whatsapp, preferred_contact_method,
        category_specific_data, image_url, additional_images, user_id, is_active, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
        NOW(), NOW()
      ) RETURNING id, title, created_at
    `, [
      data.city_slug,
      data.category,
      data.title,
      data.description,
      mainPrice,
      priceLocal,
      priceUsd,
      data.contact_email || null,
      data.contact_phone || null,
      data.contact_whatsapp || null,
      data.preferred_contact_method || 'email',
      data.category_specific_data ? JSON.stringify(data.category_specific_data) : null,
      data.image_url || null,
      data.additional_images ? JSON.stringify(data.additional_images) : null,
      data.user_id,
      true
    ]);

    if (insertResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Failed to create classified post'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const newPost = insertResult.rows[0];

    // Update user's total posts count
    await query(`
      UPDATE user_profiles 
      SET total_posts = total_posts + 1, updated_at = NOW()
      WHERE id = $1
    `, [data.user_id]);

    // Update city classified counts
    await updateCityClassifiedCounts(data.city_slug);

    // Invalidate cache to ensure real-time updates
    const { invalidateCache } = await import('../../../lib/cache');
    invalidateCache.onClassifiedChange();

    return new Response(JSON.stringify({
      success: true,
      post: {
        id: newPost.id,
        title: newPost.title,
        created_at: newPost.created_at
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error creating classified post:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
