import type { APIRoute } from 'astro';
import { updateListingStatus } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId } = await request.json();
    console.log('Reject listing request - Listing ID:', listingId);

    if (!listingId) {
      return new Response(JSON.stringify({ error: 'Missing listing ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update listing status to rejected
    const { error } = await updateListingStatus(listingId, 'rejected');

    if (error) {
      console.error('Error rejecting listing:', error);
      return new Response(JSON.stringify({ error: 'Failed to reject listing' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
