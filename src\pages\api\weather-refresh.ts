// API endpoint to manually refresh weather data
// GET /api/weather-refresh

import type { APIRoute } from 'astro';
import { getWeatherForCities } from '../../lib/weather';

export const GET: APIRoute = async ({ request }) => {
  try {
    // Get weather for all Mexican cities
    const cityNames = ['Cancún', 'Playa del Carmen', 'Tulum', 'Puerto Morelos', 'Isla Mujeres'];
    
    const weatherData = await getWeatherForCities(cityNames);
    
    // Format the response to show key info
    const formattedData = Object.entries(weatherData).reduce((acc, [city, weather]) => {
      if (weather) {
        acc[city] = {
          temperature: `${weather.temperature}°C`,
          condition: weather.description,
          uvIndex: `${weather.uvIndex} (${weather.uvLevel})`,
          airQuality: `${weather.airQuality} (${weather.airQualityLevel})`
        };
      }
      return acc;
    }, {} as Record<string, any>);

    return new Response(JSON.stringify({
      success: true,
      message: 'Weather data refreshed successfully',
      data: formattedData,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Weather refresh error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to refresh weather data',
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};