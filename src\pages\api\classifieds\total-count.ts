import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async () => {
  try {
    // Get total count of active classifieds across all cities
    const result = await query(`
      SELECT COUNT(*) as total
      FROM classified_posts
      WHERE is_active = true
      AND (expires_at IS NULL OR expires_at > NOW())
    `);

    const total = parseInt(result.rows[0]?.total || 0);

    return new Response(JSON.stringify({
      total,
      success: true
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=120' // Cache for 2 minutes (same as business count)
      }
    });

  } catch (error) {
    console.error('Error fetching classifieds count:', error);
    
    return new Response(JSON.stringify({ 
      total: 0,
      success: false,
      error: 'Failed to fetch classifieds count'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
