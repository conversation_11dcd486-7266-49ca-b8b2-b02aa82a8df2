import type { APIRoute } from 'astro';
import { createClient } from '@supabase/supabase-js';

// Create a dedicated client for server-side storage operations
const supabaseUrl = 'https://ltpeowkkfassadoerorm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0cGVvd2trZmFzc2Fkb2Vyb3JtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MzI4MDIsImV4cCI6MjA2MTEwODgwMn0.nuCs5-P6ui4yUSrgerv2K9o3i4JKO4s7o3KC6TEtDdM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const POST: APIRoute = async ({ request }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const userId = formData.get('userId') as string;

    if (!file || !userId) {
      return new Response(JSON.stringify({
        error: 'Please select an image to upload and make sure you\'re logged in.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return new Response(JSON.stringify({
        error: 'Oops! We only accept JPEG, PNG, and WebP images. Please try a different image format.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file size (max 2MB for better performance)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      return new Response(JSON.stringify({
        error: 'Image too large! Please use images under 2MB for faster loading. Try compressing your image or use a smaller size.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate image dimensions (optional but recommended)
    const imageBuffer = await file.arrayBuffer();

    // Basic image validation - check if it's actually an image
    const uint8Array = new Uint8Array(imageBuffer);
    const isValidImage = (
      // JPEG
      (uint8Array[0] === 0xFF && uint8Array[1] === 0xD8) ||
      // PNG
      (uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47) ||
      // WebP
      (uint8Array[8] === 0x57 && uint8Array[9] === 0x45 && uint8Array[10] === 0x42 && uint8Array[11] === 0x50)
    );

    if (!isValidImage) {
      return new Response(JSON.stringify({
        error: 'Invalid image file! Please upload a valid JPEG, PNG, or WebP image.',
        friendlyError: true
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Convert File to ArrayBuffer for Supabase
    const fileBuffer = await file.arrayBuffer();

    // Generate unique filename (simplified for now)
    const fileExt = file.name.split('.').pop() || 'jpg';
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('classified-images')
      .upload(fileName, fileBuffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Storage upload error:', error);
      return new Response(JSON.stringify({
        error: `Failed to upload image: ${error.message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('classified-images')
      .getPublicUrl(fileName);

    return new Response(JSON.stringify({
      success: true,
      imageUrl: publicUrl,
      fileName: fileName,
      path: data.path
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Upload error:', error);
    return new Response(JSON.stringify({
      error: `Internal server error: ${error.message}`
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
