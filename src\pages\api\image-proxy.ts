import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  try {
    const imageUrl = url.searchParams.get('url');
    
    if (!imageUrl) {
      return new Response('Missing image URL parameter', { status: 400 });
    }

    // Validate that it's a Google image URL for security
    const allowedDomains = [
      'lh3.googleusercontent.com',
      'lh4.googleusercontent.com',
      'lh5.googleusercontent.com',
      'lh6.googleusercontent.com',
      'streetviewpixels-pa.googleapis.com',
      'maps.googleapis.com',
      'maps.gstatic.com'
    ];

    const urlObj = new URL(imageUrl);
    if (!allowedDomains.includes(urlObj.hostname)) {
      return new Response('Invalid image domain', { status: 403 });
    }

    // Try different approaches for different URL types
    let response;

    if (imageUrl.includes('gps-proxy') || imageUrl.includes('gps-cs-s')) {
      // For gps-proxy and gps-cs-s URLs, try with more specific headers
      response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Referer': 'https://maps.google.com/',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Sec-Fetch-Dest': 'image',
          'Sec-Fetch-Mode': 'no-cors',
          'Sec-Fetch-Site': 'cross-site'
        }
      });
    } else if (imageUrl.includes('streetviewpixels')) {
      // For street view URLs
      response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Referer': 'https://maps.google.com/',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
        }
      });
    } else {
      // For regular /p/ URLs
      response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Referer': 'https://www.google.com/',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
        }
      });
    }

    if (!response.ok) {
      console.error(`Failed to fetch image: ${response.status} ${response.statusText} for URL: ${imageUrl}`);
      return new Response(`Failed to fetch image: ${response.status}`, { status: response.status });
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // Return the image with proper headers
    return new Response(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    });

  } catch (error) {
    console.error('Image proxy error:', error);
    return new Response('Internal server error', { status: 500 });
  }
};
