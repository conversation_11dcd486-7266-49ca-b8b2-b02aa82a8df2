import type { APIRoute } from 'astro';
import { getListingById, updateThumbsCount } from '../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId, direction } = await request.json();

    if (!listingId || !direction || !['up', 'down'].includes(direction)) {
      return new Response(JSON.stringify({ error: 'Invalid request' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if listing exists
    const { data: listing, error: fetchError } = await getListingById(listingId);

    if (fetchError || !listing) {
      return new Response(JSON.stringify({ error: 'Listing not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update the count
    const { data: updatedCounts, error: updateError } = await updateThumbsCount(listingId, direction);

    if (updateError) {
      console.error('Error updating vote:', updateError);
      return new Response(JSON.stringify({ error: 'Failed to update vote' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({
      success: true,
      thumbsUp: updatedCounts.thumbs_up_count,
      thumbsDown: updatedCounts.thumbs_down_count
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
