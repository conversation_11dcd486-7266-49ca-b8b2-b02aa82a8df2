---
import AdminLayout from '../../layouts/AdminLayout.astro';
import { query } from '../../lib/database';

export const prerender = false;

// No server-side auth - handled client-side for cross-environment compatibility

// Get support requests
const supportRequestsResult = await query(`
  SELECT 
    sr.*,
    u.email as user_email_auth
  FROM support_requests sr
  LEFT JOIN auth.users u ON sr.user_id = u.id
  ORDER BY sr.created_at DESC
`);
const supportRequests = supportRequestsResult.rows;

// Get stats
const statsResult = await query(`
  SELECT 
    COUNT(*) as total,
    COUNT(CASE WHEN status = 'open' THEN 1 END) as open,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved,
    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed
  FROM support_requests
`);
const stats = statsResult.rows[0];
---

<AdminLayout title="Support Requests">
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Support Requests</h1>
        <p class="text-gray-600">Manage user support tickets and feedback</p>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="text-2xl font-bold text-gray-900">{stats.total}</div>
        <div class="text-sm text-gray-600">Total Requests</div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="text-2xl font-bold text-red-600">{stats.open}</div>
        <div class="text-sm text-gray-600">Open</div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="text-2xl font-bold text-yellow-600">{stats.in_progress}</div>
        <div class="text-sm text-gray-600">In Progress</div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="text-2xl font-bold text-green-600">{stats.resolved}</div>
        <div class="text-sm text-gray-600">Resolved</div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="text-2xl font-bold text-gray-600">{stats.closed}</div>
        <div class="text-sm text-gray-600">Closed</div>
      </div>
    </div>

    <!-- Support Requests Table -->
    <div class="bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">All Support Requests</h2>
      </div>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {supportRequests.map((request) => (
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div>
                    <div class="font-medium text-gray-900">{request.subject}</div>
                    <div class="text-sm text-gray-500 truncate max-w-xs">{request.message}</div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{request.user_email || request.user_email_auth || 'Anonymous'}</div>
                </td>
                <td class="px-6 py-4">
                  <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    {request.type.replace('_', ' ')}
                  </span>
                </td>
                <td class="px-6 py-4">
                  <span class={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    request.status === 'open' ? 'bg-red-100 text-red-800' :
                    request.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                    request.status === 'resolved' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {request.status.replace('_', ' ')}
                  </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-500">
                  {new Date(request.created_at).toLocaleDateString()}
                </td>
                <td class="px-6 py-4">
                  <div class="flex space-x-2">
                    <button
                      onclick={`viewRequest('${request.id}', '${request.subject}', '${request.message}', '${request.admin_response || ''}', '${request.status}')`}
                      class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      View
                    </button>
                    <button
                      onclick={`respondToRequest('${request.id}', '${request.subject}')`}
                      class="text-emerald-600 hover:text-emerald-800 text-sm font-medium"
                    >
                      Respond
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {supportRequests.length === 0 && (
          <div class="text-center py-8 text-gray-500">
            <div class="text-4xl mb-4">📧</div>
            <p>No support requests yet</p>
          </div>
        )}
      </div>
    </div>
  </div>

  <!-- Support Request Modal -->
  <div id="support-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900" id="modal-title">Support Request</h3>
          <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div id="modal-content" class="space-y-4">
          <!-- Content will be populated by JavaScript -->
        </div>

        <div id="response-form" class="hidden mt-6 border-t border-gray-200 pt-6">
          <h4 class="font-medium text-gray-900 mb-3">Admin Response</h4>
          <textarea
            id="admin-response"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Type your response to the user..."
          ></textarea>
          <div class="flex items-center space-x-3 mt-4">
            <select id="response-status" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
            <button onclick="submitResponse()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
              Send Response
            </button>
            <button onclick="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script is:inline>
    // Admin verification
    document.addEventListener('DOMContentLoaded', async () => {
      let attempts = 0;
      while (!window.authFunctions && attempts < 30) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (!window.authFunctions) {
        window.location.href = '/account?error=admin_access_required';
        return;
      }

      const currentUser = await window.authFunctions.getCurrentUser();
      if (!currentUser || currentUser.email !== '<EMAIL>') {
        window.location.href = '/account?error=admin_access_required';
        return;
      }
    });

    let currentRequestId = null;

    window.viewRequest = function(requestId, subject, message, adminResponse, status) {
      currentRequestId = requestId;
      document.getElementById('modal-title').textContent = subject;

      const content = document.getElementById('modal-content');
      content.innerHTML = `
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h5 class="font-medium text-blue-900 mb-2">User Message:</h5>
          <p class="text-blue-800">${message}</p>
        </div>
        ${adminResponse ? `
          <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
            <h5 class="font-medium text-emerald-900 mb-2">Your Response:</h5>
            <p class="text-emerald-800">${adminResponse}</p>
          </div>
        ` : '<p class="text-gray-600 italic">No admin response yet</p>'}
        <div class="text-sm text-gray-600">
          <strong>Status:</strong> <span class="capitalize">${status.replace('_', ' ')}</span>
        </div>
      `;

      document.getElementById('support-modal').classList.remove('hidden');
    };

    window.respondToRequest = function(requestId, subject) {
      currentRequestId = requestId;
      document.getElementById('modal-title').textContent = `Respond to: ${subject}`;
      document.getElementById('modal-content').innerHTML = '';
      document.getElementById('response-form').classList.remove('hidden');
      document.getElementById('support-modal').classList.remove('hidden');
    };

    window.closeModal = function() {
      document.getElementById('support-modal').classList.add('hidden');
      document.getElementById('response-form').classList.add('hidden');
      document.getElementById('admin-response').value = '';
      currentRequestId = null;
    };

    window.submitResponse = async function() {
      if (!currentRequestId) return;

      const response = document.getElementById('admin-response').value;
      const status = document.getElementById('response-status').value;

      if (!response.trim()) {
        alert('Please enter a response');
        return;
      }

      try {
        const result = await fetch('/api/admin/support-response', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            requestId: currentRequestId,
            response: response,
            status: status
          })
        });

        if (result.ok) {
          alert('Response sent successfully');
          window.location.reload();
        } else {
          alert('Failed to send response');
        }
      } catch (error) {
        alert('Error sending response');
      }
    };
  </script>
</AdminLayout>
