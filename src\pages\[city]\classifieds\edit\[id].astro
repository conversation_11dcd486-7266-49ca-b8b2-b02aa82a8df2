---
import Layout from '../../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../../components/MinimalAcctButton.astro';
import { getCityBySlug, query } from '../../../../lib/database';
import { getCurrencyForCountry } from '../../../../lib/currencies';

const { city: citySlug, id: postId } = Astro.params;

if (!citySlug || !postId) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);
if (cityError || !city) {
  return Astro.redirect('/');
}

// Get classified post data
let classifiedPost = null;
try {
  const result = await query(`SELECT * FROM classified_posts WHERE id = $1 AND city_slug = $2`, [postId, citySlug]);
  classifiedPost = result.rows[0] || null;
} catch (error) {
  console.error('Error fetching classified post:', error);
}

if (!classifiedPost) {
  return Astro.redirect(`/${citySlug}/classifieds`);
}

// Get local currency for the city's country
const localCurrency = getCurrencyForCountry(city.country || 'Mexico');

const pageTitle = `Edit Classified Ad - ${city.name}`;
---

<Layout title={pageTitle}>
  <MinimalAcctButton />

  <!-- Beautiful UX Animations -->
  <style>
    @keyframes bounce-in {
      0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.3);
      }
      50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
      }
      70% {
        transform: translate(-50%, -50%) scale(0.9);
      }
      100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }

    .animate-bounce-in {
      animation: bounce-in 0.6s ease-out;
    }
  </style>

  <main class="min-h-screen bg-slate-50">
    <!-- Header with Breadcrumb -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <nav class="flex items-center space-x-2 text-sm text-slate-600 mb-4">
          <a href="/" class="hover:text-blue-600 transition-colors">🌎 Main</a>
          <span class="text-slate-400">›</span>
          <a href={`/${citySlug}`} class="hover:text-blue-600 transition-colors">{city.name}</a>
          <span class="text-slate-400">›</span>
          <a href="/account" class="hover:text-blue-600 transition-colors">Account</a>
          <span class="text-slate-400">›</span>
          <span class="text-slate-900 font-medium">Edit Ad</span>
        </nav>

        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <span class="text-2xl">✏️</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-slate-900">Edit Classified Ad</h1>
            <p class="text-slate-600">Update your listing details</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="bg-white border border-slate-200 rounded-xl overflow-hidden">
          <div class="p-4 sm:p-6 lg:p-8">
            <!-- Category-Specific Forms -->
            <div id="classified-forms" style="display: block;">

              <!-- Housing Form -->
              <form id="housing-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">🏠</span>
                  <h2 class="text-lg font-semibold text-slate-900">Housing Details</h2>
                </div>

                <!-- Listing Type -->
                <div>
                  <label for="listing_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Listing Type *
                  </label>
                  <select
                    id="listing_type"
                    name="listing_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select listing type</option>
                    <option value="rent">🏠 For Rent</option>
                    <option value="sale">🏡 For Sale</option>
                    <option value="roommate">👥 Roommate Wanted</option>
                  </select>
                </div>

                <!-- Housing Type -->
                <div>
                  <label for="housing_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Property Type *
                  </label>
                  <select
                    id="housing_type"
                    name="housing_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select property type</option>
                    <option value="apartment">Apartment</option>
                    <option value="house">House</option>
                    <option value="room">Room</option>
                    <option value="studio">Studio</option>
                    <option value="condo">Condo</option>
                    <option value="villa">Villa</option>
                  </select>
                </div>

                <!-- Title -->
                <div>
                  <label for="housing_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    id="housing_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., Beautiful 2BR apartment near beach"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="housing_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    id="housing_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the property, amenities, location, etc..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Housing Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label for="bedrooms" class="block text-sm font-medium text-slate-700 mb-2">
                      Bedrooms
                    </label>
                    <select
                      id="bedrooms"
                      name="bedrooms"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="0">Studio</option>
                      <option value="1">1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4+</option>
                    </select>
                  </div>
                  <div>
                    <label for="bathrooms" class="block text-sm font-medium text-slate-700 mb-2">
                      Bathrooms
                    </label>
                    <select
                      id="bathrooms"
                      name="bathrooms"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="1">1</option>
                      <option value="1.5">1.5</option>
                      <option value="2">2</option>
                      <option value="2.5">2.5</option>
                      <option value="3">3+</option>
                    </select>
                  </div>
                  <div>
                    <label for="furnished" class="block text-sm font-medium text-slate-700 mb-2">
                      Furnished
                    </label>
                    <select
                      id="furnished"
                      name="furnished"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="yes">Yes</option>
                      <option value="no">No</option>
                      <option value="partial">Partially</option>
                    </select>
                  </div>
                </div>

                <!-- Dual Currency Price -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    Price *
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="housing_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="housing_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-500 mt-2">Enter price in either currency (at least one required)</p>
                </div>

                <!-- Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span class="text-xl">📸</span>
                      <h3 class="text-lg font-semibold text-slate-900">Photos</h3>
                    </div>
                  </div>

                  <!-- Current Images Display -->
                  <div id="housing_current_images" class="mb-4" style="display: none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
                      <div class="flex items-center space-x-2 mb-2">
                        <span class="text-blue-600">ℹ️</span>
                        <p class="text-sm font-medium text-blue-900">Current Photos</p>
                      </div>
                      <p class="text-xs text-blue-700">Your existing photos are shown below. You can remove individual photos or add new ones (up to 3 total).</p>
                    </div>
                    <div id="housing_existing_images" class="grid grid-cols-3 gap-4">
                      <!-- Existing images will be displayed here -->
                    </div>
                  </div>

                  <!-- Upload New Images -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input
                      type="file"
                      id="housing_image_upload"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      multiple
                      class="hidden"
                    />
                    <label for="housing_image_upload" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Add photos</div>
                        <div class="text-xs text-slate-500">JPEG, PNG, or WebP (max 2MB each)</div>
                        <div class="text-xs text-slate-400 mt-2">Up to 3 photos total</div>
                      </div>
                    </label>
                  </div>

                  <!-- New Image Previews -->
                  <div id="housing_image_previews" class="grid grid-cols-3 gap-4 mt-4 hidden">
                    <!-- New uploaded images will be previewed here -->
                  </div>

                  <!-- Upload Status -->
                  <div id="housing_upload_status" class="hidden mt-3 p-3 rounded-lg">
                    <div class="flex items-center space-x-2">
                      <div class="w-4 h-4 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
                      <span class="text-sm text-emerald-700">Uploading images...</span>
                    </div>
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method</h3>

                    <!-- Contact Methods Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="housing_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            required
                          />
                          <label for="housing_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="housing_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="housing_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="housing_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="housing_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="housing_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="housing_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="housing_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="housing_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="housing_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="housing_contact_whatsapp"
                          name="contact_whatsapp"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a href="/account" class="text-slate-600 hover:text-slate-800 font-medium">← Back to Account</a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold">
                      💾 Update Ad
                    </button>
                  </div>
                </div>
              </form>

              <!-- Jobs Form -->
              <form id="jobs-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">💼</span>
                  <h2 class="text-lg font-semibold text-slate-900">Job Details</h2>
                </div>

                <!-- Job Listing Type -->
                <div>
                  <label for="job_listing_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Listing Type *
                  </label>
                  <select
                    id="job_listing_type"
                    name="job_listing_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select listing type</option>
                    <option value="job_offered">💼 Job Offered (Hiring)</option>
                    <option value="looking_for_work">🔍 Looking for Work</option>
                  </select>
                </div>

                <!-- Job Type -->
                <div>
                  <label for="job_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Job Type
                  </label>
                  <select
                    id="job_type"
                    name="job_type"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select job type</option>
                    <option value="full-time">Full-time</option>
                    <option value="part-time">Part-time</option>
                    <option value="freelance">Freelance</option>
                    <option value="contract">Contract</option>
                    <option value="internship">Internship</option>
                    <option value="service">Service/Consulting</option>
                  </select>
                </div>

                <!-- Title -->
                <div>
                  <label for="job_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Job Title *
                  </label>
                  <input
                    type="text"
                    id="job_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., English Teacher, Web Developer, Cleaning Service"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="job_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Job Description *
                  </label>
                  <textarea
                    id="job_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the job requirements, responsibilities, qualifications..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Job Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="experience_level" class="block text-sm font-medium text-slate-700 mb-2">
                      Experience Level
                    </label>
                    <select
                      id="experience_level"
                      name="experience_level"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="entry">Entry Level</option>
                      <option value="mid">Mid Level</option>
                      <option value="senior">Senior Level</option>
                      <option value="any">Any Level</option>
                    </select>
                  </div>
                  <div>
                    <label for="remote_ok" class="block text-sm font-medium text-slate-700 mb-2">
                      Remote Work
                    </label>
                    <select
                      id="remote_ok"
                      name="remote_ok"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select</option>
                      <option value="yes">Remote OK</option>
                      <option value="no">On-site Only</option>
                      <option value="hybrid">Hybrid</option>
                    </select>
                  </div>
                </div>

                <!-- Dual Currency Salary -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    Salary/Rate (Optional)
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="job_salary_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="job_salary_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-500 mt-2">Enter salary in either currency (optional)</p>
                </div>

                <!-- Job Category -->
                <div>
                  <label for="job_category" class="block text-sm font-medium text-slate-700 mb-2">
                    Job Category
                  </label>
                  <select
                    id="job_category"
                    name="job_category"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select category</option>
                    <option value="technology">Technology</option>
                    <option value="education">Education</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="hospitality">Hospitality</option>
                    <option value="construction">Construction</option>
                    <option value="sales">Sales</option>
                    <option value="marketing">Marketing</option>
                    <option value="finance">Finance</option>
                    <option value="legal">Legal</option>
                    <option value="creative">Creative</option>
                    <option value="administrative">Administrative</option>
                    <option value="customer-service">Customer Service</option>
                    <option value="transportation">Transportation</option>
                    <option value="cleaning">Cleaning</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <!-- Rate Information -->
                <div>
                  <label for="rate_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Rate Type
                  </label>
                  <select
                    id="rate_type"
                    name="rate_type"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    onchange="toggleRateFields()"
                  >
                    <option value="">Select rate type</option>
                    <option value="hourly">💰 Hourly</option>
                    <option value="daily">📅 Daily</option>
                    <option value="weekly">📆 Weekly</option>
                    <option value="monthly">🗓️ Monthly</option>
                    <option value="project">📋 Project</option>
                    <option value="negotiable">🤝 Negotiable</option>
                  </select>
                </div>

                <!-- Rate Amount (shown when rate type is selected) -->
                <div id="rate_amount_section" style="display: none;">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency Rate -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        Rate in {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="rate_amount_local"
                          name="price_local"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD Rate -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        Rate in US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="rate_amount_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- CV/Portfolio Upload -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-2">
                    CV / Portfolio (Optional)
                  </label>
                  
                  <!-- Current CV Display -->
                  <div id="current_cv_display" style="display: none;" class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <span class="text-xl">📄</span>
                        <div>
                          <div class="font-medium text-slate-900" id="current_cv_name"></div>
                          <div class="text-sm text-slate-500" id="current_cv_size"></div>
                        </div>
                      </div>
                      <button type="button" onclick="removeCurrentCV()" class="text-red-600 hover:text-red-800 text-sm font-medium">
                        Remove
                      </button>
                    </div>
                  </div>

                  <!-- CV Upload Input -->
                  <input
                    type="file"
                    id="cv_upload"
                    name="cv_upload"
                    accept=".pdf,.doc,.docx"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                  <p class="text-xs text-slate-500 mt-1">Upload PDF, DOC, or DOCX files (max 5MB)</p>
                  
                  <!-- CV Preview -->
                  <div id="cv_preview" class="hidden mt-3"></div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method</h3>

                    <!-- Contact Methods Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="jobs_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            required
                          />
                          <label for="jobs_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="jobs_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="jobs_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="jobs_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="jobs_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="jobs_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="jobs_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="jobs_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="jobs_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="jobs_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="jobs_contact_whatsapp"
                          name="contact_whatsapp"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a href="/account" class="text-slate-600 hover:text-slate-800 font-medium">← Back to Account</a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold">
                      💾 Update Ad
                    </button>
                  </div>
                </div>
              </form>



              <!-- Buy & Sell Form -->
              <form id="buy-sell-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">🛍️</span>
                  <h2 class="text-lg font-semibold text-slate-900">Buy & Sell Details</h2>
                </div>

                <!-- Listing Type -->
                <div>
                  <label for="buy_sell_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Listing Type *
                  </label>
                  <select
                    id="buy_sell_type"
                    name="buy_sell_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select listing type</option>
                    <option value="sell">💰 For Sale</option>
                    <option value="buy">🔍 Want to Buy</option>
                  </select>
                </div>

                <!-- Item Category -->
                <div>
                  <label for="item_category" class="block text-sm font-medium text-slate-700 mb-2">
                    Item Category *
                  </label>
                  <select
                    id="item_category"
                    name="item_category"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">Select category</option>
                    <option value="furniture">Furniture</option>
                    <option value="electronics">Electronics</option>
                    <option value="vehicles">Vehicles</option>
                    <option value="appliances">Appliances</option>
                    <option value="clothing">Clothing</option>
                    <option value="books">Books</option>
                    <option value="sports">Sports & Recreation</option>
                    <option value="tools">Tools</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <!-- Title -->
                <div>
                  <label for="buy-sell_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Item Title *
                  </label>
                  <input
                    type="text"
                    id="buy-sell_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., iPhone 13, Dining Table, Mountain Bike"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="buy-sell_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Item Description *
                  </label>
                  <textarea
                    id="buy-sell_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the item, condition, features, etc..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Item Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="condition" class="block text-sm font-medium text-slate-700 mb-2">
                      Condition
                    </label>
                    <select
                      id="condition"
                      name="condition"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">Select condition</option>
                      <option value="new">New</option>
                      <option value="like-new">Like New</option>
                      <option value="good">Good</option>
                      <option value="fair">Fair</option>
                      <option value="poor">Poor</option>
                    </select>
                  </div>
                  <div>
                    <label for="brand" class="block text-sm font-medium text-slate-700 mb-2">
                      Brand (Optional)
                    </label>
                    <input
                      type="text"
                      id="brand"
                      name="brand"
                      placeholder="e.g., Apple, IKEA, Toyota"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                </div>

                <!-- Dual Currency Price -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    Price *
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="buy_sell_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="buy_sell_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-500 mt-2">Enter price in either currency (at least one required)</p>
                </div>

                <!-- Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center space-x-3 mb-4">
                    <span class="text-xl">📸</span>
                    <h3 class="text-lg font-semibold text-slate-900">Photos</h3>
                  </div>

                  <!-- Current Image Display -->
                  <div id="buy_sell_current_image" class="mb-4" style="display: none;">
                    <p class="text-sm font-medium text-slate-700 mb-2">Current Image:</p>
                    <div class="relative inline-block">
                      <img id="buy_sell_current_img" src="" alt="Current image" class="w-32 h-24 object-cover rounded-lg border-2 border-slate-200">
                      <button type="button" onclick="removeCurrentImage('buy_sell')" class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs">×</button>
                    </div>
                  </div>

                  <!-- Upload New Image -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input type="file" id="buy_sell_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" class="hidden" />
                    <label for="buy_sell_image_upload" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Click to upload new photo</div>
                        <div class="text-xs text-slate-500">JPEG, PNG, or WebP (max 2MB)</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method</h3>

                    <!-- Contact Methods Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="buy-sell_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            required
                          />
                          <label for="buy-sell_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="buy-sell_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="buy-sell_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="buy-sell_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="buy-sell_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="buy-sell_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="buy-sell_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="buy-sell_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="buy-sell_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="buy-sell_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="buy-sell_contact_whatsapp"
                          name="contact_whatsapp"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a href="/account" class="text-slate-600 hover:text-slate-800 font-medium">← Back to Account</a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold">
                      💾 Update Ad
                    </button>
                  </div>
                </div>
              </form>

              <!-- Community Form -->
              <form id="community-form" class="space-y-6" style="display: none;">
                <div class="flex items-center space-x-3 pb-4 border-b border-slate-200">
                  <span class="text-xl">🤝</span>
                  <h2 class="text-lg font-semibold text-slate-900">Community Details</h2>
                </div>

                <!-- Event Type -->
                <div>
                  <label for="event_type" class="block text-sm font-medium text-slate-700 mb-2">
                    Event Type *
                  </label>
                  <select
                    id="event_type"
                    name="event_type"
                    required
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    onchange="handleEventTypeChange(this.value)"
                  >
                    <option value="">Select event type</option>
                    <option value="social">🎉 Social Event - Parties, gatherings, celebrations</option>
                    <option value="meetup">👥 Meetup - Casual get-togethers, networking</option>
                    <option value="class">📚 Class/Workshop - Learning, skills, education</option>
                    <option value="announcement">📢 Announcement - Important community news</option>
                    <option value="useful-links">🔗 Useful Links - Facebook groups, Telegram, WhatsApp groups</option>
                    <option value="other">📝 Other - Everything else</option>
                  </select>
                  <p class="text-xs text-slate-500 mt-1">Choose the category that best describes your post</p>
                </div>

                <!-- Title -->
                <div>
                  <label for="community_title" class="block text-sm font-medium text-slate-700 mb-2">
                    Event Title *
                  </label>
                  <input
                    type="text"
                    id="community_title"
                    name="title"
                    required
                    maxlength="100"
                    placeholder="e.g., Beach Cleanup, Expat Meetup, Spanish Class"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label for="community_description" class="block text-sm font-medium text-slate-700 mb-2">
                    Event Description *
                  </label>
                  <textarea
                    id="community_description"
                    name="description"
                    required
                    rows="4"
                    maxlength="2000"
                    placeholder="Describe the event, what to expect, who should attend..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  ></textarea>
                </div>

                <!-- Event Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label for="event_date" class="block text-sm font-medium text-slate-700 mb-2">
                      Event Date
                    </label>
                    <input
                      type="date"
                      id="event_date"
                      name="event_date"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                  <div>
                    <label for="event_time" class="block text-sm font-medium text-slate-700 mb-2">
                      Event Time
                    </label>
                    <input
                      type="time"
                      id="event_time"
                      name="event_time"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                </div>

                <!-- Event Location -->
                <div>
                  <label for="event_location" class="block text-sm font-medium text-slate-700 mb-2">
                    Event Location
                  </label>
                  <input
                    type="text"
                    id="event_location"
                    name="event_location"
                    placeholder="e.g., Central Park, Community Center, Online"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>

                <!-- Dual Currency Cost -->
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-3">
                    Cost (Optional)
                  </label>

                  <!-- Currency Selection -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Local Currency -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        {localCurrency.name} ({localCurrency.code})
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">{localCurrency.symbol}</span>
                        <input
                          type="number"
                          id="community_price_local"
                          name="price_local"
                          min="0"
                          step="1"
                          placeholder="0"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <!-- USD -->
                    <div>
                      <label class="block text-xs font-medium text-slate-600 mb-2">
                        US Dollars (USD)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-3 text-slate-500">$</span>
                        <input
                          type="number"
                          id="community_price_usd"
                          name="price_usd"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          class="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-500 mt-2">Enter cost in either currency (leave blank for free events)</p>
                </div>

                <!-- Image Upload -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="flex items-center space-x-3 mb-4">
                    <span class="text-xl">📸</span>
                    <h3 class="text-lg font-semibold text-slate-900">Photos</h3>
                  </div>

                  <!-- Current Image Display -->
                  <div id="community_current_image" class="mb-4" style="display: none;">
                    <p class="text-sm font-medium text-slate-700 mb-2">Current Image:</p>
                    <div class="relative inline-block">
                      <img id="community_current_img" src="" alt="Current image" class="w-32 h-24 object-cover rounded-lg border-2 border-slate-200">
                      <button type="button" onclick="removeCurrentImage('community')" class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs">×</button>
                    </div>
                  </div>

                  <!-- Upload New Image -->
                  <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-emerald-400 transition-colors">
                    <input type="file" id="community_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" class="hidden" />
                    <label for="community_image_upload" class="cursor-pointer">
                      <div class="space-y-2">
                        <div class="text-4xl text-slate-400">📷</div>
                        <div class="text-sm font-medium text-slate-700">Click to upload new photo</div>
                        <div class="text-xs text-slate-500">JPEG, PNG, or WebP (max 2MB)</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="border-t border-slate-200 pt-6">
                  <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-semibold text-slate-900">⭐ Preferred Contact Method</h3>

                    <!-- Contact Methods Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Email -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="email"
                            id="community_preferred_email"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                            required
                          />
                          <label for="community_preferred_email" class="text-sm font-medium text-slate-700 flex items-center">
                            📧 Email <span id="community_email_star" class="ml-1 text-yellow-500">⭐</span>
                          </label>
                        </div>
                        <input
                          type="email"
                          id="community_contact_email"
                          name="contact_email"
                          placeholder="<EMAIL>"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- Phone -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="phone"
                            id="community_preferred_phone"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="community_preferred_phone" class="text-sm font-medium text-slate-700 flex items-center">
                            📞 Phone <span id="community_phone_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="community_contact_phone"
                          name="contact_phone"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>

                      <!-- WhatsApp -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="preferred_contact_method"
                            value="whatsapp"
                            id="community_preferred_whatsapp"
                            class="w-4 h-4 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label for="community_preferred_whatsapp" class="text-sm font-medium text-slate-700 flex items-center">
                            💬 WhatsApp <span id="community_whatsapp_star" class="ml-1 text-yellow-500 hidden">⭐</span>
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="community_contact_whatsapp"
                          name="contact_whatsapp"
                          placeholder="+52 ************"
                          class="w-full px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="flex items-center justify-between">
                    <a href="/account" class="text-slate-600 hover:text-slate-800 font-medium">← Back to Account</a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold">
                      💾 Update Ad
                    </button>
                  </div>
                </div>
              </form>

            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ classifiedPost, citySlug, postId }}>
  // Parse category specific data
  let categoryData = {};
  try {
    if (classifiedPost.category_specific_data) {
      // Check if it's already an object or a string that needs parsing
      if (typeof classifiedPost.category_specific_data === 'string') {
        categoryData = JSON.parse(classifiedPost.category_specific_data);
      } else if (typeof classifiedPost.category_specific_data === 'object') {
        categoryData = classifiedPost.category_specific_data;
      }
    }
  } catch (e) {
    console.error('Error parsing category data:', e);
    categoryData = {};
  }
  
  console.log('Category data type:', typeof classifiedPost.category_specific_data);
  console.log('Category data value:', classifiedPost.category_specific_data);
  console.log('Parsed category data:', categoryData);

  // Show the correct form based on category
  function showCategoryForm(category) {
    // Hide all forms
    document.getElementById('housing-form').style.display = 'none';
    document.getElementById('jobs-form').style.display = 'none';
    document.getElementById('buy-sell-form').style.display = 'none';
    document.getElementById('community-form').style.display = 'none';

    // Show the correct form
    document.getElementById(category + '-form').style.display = 'block';

    // Populate form with existing data
    populateForm(category);

    // Set up image upload for this category after the form is visible
    setTimeout(() => {
      setupImageUpload(category);
    }, 100);
  }

  // Populate form with existing classified data
  function populateForm(category) {
    console.log('Populating form for category:', category);
    console.log('Classified post data:', classifiedPost);
    console.log('Category data:', categoryData);

    // Helper function to safely set field value
    function setFieldValue(fieldId, value) {
      const field = document.getElementById(fieldId);
      if (field) {
        field.value = value || '';
        console.log(`Set ${fieldId} to:`, value);
      } else {
        console.warn(`Field not found: ${fieldId}`);
      }
    }

    // Helper function to safely check radio button
    function setRadioValue(fieldId, checked) {
      const field = document.getElementById(fieldId);
      if (field) {
        field.checked = checked;
        console.log(`Set ${fieldId} checked:`, checked);
      } else {
        console.warn(`Radio field not found: ${fieldId}`);
      }
    }

    // Basic fields - handle different ID patterns
    const titleId = category === 'housing' ? 'housing_title' : 
                   category === 'jobs' ? 'jobs_title' :
                   category === 'buy-sell' ? 'buy-sell_title' : 'community_title';
    const descId = category === 'housing' ? 'housing_description' : 
                   category === 'jobs' ? 'jobs_description' :
                   category === 'buy-sell' ? 'buy-sell_description' : 'community_description';
    
    setFieldValue(titleId, classifiedPost.title);
    setFieldValue(descId, classifiedPost.description);

    // Contact fields - handle buy-sell vs buy_sell ID differences
    const categoryId = category.replace('-', '_');
    setFieldValue(categoryId + '_contact_email', classifiedPost.contact_email);
    setFieldValue(categoryId + '_contact_phone', classifiedPost.contact_phone);
    setFieldValue(categoryId + '_contact_whatsapp', classifiedPost.contact_whatsapp);

    // Set preferred contact method
    const preferredMethod = classifiedPost.preferred_contact_method || 'email';
    setRadioValue(categoryId + '_preferred_' + preferredMethod, true);

    // Show star for preferred method
    updatePreferredStar(categoryId, preferredMethod);

    // Category-specific fields with proper error handling
    if (category === 'housing') {
      // Set dual currency fields
      setFieldValue('housing_price_local', classifiedPost.price_local);
      setFieldValue('housing_price_usd', classifiedPost.price_usd);
      setFieldValue('listing_type', categoryData.listing_type);
      setFieldValue('housing_type', categoryData.housing_type);
      setFieldValue('bedrooms', categoryData.bedrooms);
      setFieldValue('bathrooms', categoryData.bathrooms);
      setFieldValue('furnished', categoryData.furnished);
    } else if (category === 'jobs') {
      // Set dual currency fields
      setFieldValue('job_salary_local', classifiedPost.price_local);
      setFieldValue('job_salary_usd', classifiedPost.price_usd);
      setFieldValue('job_listing_type', categoryData.job_listing_type);
      setFieldValue('job_type', categoryData.job_type);
      setFieldValue('experience_level', categoryData.experience_level);
      setFieldValue('remote_ok', categoryData.remote_ok);
    } else if (category === 'buy-sell') {
      // Set dual currency fields
      setFieldValue('buy_sell_price_local', classifiedPost.price_local);
      setFieldValue('buy_sell_price_usd', classifiedPost.price_usd);
      setFieldValue('buy_sell_type', categoryData.listing_type);
      setFieldValue('item_category', categoryData.item_category);
      setFieldValue('condition', categoryData.condition);
      setFieldValue('brand', categoryData.brand);
    } else if (category === 'community') {
      // Set dual currency fields
      setFieldValue('community_price_local', classifiedPost.price_local);
      setFieldValue('community_price_usd', classifiedPost.price_usd);
      setFieldValue('event_type', categoryData.event_type);
      setFieldValue('event_date', categoryData.event_date);
      setFieldValue('event_time', categoryData.event_time);
      setFieldValue('event_location', categoryData.event_location);
    }

    // Show existing images if available
    showExistingImages(category, classifiedPost);
  }

  // Function to show existing images
  function showExistingImages(category, post) {
    console.log('Showing existing images for category:', category);

    // Initialize existing images array
    if (!window[`${category}_existingImages`]) {
      window[`${category}_existingImages`] = [];
    }
    if (!window[`${category}_removedImages`]) {
      window[`${category}_removedImages`] = [];
    }

    // Collect all existing image URLs
    const existingImageUrls = [];
    if (post.image_url) {
      existingImageUrls.push(post.image_url);
    }
    if (post.additional_images && Array.isArray(post.additional_images)) {
      existingImageUrls.push(...post.additional_images);
    }

    if (existingImageUrls.length === 0) {
      console.log('No existing images found');
      return;
    }

    // Store existing images
    window[`${category}_existingImages`] = existingImageUrls;

    // Display existing images
    const currentImagesDiv = document.getElementById(`${category}_current_images`);
    const existingImagesContainer = document.getElementById(`${category}_existing_images`);

    if (currentImagesDiv && existingImagesContainer) {
      currentImagesDiv.style.display = 'block';

      existingImagesContainer.innerHTML = existingImageUrls.map((imageUrl, index) => `
        <div id="existing_${category}_${index}" class="relative group">
          <img src="${imageUrl}" alt="Current image ${index + 1}" class="w-full h-24 object-cover rounded-lg border-2 border-blue-200" />
          <button
            type="button"
            onclick="removeExistingImage('${category}', ${index}, '${imageUrl}')"
            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs transition-colors flex items-center justify-center"
            title="Remove this image"
          >
            ×
          </button>
          <div class="absolute bottom-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded">
            Current
          </div>
        </div>
      `).join('');

      console.log(`Displayed ${existingImageUrls.length} existing images`);
    }
  }

  // Function to remove existing image
  window.removeExistingImage = function(category, index, imageUrl) {
    // Add to removed images array
    if (!window[`${category}_removedImages`]) {
      window[`${category}_removedImages`] = [];
    }
    window[`${category}_removedImages`].push(imageUrl);

    // Remove from existing images array
    const existingImages = window[`${category}_existingImages`] || [];
    existingImages.splice(index, 1);

    // Remove the visual element
    const imageElement = document.getElementById(`existing_${category}_${index}`);
    if (imageElement) {
      imageElement.remove();
    }

    // Hide container if no more existing images
    const existingImagesContainer = document.getElementById(`${category}_existing_images`);
    const currentImagesDiv = document.getElementById(`${category}_current_images`);
    if (existingImagesContainer && existingImagesContainer.children.length === 0 && currentImagesDiv) {
      currentImagesDiv.style.display = 'none';
    }

    console.log(`Removed existing image: ${imageUrl}`);
    showImageUploadToast('Image marked for removal', 'success');
  }

  // Function to remove current image
  window.removeCurrentImage = function(category) {
    const currentImageDiv = document.getElementById(category + '_current_image');
    if (currentImageDiv) {
      // Hide the current image
      currentImageDiv.style.display = 'none';

      // Show removal confirmation
      const uploadArea = document.querySelector(`#${category}_image_upload`).closest('.border-dashed');
      if (uploadArea) {
        uploadArea.innerHTML = `
          <div class="space-y-3">
            <div class="text-4xl text-red-400">🗑️</div>
            <div class="text-sm font-medium text-red-600">Current photo will be removed</div>
            <div class="text-xs text-slate-500">Upload a new photo or leave empty to have no photo</div>
            <button type="button" onclick="undoImageRemoval('${category}')" class="text-xs text-blue-600 hover:text-blue-700 underline">
              Undo removal
            </button>
          </div>
        `;
      }
    }
    // Mark image for removal in form submission
    window.imageRemoved = true;

    // Show toast notification
    showImageUploadToast('Current photo marked for removal', 'success');
  }

  // Function to undo image removal
  window.undoImageRemoval = function(category) {
    // Reset removal flag
    window.imageRemoved = false;

    // Show the current image again
    const currentImageDiv = document.getElementById(category + '_current_image');
    if (currentImageDiv) {
      currentImageDiv.style.display = 'block';
    }

    // Reset upload area
    const uploadArea = document.querySelector(`#${category}_image_upload`).closest('.border-dashed');
    if (uploadArea) {
      uploadArea.innerHTML = `
        <input type="file" id="${category}_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" class="hidden" />
        <label for="${category}_image_upload" class="cursor-pointer">
          <div class="space-y-2">
            <div class="text-4xl text-slate-400">📷</div>
            <div class="text-sm font-medium text-slate-700">Upload a new photo</div>
            <div class="text-xs text-slate-500">JPEG, PNG, or WebP (max 2MB)</div>
            <div class="text-xs text-slate-400 mt-2">Optional - leave empty to keep current photo</div>
          </div>
        </label>
      `;

      // Re-attach event listener
      setTimeout(() => {
        setupImageUpload(category);
      }, 50);
    }

    // Show toast notification
    showImageUploadToast('Photo removal cancelled', 'success');
  }

  // Function to update preferred star display
  function updatePreferredStar(category, selectedMethod) {
    // Hide all stars for this category
    const stars = ['email', 'phone', 'whatsapp'];
    stars.forEach(method => {
      const star = document.getElementById(`${category}_${method}_star`);
      if (star) {
        star.classList.add('hidden');
      }
    });

    // Show star for selected method
    const selectedStar = document.getElementById(`${category}_${selectedMethod}_star`);
    if (selectedStar) {
      selectedStar.classList.remove('hidden');
    }
  }

  // Initialize the form
  showCategoryForm(classifiedPost.category);

  // Image upload functionality
  function setupImageUpload(category = null) {
    // If no category specified, set up for all categories
    const categoriesToSetup = category ? [category] : ['housing', 'jobs', 'buy-sell', 'community'];

    categoriesToSetup.forEach(cat => {
      const imageInput = document.getElementById(`${cat}_image_upload`);
      if (imageInput) {
        // Remove existing event listeners to prevent duplicates
        imageInput.removeEventListener('change', handleImageUpload);
        imageInput.addEventListener('change', handleImageUpload);
        console.log(`Image upload setup for category: ${cat}`);
      } else {
        console.log(`Image input not found for category: ${cat} (this is normal if the form is hidden)`);
      }
    });
  }

  // Initialize image upload functionality for the current category only
  setupImageUpload(classifiedPost.category);

  async function handleImageUpload(e) {
    console.log('Multi-image upload triggered!', e.target);

    const files = Array.from(e.target.files);
    console.log(`Selected ${files.length} files:`, files.map(f => f.name));

    if (files.length === 0) {
      console.log('No files selected, returning');
      return;
    }

    // Get category from input ID
    const inputId = e.target.id;
    const category = inputId.replace('_image_upload', '');
    console.log(`Upload for category: ${category}, input ID: ${inputId}`);

    // Initialize arrays if they don't exist
    if (!window[`${category}_uploadedImages`]) {
      window[`${category}_uploadedImages`] = [];
    }
    if (!window[`${category}_existingImages`]) {
      window[`${category}_existingImages`] = [];
    }

    const maxImages = 3;
    const currentCount = getCurrentImageCount(category);

    // Check if adding these files would exceed the limit
    if (currentCount + files.length > maxImages) {
      showImageError(`You can only have up to ${maxImages} images total. You currently have ${currentCount} images. Please select ${maxImages - currentCount} or fewer images.`);
      return;
    }

    // Validate each file before uploading
    for (const file of files) {
      if (!validateImageFile(file)) {
        return; // Stop if any file is invalid
      }
    }

    // Get current user
    const currentUser = window.authFunctions?.getCurrentUser();
    if (!currentUser) {
      showImageError('Please log in to upload images.');
      return;
    }

    // Show upload status
    showUploadStatus(category, `Uploading ${files.length} image${files.length > 1 ? 's' : ''}...`);

    // Upload files one by one
    for (const file of files) {
      await uploadSingleImage(category, file);
    }

    // Clear the input so the same files can be selected again if needed
    e.target.value = '';

    // Hide upload status after a delay
    setTimeout(() => hideUploadStatus(category), 2000);
  }

  function getCurrentImageCount(category) {
    const existingCount = window[`${category}_existingImages`] ? window[`${category}_existingImages`].length : 0;
    const uploadedCount = window[`${category}_uploadedImages`] ? window[`${category}_uploadedImages`].length : 0;
    const removedCount = window[`${category}_removedImages`] ? window[`${category}_removedImages`].length : 0;
    return existingCount + uploadedCount - removedCount;
  }

  async function uploadSingleImage(category, file) {
    const currentUser = window.authFunctions?.getCurrentUser();
    if (!currentUser) {
      showImageError('Please log in to upload images.');
      return;
    }

    // Create preview immediately
    const previewId = `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    createImagePreview(category, file, previewId);

    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('userId', currentUser.id);

      const response = await fetch('/api/classifieds/upload-image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Store the uploaded image
        window[`${category}_uploadedImages`].push({
          url: result.imageUrl,
          previewId: previewId,
          fileName: file.name
        });

        updateImagePreview(previewId, result.imageUrl, true);
        console.log(`Successfully uploaded: ${file.name}`);
      } else {
        // Remove the failed preview
        removeImagePreview(previewId);
        showImageError(result.error || `Failed to upload ${file.name}. Please try again.`);
      }
    } catch (error) {
      console.error('Upload error:', error);
      removeImagePreview(previewId);
      showImageError(`Failed to upload ${file.name}. Please check your connection and try again.`);
    }
  }

  function validateImageFile(file) {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      showImageError(`"${file.name}" is not a supported image format. Please use JPEG, PNG, or WebP images only.`);
      return false;
    }

    // Check file size (2MB limit)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
      showImageError(`"${file.name}" is too large (${sizeMB}MB). Please use images under 2MB. Try compressing your image or use a smaller size.`);
      return false;
    }

    return true;
  }

  function createImagePreview(category, file, previewId) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const previewsContainer = document.getElementById(`${category}_image_previews`);
      if (!previewsContainer) {
        console.error(`Preview container not found: ${category}_image_previews`);
        return;
      }

      const previewHtml = `
        <div id="${previewId}" class="relative group">
          <img src="${e.target.result}" alt="Preview" class="w-full h-24 object-cover rounded-lg border-2 border-slate-200" />
          <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
            <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <div class="absolute bottom-1 left-1 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            Uploading...
          </div>
        </div>
      `;

      previewsContainer.innerHTML += previewHtml;
      previewsContainer.classList.remove('hidden');
    };
    reader.readAsDataURL(file);
  }

  function updateImagePreview(previewId, imageUrl, success) {
    const previewElement = document.getElementById(previewId);
    if (!previewElement) return;

    if (success) {
      previewElement.innerHTML = `
        <img src="${imageUrl}" alt="Uploaded" class="w-full h-24 object-cover rounded-lg border-2 border-green-200" />
        <button
          type="button"
          onclick="removeUploadedImage('${previewId}')"
          class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs transition-colors flex items-center justify-center"
          title="Remove this image"
        >
          ×
        </button>
        <div class="absolute bottom-1 left-1 bg-green-500 text-white text-xs px-2 py-1 rounded">
          ✓ Uploaded
        </div>
      `;
    }
  }

  function removeImagePreview(previewId) {
    const previewElement = document.getElementById(previewId);
    if (previewElement) {
      previewElement.remove();

      // Hide container if no more previews
      const category = getCurrentCategory();
      const previewsContainer = document.getElementById(`${category}_image_previews`);
      if (previewsContainer && previewsContainer.children.length === 0) {
        previewsContainer.classList.add('hidden');
      }
    }
  }

  function getCurrentCategory() {
    return classifiedPost.category;
  }

  // Function to remove uploaded image
  window.removeUploadedImage = function(previewId) {
    const category = getCurrentCategory();
    const uploadedImages = window[`${category}_uploadedImages`] || [];

    // Find and remove the image from the array
    const imageIndex = uploadedImages.findIndex(img => img.previewId === previewId);
    if (imageIndex > -1) {
      uploadedImages.splice(imageIndex, 1);
      console.log(`Removed image from uploaded list: ${previewId}`);
    }

    // Remove the preview element
    removeImagePreview(previewId);

    showImageUploadToast('Image removed', 'success');
  }

  function showUploadStatus(category, message) {
    const statusElement = document.getElementById(`${category}_upload_status`);
    if (statusElement) {
      statusElement.innerHTML = `
        <div class="flex items-center space-x-2">
          <div class="w-4 h-4 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm text-emerald-700">${message}</span>
        </div>
      `;
      statusElement.classList.remove('hidden');
    }
  }

  function hideUploadStatus(category) {
    const statusElement = document.getElementById(`${category}_upload_status`);
    if (statusElement) {
      statusElement.classList.add('hidden');
    }
  }

  function showImageUploadProgress(category) {
    const imageInput = document.querySelector(`#${category}_image_upload`);
    if (!imageInput) {
      console.log(`Image input not found for category: ${category} - upload progress not shown`);
      return;
    }

    const uploadArea = imageInput.closest('.border-dashed');
    if (uploadArea) {
      // Store the original input element before replacing content
      const originalInput = uploadArea.querySelector(`#${category}_image_upload`);

      uploadArea.innerHTML = `
        <input type="file" id="${category}_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" multiple class="hidden" />
        <div class="space-y-2">
          <div class="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div class="text-sm font-medium text-blue-600">Uploading image...</div>
        </div>
      `;

      // Re-attach event listener to the new input
      const newInput = uploadArea.querySelector(`#${category}_image_upload`);
      if (newInput) {
        newInput.addEventListener('change', handleImageUpload);
        console.log(`Re-attached event listener for ${category}_image_upload`);
      }
    } else {
      console.log(`Upload area not found for category: ${category} - upload progress not shown`);
    }
  }

  function hideImageUploadProgress(category) {
    const imageInput = document.querySelector(`#${category}_image_upload`);
    if (!imageInput) {
      console.log(`Image input not found for category: ${category} - cannot hide progress`);
      return;
    }

    const uploadArea = imageInput.closest('.border-dashed');
    if (uploadArea) {
      uploadArea.innerHTML = `
        <input type="file" id="${category}_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" multiple class="hidden" />
        <label for="${category}_image_upload" class="cursor-pointer">
          <div class="space-y-2">
            <div class="text-4xl text-slate-400">📷</div>
            <div class="text-sm font-medium text-slate-700">Add photos</div>
            <div class="text-xs text-slate-500">JPEG, PNG, or WebP (max 2MB each)</div>
            <div class="text-xs text-slate-400 mt-2">Up to 3 photos total</div>
          </div>
        </label>
      `;
      // Re-attach event listener to the new input
      setTimeout(() => {
        setupImageUpload(category);
      }, 50);
    } else {
      console.log(`Upload area not found for category: ${category} - cannot hide progress`);
    }
  }

  function showImageUploadSuccess(category, imageUrl) {
    console.log(`showImageUploadSuccess called for category: ${category}, imageUrl: ${imageUrl}`);

    // Debug: Check what elements exist
    const allImageInputs = document.querySelectorAll('[id*="image_upload"]');
    console.log('All image upload inputs found:', Array.from(allImageInputs).map(el => el.id));

    // Find the upload area by looking for the border-dashed class in the current category form
    const categoryForm = document.querySelector(`#${category}-form`);
    const uploadArea = categoryForm ? categoryForm.querySelector('.border-dashed') : null;

    console.log(`Looking for upload area in #${category}-form:`, uploadArea);

    if (uploadArea) {
      uploadArea.innerHTML = `
        <input type="file" id="${category}_image_upload" accept="image/jpeg,image/jpg,image/png,image/webp" multiple class="hidden" />
        <label for="${category}_image_upload" class="cursor-pointer">
          <div class="space-y-2">
            <img src="${imageUrl}" alt="New image" class="w-24 h-18 object-cover rounded-lg mx-auto border-2 border-green-200">
            <div class="text-sm font-medium text-green-600">✓ Image uploaded successfully!</div>
            <div class="text-xs text-slate-500">Click to add more images (up to 3 total)</div>
          </div>
        </label>
      `;

      // Re-attach event listener to the new input
      const newInput = uploadArea.querySelector(`#${category}_image_upload`);
      if (newInput) {
        newInput.addEventListener('change', handleImageUpload);
        console.log(`Re-attached event listener for ${category}_image_upload in success`);
      }

      console.log('Upload area updated with success message');
    } else {
      console.log(`Upload area not found for category: ${category} - using fallback notification`);
      // Fallback: show a toast notification
      showImageUploadToast('✓ New image uploaded successfully!', 'success');
    }
  }

  function showImageError(message) {
    showImageUploadToast(message, 'error');
  }

  function showImageUploadToast(message, type = 'success') {
    // Create a temporary toast message
    const isError = type === 'error';
    const toastDiv = document.createElement('div');
    toastDiv.className = `fixed top-4 right-4 ${isError ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'} border rounded-lg p-4 z-50 max-w-sm animate-bounce-in`;
    toastDiv.innerHTML = `
      <div class="flex items-start space-x-2">
        <span class="${isError ? 'text-red-500' : 'text-green-500'} text-lg">${isError ? '⚠️' : '✅'}</span>
        <div class="text-sm ${isError ? 'text-red-800' : 'text-green-800'}">
          <p class="font-medium">${isError ? 'Upload Error' : 'Upload Success'}</p>
          <p>${message}</p>
        </div>
      </div>
    `;
    document.body.appendChild(toastDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (toastDiv.parentNode) {
        toastDiv.parentNode.removeChild(toastDiv);
      }
    }, 5000);
  }

  // Handle form submission
  document.addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target);
    const category = classifiedPost.category;

    // Collect category-specific data
    const categorySpecificData = {};
    if (category === 'housing') {
      categorySpecificData.listing_type = formData.get('listing_type');
      categorySpecificData.housing_type = formData.get('housing_type');
      categorySpecificData.bedrooms = formData.get('bedrooms');
      categorySpecificData.bathrooms = formData.get('bathrooms');
      categorySpecificData.furnished = formData.get('furnished');
    } else if (category === 'jobs') {
      categorySpecificData.job_listing_type = formData.get('job_listing_type');
      categorySpecificData.job_type = formData.get('job_type');
      categorySpecificData.job_category = formData.get('job_category');
      categorySpecificData.experience_level = formData.get('experience_level');
      categorySpecificData.remote_ok = formData.get('remote_ok');
      
      // Add CV information if uploaded or existing
      if (window.uploadedCVInfo) {
        categorySpecificData.cv_url = window.uploadedCVInfo.url;
        categorySpecificData.cv_filename = window.uploadedCVInfo.fileName;
        categorySpecificData.cv_size = window.uploadedCVInfo.fileSize;
      } else if (window.currentCVInfo && !window.cvRemoved) {
        categorySpecificData.cv_url = window.currentCVInfo.url;
        categorySpecificData.cv_filename = window.currentCVInfo.filename;
        categorySpecificData.cv_size = window.currentCVInfo.size;
      }
    } else if (category === 'buy-sell') {
      categorySpecificData.listing_type = formData.get('buy_sell_type');
      categorySpecificData.item_category = formData.get('item_category');
      categorySpecificData.condition = formData.get('condition');
      categorySpecificData.brand = formData.get('brand');
    } else if (category === 'community') {
      categorySpecificData.event_type = formData.get('event_type');
      categorySpecificData.event_date = formData.get('event_date');
      categorySpecificData.event_time = formData.get('event_time');
      categorySpecificData.event_location = formData.get('event_location');
    }

    // Collect uploaded images
    const uploadedImages = window[`${category}_uploadedImages`] || [];
    const existingImages = window[`${category}_existingImages`] || [];
    const removedImages = window[`${category}_removedImages`] || [];

    // Prepare image data for submission
    const allImages = [...existingImages, ...uploadedImages.map(img => img.url)];
    const imagesToRemove = removedImages;

    const data = {
      id: postId,
      title: formData.get('title'),
      description: formData.get('description'),
      price: formData.get('price') || null,
      price_local: formData.get('price_local') || null,
      price_usd: formData.get('price_usd') || null,
      contact_email: formData.get('contact_email'),
      contact_phone: formData.get('contact_phone'),
      contact_whatsapp: formData.get('contact_whatsapp'),
      preferred_contact_method: formData.get('preferred_contact_method'),
      category_specific_data: categorySpecificData,
      // Rate information for jobs
      rate_type: formData.get('rate_type') || null,
      rate_amount: formData.get('price_local') || formData.get('price_usd') || null,
      rate_currency: formData.get('price_local') ? 'MXN' : (formData.get('price_usd') ? 'USD' : null),
      // Handle multiple images
      image_urls: allImages,
      images_to_remove: imagesToRemove,
      // Legacy support
      new_image_url: allImages.length > 0 ? allImages[0] : null,
      remove_current_image: false
    };

    // Log image handling for debugging
    console.log('Multi-image handling:', {
      totalImages: allImages.length,
      newlyUploaded: uploadedImages.length,
      existing: existingImages.length,
      toRemove: imagesToRemove.length,
      allImageUrls: allImages
    });

    try {
      console.log('Sending update request with data:', data);

      const response = await fetch('/api/classifieds/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const result = await response.json();
      console.log('API response:', result);

      if (result.success) {
        // Create a more detailed success message
        let successMessage = 'Your classified ad has been updated successfully!';

        const totalImages = allImages.length;
        const newImages = uploadedImages.length;
        const removedCount = imagesToRemove.length;

        if (newImages > 0 && removedCount > 0) {
          successMessage += ` Added ${newImages} new image${newImages > 1 ? 's' : ''} and removed ${removedCount} image${removedCount > 1 ? 's' : ''}.`;
        } else if (newImages > 0) {
          successMessage += ` Added ${newImages} new image${newImages > 1 ? 's' : ''}.`;
        } else if (removedCount > 0) {
          successMessage += ` Removed ${removedCount} image${removedCount > 1 ? 's' : ''}.`;
        } else if (totalImages > 0) {
          successMessage += ` Your ${totalImages} image${totalImages > 1 ? 's' : ''} ${totalImages > 1 ? 'have' : 'has'} been preserved.`;
        }

        // Show success and redirect to account page
        showUpdateSuccessAndRedirect(successMessage, classifiedPost);
      } else {
        showUpdateError('Error updating ad: ' + (result.error || 'Please check your information and try again.'));
      }
    } catch (error) {
      console.error('Update error:', error);
      showUpdateError('Unable to update your ad right now. Please check your internet connection and try again.');
    }
  });

  // Modern inline feedback functions (no popups!)
  function showUpdateError(message) {
    clearUpdateFeedback();

    // Create inline error message at top of form
    const formContainer = document.querySelector('.max-w-2xl');
    const errorDiv = document.createElement('div');
    errorDiv.id = 'update-feedback';
    errorDiv.className = 'mb-6 bg-red-50 border border-red-200 rounded-lg p-4 animate-slide-down';
    errorDiv.innerHTML = `
      <div class="flex items-start space-x-3">
        <span class="text-red-500 text-xl flex-shrink-0">⚠️</span>
        <div class="flex-1">
          <h4 class="font-semibold text-red-900 mb-1">Update Failed</h4>
          <p class="text-red-800 text-sm">${message}</p>
          <button onclick="clearUpdateFeedback()" class="mt-2 text-red-600 hover:text-red-700 text-sm underline">
            Dismiss
          </button>
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(errorDiv, formContainer.firstChild);
      // Scroll to top to show error
      errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Auto-hide after 10 seconds
    setTimeout(clearUpdateFeedback, 10000);
  }

  function showUpdateSuccess(message, redirectUrl) {
    clearUpdateFeedback();

    // Create inline success message
    const formContainer = document.querySelector('.max-w-2xl');
    const successDiv = document.createElement('div');
    successDiv.id = 'update-feedback';
    successDiv.className = 'mb-6 bg-green-50 border border-green-200 rounded-lg p-4 animate-slide-down';

    const redirectContent = redirectUrl ? `
      <div class="flex items-center space-x-2 mt-3">
        <div class="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
        <span class="text-green-700 text-sm">Redirecting to your account...</span>
      </div>
    ` : `
      <div class="mt-3">
        <span class="text-green-700 text-sm">You can continue editing or go back to your account.</span>
      </div>
    `;

    successDiv.innerHTML = `
      <div class="flex items-start space-x-3">
        <span class="text-green-500 text-xl flex-shrink-0">✅</span>
        <div class="flex-1">
          <h4 class="font-semibold text-green-900 mb-1">Update Successful!</h4>
          <p class="text-green-800 text-sm">${message}</p>
          ${redirectContent}
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(successDiv, formContainer.firstChild);
      // Scroll to top to show success
      successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Only redirect if redirectUrl is provided
    if (redirectUrl) {
      setTimeout(() => {
        window.location.href = redirectUrl;
      }, 2000);
    } else {
      // Auto-hide success message after 8 seconds if staying on page
      setTimeout(clearUpdateFeedback, 8000);
    }
  }

  function showUpdateSuccessWithNavigation(message, post) {
    clearUpdateFeedback();

    // Create enhanced success message with navigation options
    const formContainer = document.querySelector('.max-w-2xl');
    const successDiv = document.createElement('div');
    successDiv.id = 'update-feedback';
    successDiv.className = 'mb-6 bg-green-50 border border-green-200 rounded-xl p-6 animate-slide-down shadow-lg';

    const citySlug = post.city_slug;
    const category = post.category;
    const postId = post.id;

    successDiv.innerHTML = `
      <div class="text-center">
        <!-- Success Icon -->
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span class="text-3xl">🎉</span>
        </div>

        <!-- Success Message -->
        <h3 class="text-xl font-bold text-green-900 mb-2">Update Successful!</h3>
        <p class="text-green-800 text-sm mb-6">${message}</p>

        <!-- Navigation Options -->
        <div class="space-y-4">
          <div class="text-sm font-medium text-slate-700 mb-3">What would you like to do next?</div>

          <!-- Primary Actions -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
            <button onclick="viewUpdatedListing('${citySlug}', '${category}', '${postId}')"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
              <span>👁️</span>
              <span>View Your Listing</span>
            </button>

            <button onclick="goToAccount()"
                    class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
              <span>📋</span>
              <span>My Account</span>
            </button>
          </div>

          <!-- Secondary Actions -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <button onclick="browseCategoryListings('${citySlug}', '${category}')"
                    class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
              Browse ${category.charAt(0).toUpperCase() + category.slice(1)}
            </button>

            <button onclick="createNewListing('${citySlug}')"
                    class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
              Post New Ad
            </button>

            <button onclick="clearUpdateFeedback()"
                    class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
              Continue Editing
            </button>
          </div>
        </div>

        <!-- Auto-hide notice -->
        <div class="mt-4 text-xs text-slate-500">
          This message will disappear in <span id="countdown">15</span> seconds
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(successDiv, formContainer.firstChild);
      successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Start countdown
    startCountdown(15);
  }

  function startCountdown(seconds) {
    const countdownElement = document.getElementById('countdown');
    let remaining = seconds;

    const timer = setInterval(() => {
      remaining--;
      if (countdownElement) {
        countdownElement.textContent = remaining;
      }

      if (remaining <= 0) {
        clearInterval(timer);
        clearUpdateFeedback();
      }
    }, 1000);
  }

  // Navigation functions
  window.viewUpdatedListing = function(citySlug, category, postId) {
    window.location.href = `/${citySlug}/classifieds/${category}/${postId}`;
  }

  window.goToAccount = function() {
    window.location.href = '/account';
  }

  window.browseCategoryListings = function(citySlug, category) {
    window.location.href = `/${citySlug}/classifieds/${category}`;
  }

  window.createNewListing = function(citySlug) {
    window.location.href = `/${citySlug}/classifieds/create`;
  }

  function addFormSavedFeedback() {
    // Add subtle green glow to form to indicate it's been saved
    const form = document.querySelector('form');
    if (form) {
      form.style.transition = 'box-shadow 0.3s ease';
      form.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.3)';

      // Remove the glow after 2 seconds
      setTimeout(() => {
        form.style.boxShadow = '';
      }, 2000);
    }

    // Add checkmark to submit button temporarily
    const submitButton = document.querySelector('button[type="submit"]');
    if (submitButton) {
      const originalText = submitButton.innerHTML;
      submitButton.innerHTML = '✅ Saved!';
      submitButton.style.backgroundColor = '#10b981';
      submitButton.disabled = true;

      // Reset button after 3 seconds
      setTimeout(() => {
        submitButton.innerHTML = originalText;
        submitButton.style.backgroundColor = '';
        submitButton.disabled = false;
      }, 3000);
    }
  }

  function showUpdateSuccessAndRedirect(message, post) {
    clearUpdateFeedback();

    // Add visual feedback to form first
    addFormSavedFeedback();

    // Create success message with redirect info
    const formContainer = document.querySelector('.max-w-2xl');
    const successDiv = document.createElement('div');
    successDiv.id = 'update-feedback';
    successDiv.className = 'mb-6 bg-green-50 border border-green-200 rounded-xl p-6 animate-slide-down shadow-lg';

    successDiv.innerHTML = `
      <div class="text-center">
        <!-- Success Icon -->
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span class="text-3xl">🎉</span>
        </div>

        <!-- Success Message -->
        <h3 class="text-xl font-bold text-green-900 mb-2">Update Successful!</h3>
        <p class="text-green-800 text-sm mb-4">${message}</p>

        <!-- Redirect Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div class="flex items-center justify-center space-x-2 mb-2">
            <div class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-blue-700 font-medium">Taking you to your account...</span>
          </div>
          <p class="text-blue-600 text-sm">You'll see your updated classified in your listings</p>
        </div>

        <!-- Quick Actions -->
        <div class="flex justify-center space-x-3">
          <button onclick="goToAccountNow()"
                  class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Go Now
          </button>
          <button onclick="stayOnPage()"
                  class="bg-slate-200 hover:bg-slate-300 text-slate-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Stay Here
          </button>
        </div>

        <!-- Countdown -->
        <div class="mt-3 text-xs text-slate-500">
          Redirecting in <span id="redirect-countdown">3</span> seconds
        </div>
      </div>
    `;

    if (formContainer) {
      formContainer.insertBefore(successDiv, formContainer.firstChild);
      successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Start redirect countdown
    startRedirectCountdown(3);
  }

  function startRedirectCountdown(seconds) {
    const countdownElement = document.getElementById('redirect-countdown');
    let remaining = seconds;

    const timer = setInterval(() => {
      remaining--;
      if (countdownElement) {
        countdownElement.textContent = remaining;
      }

      if (remaining <= 0) {
        clearInterval(timer);
        goToAccountNow();
      }
    }, 1000);

    // Store timer ID so we can cancel it if user chooses to stay
    window.redirectTimer = timer;
  }

  window.goToAccountNow = function() {
    // Cancel any existing timer
    if (window.redirectTimer) {
      clearInterval(window.redirectTimer);
    }

    // Redirect to account page with special parameter to show classifieds tab
    window.location.href = '/account?tab=classifieds';
  }

  window.stayOnPage = function() {
    // Cancel the redirect timer
    if (window.redirectTimer) {
      clearInterval(window.redirectTimer);
    }

    // Clear the success message
    clearUpdateFeedback();

    // Show a small confirmation
    showImageUploadToast('Staying on edit page', 'success');
  }

  function clearUpdateFeedback() {
    const feedbackDiv = document.getElementById('update-feedback');
    if (feedbackDiv) {
      feedbackDiv.style.opacity = '0';
      feedbackDiv.style.transform = 'translateY(-10px)';
      setTimeout(() => feedbackDiv.remove(), 300);
    }
  }

  // Rate field toggle function
  window.toggleRateFields = function() {
    const rateType = document.getElementById('rate_type').value;
    const rateSection = document.getElementById('rate_amount_section');
    
    if (rateType && rateType !== 'negotiable') {
      rateSection.style.display = 'block';
    } else {
      rateSection.style.display = 'none';
    }
  };

  // CV handling functions
  window.removeCurrentCV = function() {
    window.cvRemoved = true;
    document.getElementById('current_cv_display').style.display = 'none';
    document.getElementById('cv_upload').style.display = 'block';
  };

  // CV upload handler and initialization
  document.addEventListener('DOMContentLoaded', function() {
    const cvUpload = document.getElementById('cv_upload');
    if (cvUpload) {
      cvUpload.addEventListener('change', handleCVUpload);
    }

    // Initialize existing CV data
    const categoryData = classifiedPost.category_specific_data;
    if (categoryData && categoryData.cv_url) {
      window.currentCVInfo = {
        url: categoryData.cv_url,
        filename: categoryData.cv_filename || 'CV/Portfolio.pdf',
        size: categoryData.cv_size || 0
      };

      // Show current CV
      const currentCVDisplay = document.getElementById('current_cv_display');
      const currentCVName = document.getElementById('current_cv_name');
      const currentCVSize = document.getElementById('current_cv_size');
      
      if (currentCVDisplay && currentCVName && currentCVSize) {
        currentCVName.textContent = window.currentCVInfo.filename;
        currentCVSize.textContent = window.currentCVInfo.size ? 
          `${(window.currentCVInfo.size / 1024 / 1024).toFixed(2)} MB` : '';
        currentCVDisplay.style.display = 'block';
      }
    }

    // Initialize rate fields
    const rateType = document.getElementById('rate_type');
    if (rateType && rateType.value) {
      toggleRateFields();
    }
  });

  async function handleCVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a PDF, DOC, or DOCX file.');
      event.target.value = '';
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB.');
      event.target.value = '';
      return;
    }

    // Show uploading state
    const preview = document.getElementById('cv_preview');
    preview.innerHTML = `
      <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center space-x-3">
          <div class="text-2xl animate-spin">⏳</div>
          <div>
            <div class="font-medium text-slate-900">Uploading ${file.name}...</div>
            <div class="text-sm text-blue-600">Please wait</div>
          </div>
        </div>
      </div>
    `;
    preview.classList.remove('hidden');

    try {
      // Upload CV file
      const formData = new FormData();
      formData.append('cv', file);
      formData.append('userId', 'user-id'); // This should be the actual user ID

      const response = await fetch('/api/classifieds/upload-cv', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Store CV info globally for form submission
        window.uploadedCVInfo = {
          url: result.cvUrl,
          fileName: result.originalName,
          fileSize: result.fileSize
        };

        // Show success state
        preview.innerHTML = `
          <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
            <div class="flex items-center space-x-3">
              <div class="text-2xl">📄</div>
              <div>
                <div class="font-medium text-slate-900">${result.originalName}</div>
                <div class="text-sm text-green-600">✓ Uploaded successfully - ${(result.fileSize / 1024 / 1024).toFixed(2)} MB</div>
              </div>
            </div>
            <button type="button" onclick="removeUploadedCV()" class="text-red-600 hover:text-red-800 font-medium text-sm">
              Remove
            </button>
          </div>
        `;
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      // Show error state
      preview.innerHTML = `
        <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
          <div class="flex items-center space-x-3">
            <div class="text-2xl">❌</div>
            <div>
              <div class="font-medium text-slate-900">Upload failed</div>
              <div class="text-sm text-red-600">${error.message}</div>
            </div>
          </div>
          <button type="button" onclick="clearCVPreview()" class="text-red-600 hover:text-red-800 font-medium text-sm">
            Remove
          </button>
        </div>
      `;
      
      // Clear the input
      event.target.value = '';
    }
  }

  window.removeUploadedCV = function() {
    window.uploadedCVInfo = null;
    document.getElementById('cv_upload').value = '';
    document.getElementById('cv_preview').classList.add('hidden');
  };

  window.clearCVPreview = function() {
    document.getElementById('cv_upload').value = '';
    document.getElementById('cv_preview').classList.add('hidden');
  };
</script>

<style>
  @keyframes slide-down {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-down {
    animation: slide-down 0.4s ease-out;
  }
</style>
